
> Configure project :app
app: 'annotationProcessor' dependencies won't be recognized as kapt annotation processors. Please change the configuration name to 'kapt' for these artifacts: 'androidx.room:room-compiler:2.5.0'.

> Task :app:dependencies

------------------------------------------------------------
Project ':app'
------------------------------------------------------------

prodDebugRuntimeClasspath - Runtime classpath of /prodDebug.
+--- androidx.databinding:viewbinding:8.1.4
|    \--- androidx.annotation:annotation:1.0.0 -> 1.3.0
+--- androidx.databinding:databinding-common:8.1.4
+--- androidx.databinding:databinding-runtime:8.1.4
|    +--- androidx.collection:collection:1.0.0 -> 1.1.0
|    |    \--- androidx.annotation:annotation:1.1.0 -> 1.3.0
|    +--- androidx.databinding:databinding-common:8.1.4
|    +--- androidx.databinding:viewbinding:8.1.4 (*)
|    \--- androidx.lifecycle:lifecycle-runtime:2.6.1
|         +--- androidx.annotation:annotation:1.1.0 -> 1.3.0
|         +--- androidx.arch.core:core-common:2.2.0
|         |    \--- androidx.annotation:annotation:1.1.0 -> 1.3.0
|         +--- androidx.arch.core:core-runtime:2.2.0
|         |    +--- androidx.annotation:annotation:1.1.0 -> 1.3.0
|         |    \--- androidx.arch.core:core-common:2.2.0 (*)
|         +--- androidx.lifecycle:lifecycle-common:2.6.1
|         |    +--- androidx.annotation:annotation:1.1.0 -> 1.3.0
|         |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.10 -> 1.9.24
|         |    |    +--- org.jetbrains:annotations:13.0
|         |    |    +--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.0 -> 1.8.20 (c)
|         |    |    +--- org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.0 -> 1.8.20 (c)
|         |    |    \--- org.jetbrains.kotlin:kotlin-stdlib-common:1.9.24 (c)
|         |    +--- org.jetbrains.kotlinx:kotlinx-coroutines-android:1.6.4
|         |    |    +--- org.jetbrains.kotlinx:kotlinx-coroutines-core:1.6.4
|         |    |    |    \--- org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.6.4
|         |    |    |         +--- org.jetbrains.kotlinx:kotlinx-coroutines-bom:1.6.4
|         |    |    |         |    +--- org.jetbrains.kotlinx:kotlinx-coroutines-android:1.6.4 (c)
|         |    |    |         |    +--- org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.6.4 (c)
|         |    |    |         |    +--- org.jetbrains.kotlinx:kotlinx-coroutines-core:1.6.4 (c)
|         |    |    |         |    \--- org.jetbrains.kotlinx:kotlinx-coroutines-play-services:1.6.4 (c)
|         |    |    |         +--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.6.21 -> 1.8.20
|         |    |    |         |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.20 -> 1.9.24 (*)
|         |    |    |         |    \--- org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.20
|         |    |    |         |         \--- org.jetbrains.kotlin:kotlin-stdlib:1.8.20 -> 1.9.24 (*)
|         |    |    |         \--- org.jetbrains.kotlin:kotlin-stdlib-common:1.6.21 -> 1.9.24
|         |    |    |              \--- org.jetbrains.kotlin:kotlin-stdlib:1.9.24 (*)
|         |    |    +--- org.jetbrains.kotlinx:kotlinx-coroutines-bom:1.6.4 (*)
|         |    |    \--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.6.21 -> 1.8.20 (*)
|         |    +--- androidx.lifecycle:lifecycle-livedata:2.6.1 (c)
|         |    +--- androidx.lifecycle:lifecycle-livedata-core:2.6.1 (c)
|         |    +--- androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.1 (c)
|         |    +--- androidx.lifecycle:lifecycle-livedata-ktx:2.6.1 (c)
|         |    +--- androidx.lifecycle:lifecycle-process:2.6.1 (c)
|         |    +--- androidx.lifecycle:lifecycle-runtime:2.6.1 (c)
|         |    +--- androidx.lifecycle:lifecycle-runtime-ktx:2.6.1 (c)
|         |    +--- androidx.lifecycle:lifecycle-service:2.6.1 (c)
|         |    +--- androidx.lifecycle:lifecycle-viewmodel:2.6.1 (c)
|         |    +--- androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.1 (c)
|         |    \--- androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.1 (c)
|         +--- androidx.profileinstaller:profileinstaller:1.3.0
|         |    +--- androidx.annotation:annotation:1.2.0 -> 1.3.0
|         |    +--- androidx.concurrent:concurrent-futures:1.1.0
|         |    |    +--- androidx.annotation:annotation:1.1.0 -> 1.3.0
|         |    |    \--- com.google.guava:listenablefuture:1.0 -> 9999.0-empty-to-avoid-conflict-with-guava
|         |    +--- androidx.startup:startup-runtime:1.1.1
|         |    |    +--- androidx.annotation:annotation:1.1.0 -> 1.3.0
|         |    |    \--- androidx.tracing:tracing:1.0.0
|         |    |         \--- androidx.annotation:annotation:1.1.0 -> 1.3.0
|         |    \--- com.google.guava:listenablefuture:1.0 -> 9999.0-empty-to-avoid-conflict-with-guava
|         +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.10 -> 1.9.24 (*)
|         +--- androidx.lifecycle:lifecycle-common:2.6.1 (c)
|         +--- androidx.lifecycle:lifecycle-livedata:2.6.1 (c)
|         +--- androidx.lifecycle:lifecycle-livedata-core:2.6.1 (c)
|         +--- androidx.lifecycle:lifecycle-livedata-ktx:2.6.1 (c)
|         +--- androidx.lifecycle:lifecycle-process:2.6.1 (c)
|         +--- androidx.lifecycle:lifecycle-runtime-ktx:2.6.1 (c)
|         +--- androidx.lifecycle:lifecycle-service:2.6.1 (c)
|         +--- androidx.lifecycle:lifecycle-viewmodel:2.6.1 (c)
|         +--- androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.1 (c)
|         +--- androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.1 (c)
|         \--- androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.1 (c)
+--- androidx.databinding:databinding-adapters:8.1.4
|    +--- androidx.databinding:databinding-runtime:8.1.4 (*)
|    \--- androidx.databinding:databinding-common:8.1.4
+--- androidx.databinding:databinding-ktx:8.1.4
|    +--- androidx.databinding:databinding-runtime:8.1.4 (*)
|    +--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.20-RC2 -> 1.8.20 (*)
|    +--- org.jetbrains.kotlinx:kotlinx-coroutines-android:1.4.1 -> 1.6.4 (*)
|    +--- androidx.lifecycle:lifecycle-runtime-ktx:2.6.1
|    |    +--- androidx.annotation:annotation:1.0.0 -> 1.3.0
|    |    +--- androidx.lifecycle:lifecycle-runtime:2.6.1 (*)
|    |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.10 -> 1.9.24 (*)
|    |    +--- org.jetbrains.kotlinx:kotlinx-coroutines-android:1.6.4 (*)
|    |    +--- androidx.lifecycle:lifecycle-common:2.6.1 (c)
|    |    +--- androidx.lifecycle:lifecycle-livedata:2.6.1 (c)
|    |    +--- androidx.lifecycle:lifecycle-livedata-core:2.6.1 (c)
|    |    +--- androidx.lifecycle:lifecycle-livedata-ktx:2.6.1 (c)
|    |    +--- androidx.lifecycle:lifecycle-process:2.6.1 (c)
|    |    +--- androidx.lifecycle:lifecycle-runtime:2.6.1 (c)
|    |    +--- androidx.lifecycle:lifecycle-service:2.6.1 (c)
|    |    +--- androidx.lifecycle:lifecycle-viewmodel:2.6.1 (c)
|    |    +--- androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.1 (c)
|    |    +--- androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.1 (c)
|    |    \--- androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.1 (c)
|    +--- androidx.lifecycle:lifecycle-livedata:2.6.1
|    |    +--- androidx.arch.core:core-common:2.1.0 -> 2.2.0 (*)
|    |    +--- androidx.arch.core:core-runtime:2.1.0 -> 2.2.0 (*)
|    |    +--- androidx.lifecycle:lifecycle-livedata-core:2.6.1
|    |    |    +--- androidx.arch.core:core-common:2.1.0 -> 2.2.0 (*)
|    |    |    +--- androidx.arch.core:core-runtime:2.1.0 -> 2.2.0 (*)
|    |    |    +--- androidx.lifecycle:lifecycle-common:2.6.1 (*)
|    |    |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.10 -> 1.9.24 (*)
|    |    |    +--- androidx.lifecycle:lifecycle-common:2.6.1 (c)
|    |    |    +--- androidx.lifecycle:lifecycle-livedata:2.6.1 (c)
|    |    |    +--- androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.1 (c)
|    |    |    +--- androidx.lifecycle:lifecycle-livedata-ktx:2.6.1 (c)
|    |    |    +--- androidx.lifecycle:lifecycle-process:2.6.1 (c)
|    |    |    +--- androidx.lifecycle:lifecycle-runtime:2.6.1 (c)
|    |    |    +--- androidx.lifecycle:lifecycle-runtime-ktx:2.6.1 (c)
|    |    |    +--- androidx.lifecycle:lifecycle-service:2.6.1 (c)
|    |    |    +--- androidx.lifecycle:lifecycle-viewmodel:2.6.1 (c)
|    |    |    +--- androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.1 (c)
|    |    |    \--- androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.1 (c)
|    |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.10 -> 1.9.24 (*)
|    |    +--- androidx.lifecycle:lifecycle-common:2.6.1 (c)
|    |    +--- androidx.lifecycle:lifecycle-livedata-core:2.6.1 (c)
|    |    +--- androidx.lifecycle:lifecycle-livedata-ktx:2.6.1 (c)
|    |    +--- androidx.lifecycle:lifecycle-process:2.6.1 (c)
|    |    +--- androidx.lifecycle:lifecycle-runtime:2.6.1 (c)
|    |    +--- androidx.lifecycle:lifecycle-runtime-ktx:2.6.1 (c)
|    |    +--- androidx.lifecycle:lifecycle-service:2.6.1 (c)
|    |    +--- androidx.lifecycle:lifecycle-viewmodel:2.6.1 (c)
|    |    +--- androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.1 (c)
|    |    +--- androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.1 (c)
|    |    \--- androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.1 (c)
|    +--- androidx.lifecycle:lifecycle-process:2.6.1
|    |    +--- androidx.annotation:annotation:1.2.0 -> 1.3.0
|    |    +--- androidx.lifecycle:lifecycle-runtime:2.6.1 (*)
|    |    +--- androidx.startup:startup-runtime:1.1.1 (*)
|    |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.10 -> 1.9.24 (*)
|    |    +--- androidx.lifecycle:lifecycle-common:2.6.1 (c)
|    |    +--- androidx.lifecycle:lifecycle-livedata:2.6.1 (c)
|    |    +--- androidx.lifecycle:lifecycle-livedata-core:2.6.1 (c)
|    |    +--- androidx.lifecycle:lifecycle-livedata-ktx:2.6.1 (c)
|    |    +--- androidx.lifecycle:lifecycle-runtime:2.6.1 (c)
|    |    +--- androidx.lifecycle:lifecycle-runtime-ktx:2.6.1 (c)
|    |    +--- androidx.lifecycle:lifecycle-service:2.6.1 (c)
|    |    +--- androidx.lifecycle:lifecycle-viewmodel:2.6.1 (c)
|    |    +--- androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.1 (c)
|    |    +--- androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.1 (c)
|    |    \--- androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.1 (c)
|    +--- androidx.lifecycle:lifecycle-service:2.6.1
|    |    +--- androidx.lifecycle:lifecycle-runtime:2.6.1 (*)
|    |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.10 -> 1.9.24 (*)
|    |    +--- androidx.lifecycle:lifecycle-common:2.6.1 (c)
|    |    +--- androidx.lifecycle:lifecycle-livedata:2.6.1 (c)
|    |    +--- androidx.lifecycle:lifecycle-livedata-core:2.6.1 (c)
|    |    +--- androidx.lifecycle:lifecycle-livedata-ktx:2.6.1 (c)
|    |    +--- androidx.lifecycle:lifecycle-process:2.6.1 (c)
|    |    +--- androidx.lifecycle:lifecycle-runtime:2.6.1 (c)
|    |    +--- androidx.lifecycle:lifecycle-runtime-ktx:2.6.1 (c)
|    |    +--- androidx.lifecycle:lifecycle-viewmodel:2.6.1 (c)
|    |    +--- androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.1 (c)
|    |    +--- androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.1 (c)
|    |    \--- androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.1 (c)
|    \--- androidx.lifecycle:lifecycle-viewmodel:2.6.1
|         +--- androidx.annotation:annotation:1.1.0 -> 1.3.0
|         +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.10 -> 1.9.24 (*)
|         +--- androidx.lifecycle:lifecycle-common:2.6.1 (c)
|         +--- androidx.lifecycle:lifecycle-livedata:2.6.1 (c)
|         +--- androidx.lifecycle:lifecycle-livedata-core:2.6.1 (c)
|         +--- androidx.lifecycle:lifecycle-livedata-ktx:2.6.1 (c)
|         +--- androidx.lifecycle:lifecycle-process:2.6.1 (c)
|         +--- androidx.lifecycle:lifecycle-runtime:2.6.1 (c)
|         +--- androidx.lifecycle:lifecycle-runtime-ktx:2.6.1 (c)
|         +--- androidx.lifecycle:lifecycle-service:2.6.1 (c)
|         +--- androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.1 (c)
|         +--- androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.1 (c)
|         \--- androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.1 (c)
+--- org.jetbrains.kotlin:kotlin-stdlib:1.9.24 (*)
+--- project :base-android
|    +--- androidx.databinding:databinding-common:8.1.4
|    +--- androidx.databinding:databinding-runtime:8.1.4 (*)
|    +--- androidx.databinding:databinding-adapters:8.1.4 (*)
|    +--- androidx.databinding:databinding-ktx:8.1.4 (*)
|    +--- org.jetbrains.kotlin:kotlin-stdlib:1.9.24 (*)
|    +--- org.jetbrains.kotlin:kotlin-parcelize-runtime:1.9.24
|    |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.9.24 (*)
|    |    \--- org.jetbrains.kotlin:kotlin-android-extensions-runtime:1.9.24
|    |         \--- org.jetbrains.kotlin:kotlin-stdlib:1.9.24 (*)
|    +--- androidx.appcompat:appcompat:1.4.2 -> 1.6.1
|    |    +--- androidx.activity:activity:1.6.0
|    |    |    +--- androidx.annotation:annotation:1.1.0 -> 1.3.0
|    |    |    +--- androidx.collection:collection:1.0.0 -> 1.1.0 (*)
|    |    |    +--- androidx.core:core:1.8.0
|    |    |    |    +--- androidx.annotation:annotation:1.2.0 -> 1.3.0
|    |    |    |    +--- androidx.annotation:annotation-experimental:1.1.0
|    |    |    |    +--- androidx.collection:collection:1.0.0 -> 1.1.0 (*)
|    |    |    |    +--- androidx.concurrent:concurrent-futures:1.0.0 -> 1.1.0 (*)
|    |    |    |    +--- androidx.lifecycle:lifecycle-runtime:2.3.1 -> 2.6.1 (*)
|    |    |    |    \--- androidx.versionedparcelable:versionedparcelable:1.1.1
|    |    |    |         +--- androidx.annotation:annotation:1.1.0 -> 1.3.0
|    |    |    |         \--- androidx.collection:collection:1.0.0 -> 1.1.0 (*)
|    |    |    +--- androidx.lifecycle:lifecycle-runtime:2.5.1 -> 2.6.1 (*)
|    |    |    +--- androidx.lifecycle:lifecycle-viewmodel:2.5.1 -> 2.6.1 (*)
|    |    |    +--- androidx.lifecycle:lifecycle-viewmodel-savedstate:2.5.1 -> 2.6.1
|    |    |    |    +--- androidx.annotation:annotation:1.0.0 -> 1.3.0
|    |    |    |    +--- androidx.core:core-ktx:1.2.0 -> 1.8.0
|    |    |    |    |    +--- androidx.annotation:annotation:1.1.0 -> 1.3.0
|    |    |    |    |    +--- androidx.core:core:1.8.0 (*)
|    |    |    |    |    \--- org.jetbrains.kotlin:kotlin-stdlib:1.6.21 -> 1.9.24 (*)
|    |    |    |    +--- androidx.lifecycle:lifecycle-livedata-core:2.6.1 (*)
|    |    |    |    +--- androidx.lifecycle:lifecycle-viewmodel:2.6.1 (*)
|    |    |    |    +--- androidx.savedstate:savedstate:1.2.1
|    |    |    |    |    +--- androidx.annotation:annotation:1.1.0 -> 1.3.0
|    |    |    |    |    +--- androidx.arch.core:core-common:2.1.0 -> 2.2.0 (*)
|    |    |    |    |    +--- androidx.lifecycle:lifecycle-common:2.6.1 (*)
|    |    |    |    |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.10 -> 1.9.24 (*)
|    |    |    |    |    \--- androidx.savedstate:savedstate-ktx:1.2.1 (c)
|    |    |    |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.10 -> 1.9.24 (*)
|    |    |    |    +--- org.jetbrains.kotlinx:kotlinx-coroutines-android:1.6.4 (*)
|    |    |    |    +--- androidx.lifecycle:lifecycle-common:2.6.1 (c)
|    |    |    |    +--- androidx.lifecycle:lifecycle-livedata:2.6.1 (c)
|    |    |    |    +--- androidx.lifecycle:lifecycle-livedata-core:2.6.1 (c)
|    |    |    |    +--- androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.1 (c)
|    |    |    |    +--- androidx.lifecycle:lifecycle-livedata-ktx:2.6.1 (c)
|    |    |    |    +--- androidx.lifecycle:lifecycle-process:2.6.1 (c)
|    |    |    |    +--- androidx.lifecycle:lifecycle-runtime:2.6.1 (c)
|    |    |    |    +--- androidx.lifecycle:lifecycle-runtime-ktx:2.6.1 (c)
|    |    |    |    +--- androidx.lifecycle:lifecycle-service:2.6.1 (c)
|    |    |    |    +--- androidx.lifecycle:lifecycle-viewmodel:2.6.1 (c)
|    |    |    |    \--- androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.1 (c)
|    |    |    +--- androidx.savedstate:savedstate:1.2.0 -> 1.2.1 (*)
|    |    |    +--- androidx.tracing:tracing:1.0.0 (*)
|    |    |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.7.10 -> 1.9.24 (*)
|    |    |    \--- androidx.activity:activity-ktx:1.6.0 (c)
|    |    +--- androidx.annotation:annotation:1.3.0
|    |    +--- androidx.appcompat:appcompat-resources:1.6.1
|    |    |    +--- androidx.annotation:annotation:1.2.0 -> 1.3.0
|    |    |    +--- androidx.collection:collection:1.0.0 -> 1.1.0 (*)
|    |    |    +--- androidx.core:core:1.6.0 -> 1.8.0 (*)
|    |    |    +--- androidx.vectordrawable:vectordrawable:1.1.0
|    |    |    |    +--- androidx.annotation:annotation:1.1.0 -> 1.3.0
|    |    |    |    +--- androidx.core:core:1.1.0 -> 1.8.0 (*)
|    |    |    |    \--- androidx.collection:collection:1.1.0 (*)
|    |    |    +--- androidx.vectordrawable:vectordrawable-animated:1.1.0
|    |    |    |    +--- androidx.vectordrawable:vectordrawable:1.1.0 (*)
|    |    |    |    +--- androidx.interpolator:interpolator:1.0.0
|    |    |    |    |    \--- androidx.annotation:annotation:1.0.0 -> 1.3.0
|    |    |    |    \--- androidx.collection:collection:1.1.0 (*)
|    |    |    \--- androidx.appcompat:appcompat:1.6.1 (c)
|    |    +--- androidx.collection:collection:1.0.0 -> 1.1.0 (*)
|    |    +--- androidx.core:core:1.9.0 -> 1.8.0 (*)
|    |    +--- androidx.core:core-ktx:1.8.0 (*)
|    |    +--- androidx.cursoradapter:cursoradapter:1.0.0
|    |    |    \--- androidx.annotation:annotation:1.0.0 -> 1.3.0
|    |    +--- androidx.drawerlayout:drawerlayout:1.0.0 -> 1.1.1
|    |    |    +--- androidx.annotation:annotation:1.1.0 -> 1.3.0
|    |    |    +--- androidx.core:core:1.2.0 -> 1.8.0 (*)
|    |    |    \--- androidx.customview:customview:1.1.0
|    |    |         +--- androidx.annotation:annotation:1.1.0 -> 1.3.0
|    |    |         +--- androidx.core:core:1.3.0 -> 1.8.0 (*)
|    |    |         \--- androidx.collection:collection:1.1.0 (*)
|    |    +--- androidx.emoji2:emoji2:1.2.0
|    |    |    +--- androidx.annotation:annotation:1.2.0 -> 1.3.0
|    |    |    +--- androidx.collection:collection:1.1.0 (*)
|    |    |    +--- androidx.core:core:1.3.0 -> 1.8.0 (*)
|    |    |    +--- androidx.lifecycle:lifecycle-process:2.4.1 -> 2.6.1 (*)
|    |    |    \--- androidx.startup:startup-runtime:1.0.0 -> 1.1.1 (*)
|    |    +--- androidx.emoji2:emoji2-views-helper:1.2.0
|    |    |    +--- androidx.collection:collection:1.1.0 (*)
|    |    |    +--- androidx.core:core:1.3.0 -> 1.8.0 (*)
|    |    |    \--- androidx.emoji2:emoji2:1.2.0 (*)
|    |    +--- androidx.fragment:fragment:1.3.6 -> 1.5.7
|    |    |    +--- androidx.activity:activity:1.5.1 -> 1.6.0 (*)
|    |    |    +--- androidx.annotation:annotation:1.1.0 -> 1.3.0
|    |    |    +--- androidx.annotation:annotation-experimental:1.0.0 -> 1.1.0
|    |    |    +--- androidx.collection:collection:1.1.0 (*)
|    |    |    +--- androidx.core:core-ktx:1.2.0 -> 1.8.0 (*)
|    |    |    +--- androidx.lifecycle:lifecycle-livedata-core:2.5.1 -> 2.6.1 (*)
|    |    |    +--- androidx.lifecycle:lifecycle-viewmodel:2.5.1 -> 2.6.1 (*)
|    |    |    +--- androidx.lifecycle:lifecycle-viewmodel-savedstate:2.5.1 -> 2.6.1 (*)
|    |    |    +--- androidx.loader:loader:1.0.0
|    |    |    |    +--- androidx.annotation:annotation:1.0.0 -> 1.3.0
|    |    |    |    +--- androidx.core:core:1.0.0 -> 1.8.0 (*)
|    |    |    |    +--- androidx.lifecycle:lifecycle-livedata:2.0.0 -> 2.6.1 (*)
|    |    |    |    \--- androidx.lifecycle:lifecycle-viewmodel:2.0.0 -> 2.6.1 (*)
|    |    |    +--- androidx.savedstate:savedstate:1.2.0 -> 1.2.1 (*)
|    |    |    +--- androidx.viewpager:viewpager:1.0.0
|    |    |    |    +--- androidx.annotation:annotation:1.0.0 -> 1.3.0
|    |    |    |    +--- androidx.core:core:1.0.0 -> 1.8.0 (*)
|    |    |    |    \--- androidx.customview:customview:1.0.0 -> 1.1.0 (*)
|    |    |    \--- org.jetbrains.kotlin:kotlin-stdlib:1.6.21 -> 1.9.24 (*)
|    |    +--- androidx.lifecycle:lifecycle-runtime:2.5.1 -> 2.6.1 (*)
|    |    +--- androidx.lifecycle:lifecycle-viewmodel:2.5.1 -> 2.6.1 (*)
|    |    +--- androidx.resourceinspection:resourceinspection-annotation:1.0.1
|    |    |    \--- androidx.annotation:annotation:1.1.0 -> 1.3.0
|    |    +--- androidx.savedstate:savedstate:1.2.0 -> 1.2.1 (*)
|    |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.7.10 -> 1.9.24 (*)
|    |    \--- androidx.appcompat:appcompat-resources:1.6.1 (c)
|    +--- androidx.core:core-ktx:1.6.0 -> 1.8.0 (*)
|    +--- androidx.recyclerview:recyclerview:1.0.0 -> 1.3.0
|    |    +--- androidx.annotation:annotation:1.1.0 -> 1.3.0
|    |    +--- androidx.collection:collection:1.0.0 -> 1.1.0 (*)
|    |    +--- androidx.core:core:1.7.0 -> 1.8.0 (*)
|    |    +--- androidx.customview:customview:1.0.0 -> 1.1.0 (*)
|    |    \--- androidx.customview:customview-poolingcontainer:1.0.0
|    |         +--- androidx.core:core-ktx:1.5.0 -> 1.8.0 (*)
|    |         \--- org.jetbrains.kotlin:kotlin-stdlib:1.6.21 -> 1.9.24 (*)
|    +--- com.google.android.material:material:1.9.0
|    |    +--- com.google.errorprone:error_prone_annotations:2.15.0
|    |    +--- androidx.annotation:annotation:1.2.0 -> 1.3.0
|    |    +--- androidx.appcompat:appcompat:1.5.0 -> 1.6.1 (*)
|    |    +--- androidx.cardview:cardview:1.0.0
|    |    |    \--- androidx.annotation:annotation:1.0.0 -> 1.3.0
|    |    +--- androidx.coordinatorlayout:coordinatorlayout:1.1.0
|    |    |    +--- androidx.annotation:annotation:1.1.0 -> 1.3.0
|    |    |    +--- androidx.core:core:1.1.0 -> 1.8.0 (*)
|    |    |    +--- androidx.customview:customview:1.0.0 -> 1.1.0 (*)
|    |    |    \--- androidx.collection:collection:1.0.0 -> 1.1.0 (*)
|    |    +--- androidx.constraintlayout:constraintlayout:2.0.1 -> 2.1.4
|    |    |    +--- androidx.appcompat:appcompat:1.2.0 -> 1.6.1 (*)
|    |    |    +--- androidx.core:core:1.3.2 -> 1.8.0 (*)
|    |    |    \--- androidx.constraintlayout:constraintlayout-core:1.0.4
|    |    +--- androidx.core:core:1.6.0 -> 1.8.0 (*)
|    |    +--- androidx.drawerlayout:drawerlayout:1.1.1 (*)
|    |    +--- androidx.dynamicanimation:dynamicanimation:1.0.0
|    |    |    +--- androidx.core:core:1.0.0 -> 1.8.0 (*)
|    |    |    +--- androidx.collection:collection:1.0.0 -> 1.1.0 (*)
|    |    |    \--- androidx.legacy:legacy-support-core-utils:1.0.0
|    |    |         +--- androidx.annotation:annotation:1.0.0 -> 1.3.0
|    |    |         +--- androidx.core:core:1.0.0 -> 1.8.0 (*)
|    |    |         +--- androidx.documentfile:documentfile:1.0.0
|    |    |         |    \--- androidx.annotation:annotation:1.0.0 -> 1.3.0
|    |    |         +--- androidx.loader:loader:1.0.0 (*)
|    |    |         +--- androidx.localbroadcastmanager:localbroadcastmanager:1.0.0
|    |    |         |    \--- androidx.annotation:annotation:1.0.0 -> 1.3.0
|    |    |         \--- androidx.print:print:1.0.0
|    |    |              \--- androidx.annotation:annotation:1.0.0 -> 1.3.0
|    |    +--- androidx.annotation:annotation-experimental:1.0.0 -> 1.1.0
|    |    +--- androidx.fragment:fragment:1.2.5 -> 1.5.7 (*)
|    |    +--- androidx.lifecycle:lifecycle-runtime:2.0.0 -> 2.6.1 (*)
|    |    +--- androidx.recyclerview:recyclerview:1.0.0 -> 1.3.0 (*)
|    |    +--- androidx.transition:transition:1.2.0 -> 1.4.1
|    |    |    +--- androidx.annotation:annotation:1.1.0 -> 1.3.0
|    |    |    +--- androidx.core:core:1.1.0 -> 1.8.0 (*)
|    |    |    \--- androidx.collection:collection:1.1.0 (*)
|    |    +--- androidx.vectordrawable:vectordrawable:1.1.0 (*)
|    |    \--- androidx.viewpager2:viewpager2:1.0.0
|    |         +--- androidx.annotation:annotation:1.1.0 -> 1.3.0
|    |         +--- androidx.fragment:fragment:1.1.0 -> 1.5.7 (*)
|    |         +--- androidx.recyclerview:recyclerview:1.1.0 -> 1.3.0 (*)
|    |         +--- androidx.core:core:1.1.0 -> 1.8.0 (*)
|    |         \--- androidx.collection:collection:1.1.0 (*)
|    +--- androidx.browser:browser:1.2.0 -> 1.5.0
|    |    +--- androidx.annotation:annotation:1.2.0 -> 1.3.0
|    |    +--- androidx.collection:collection:1.1.0 (*)
|    |    +--- androidx.concurrent:concurrent-futures:1.0.0 -> 1.1.0 (*)
|    |    +--- androidx.core:core:1.1.0 -> 1.8.0 (*)
|    |    +--- androidx.interpolator:interpolator:1.0.0 (*)
|    |    \--- com.google.guava:listenablefuture:1.0 -> 9999.0-empty-to-avoid-conflict-with-guava
|    +--- androidx.navigation:navigation-fragment-ktx:2.3.0-alpha02
|    |    +--- androidx.navigation:navigation-fragment:2.3.0-alpha02
|    |    |    +--- androidx.fragment:fragment:1.2.1 -> 1.5.7 (*)
|    |    |    \--- androidx.navigation:navigation-runtime:2.3.0-alpha02
|    |    |         +--- androidx.navigation:navigation-common:2.3.0-alpha02
|    |    |         |    +--- androidx.annotation:annotation:1.1.0 -> 1.3.0
|    |    |         |    +--- androidx.core:core:1.2.0 -> 1.8.0 (*)
|    |    |         |    \--- androidx.collection:collection:1.1.0 (*)
|    |    |         +--- androidx.activity:activity:1.1.0 -> 1.6.0 (*)
|    |    |         +--- androidx.lifecycle:lifecycle-viewmodel:2.2.0 -> 2.6.1 (*)
|    |    |         +--- androidx.savedstate:savedstate:1.0.0 -> 1.2.1 (*)
|    |    |         \--- androidx.lifecycle:lifecycle-viewmodel-savedstate:2.2.0 -> 2.6.1 (*)
|    |    +--- androidx.navigation:navigation-runtime-ktx:2.3.0-alpha02
|    |    |    +--- androidx.navigation:navigation-runtime:2.3.0-alpha02 (*)
|    |    |    +--- androidx.navigation:navigation-common-ktx:2.3.0-alpha02
|    |    |    |    +--- androidx.navigation:navigation-common:2.3.0-alpha02 (*)
|    |    |    |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.3.60 -> 1.9.24 (*)
|    |    |    |    \--- androidx.collection:collection:1.1.0 (*)
|    |    |    +--- androidx.activity:activity-ktx:1.1.0 -> 1.6.0
|    |    |    |    +--- androidx.activity:activity:1.6.0 (*)
|    |    |    |    +--- androidx.core:core-ktx:1.1.0 -> 1.8.0 (*)
|    |    |    |    +--- androidx.lifecycle:lifecycle-runtime-ktx:2.5.1 -> 2.6.1 (*)
|    |    |    |    +--- androidx.lifecycle:lifecycle-viewmodel-ktx:2.5.1 -> 2.6.1
|    |    |    |    |    +--- androidx.lifecycle:lifecycle-viewmodel:2.6.1 (*)
|    |    |    |    |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.10 -> 1.9.24 (*)
|    |    |    |    |    +--- org.jetbrains.kotlinx:kotlinx-coroutines-android:1.6.4 (*)
|    |    |    |    |    +--- androidx.lifecycle:lifecycle-common:2.6.1 (c)
|    |    |    |    |    +--- androidx.lifecycle:lifecycle-livedata:2.6.1 (c)
|    |    |    |    |    +--- androidx.lifecycle:lifecycle-livedata-core:2.6.1 (c)
|    |    |    |    |    +--- androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.1 (c)
|    |    |    |    |    +--- androidx.lifecycle:lifecycle-livedata-ktx:2.6.1 (c)
|    |    |    |    |    +--- androidx.lifecycle:lifecycle-process:2.6.1 (c)
|    |    |    |    |    +--- androidx.lifecycle:lifecycle-runtime:2.6.1 (c)
|    |    |    |    |    +--- androidx.lifecycle:lifecycle-runtime-ktx:2.6.1 (c)
|    |    |    |    |    +--- androidx.lifecycle:lifecycle-service:2.6.1 (c)
|    |    |    |    |    +--- androidx.lifecycle:lifecycle-viewmodel:2.6.1 (c)
|    |    |    |    |    \--- androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.1 (c)
|    |    |    |    +--- androidx.savedstate:savedstate-ktx:1.2.0 -> 1.2.1
|    |    |    |    |    +--- androidx.savedstate:savedstate:1.2.1 (*)
|    |    |    |    |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.10 -> 1.9.24 (*)
|    |    |    |    |    \--- androidx.savedstate:savedstate:1.2.1 (c)
|    |    |    |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.7.10 -> 1.9.24 (*)
|    |    |    |    \--- androidx.activity:activity:1.6.0 (c)
|    |    |    +--- androidx.lifecycle:lifecycle-viewmodel-ktx:2.2.0 -> 2.6.1 (*)
|    |    |    \--- org.jetbrains.kotlin:kotlin-stdlib:1.3.60 -> 1.9.24 (*)
|    |    +--- androidx.fragment:fragment-ktx:1.2.1 -> 1.5.7
|    |    |    +--- androidx.activity:activity-ktx:1.5.1 -> 1.6.0 (*)
|    |    |    +--- androidx.collection:collection-ktx:1.1.0
|    |    |    |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.3.20 -> 1.9.24 (*)
|    |    |    |    \--- androidx.collection:collection:1.1.0 (*)
|    |    |    +--- androidx.core:core-ktx:1.2.0 -> 1.8.0 (*)
|    |    |    +--- androidx.fragment:fragment:1.5.7 (*)
|    |    |    +--- androidx.lifecycle:lifecycle-livedata-core-ktx:2.5.1 -> 2.6.1
|    |    |    |    +--- androidx.lifecycle:lifecycle-livedata-core:2.6.1 (*)
|    |    |    |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.10 -> 1.9.24 (*)
|    |    |    |    +--- androidx.lifecycle:lifecycle-common:2.6.1 (c)
|    |    |    |    +--- androidx.lifecycle:lifecycle-livedata:2.6.1 (c)
|    |    |    |    +--- androidx.lifecycle:lifecycle-livedata-core:2.6.1 (c)
|    |    |    |    +--- androidx.lifecycle:lifecycle-livedata-ktx:2.6.1 (c)
|    |    |    |    +--- androidx.lifecycle:lifecycle-process:2.6.1 (c)
|    |    |    |    +--- androidx.lifecycle:lifecycle-runtime:2.6.1 (c)
|    |    |    |    +--- androidx.lifecycle:lifecycle-runtime-ktx:2.6.1 (c)
|    |    |    |    +--- androidx.lifecycle:lifecycle-service:2.6.1 (c)
|    |    |    |    +--- androidx.lifecycle:lifecycle-viewmodel:2.6.1 (c)
|    |    |    |    +--- androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.1 (c)
|    |    |    |    \--- androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.1 (c)
|    |    |    +--- androidx.lifecycle:lifecycle-viewmodel-ktx:2.5.1 -> 2.6.1 (*)
|    |    |    +--- androidx.savedstate:savedstate-ktx:1.2.0 -> 1.2.1 (*)
|    |    |    \--- org.jetbrains.kotlin:kotlin-stdlib:1.6.21 -> 1.9.24 (*)
|    |    +--- androidx.lifecycle:lifecycle-viewmodel-ktx:2.2.0 -> 2.6.1 (*)
|    |    \--- org.jetbrains.kotlin:kotlin-stdlib:1.3.60 -> 1.9.24 (*)
|    +--- androidx.navigation:navigation-ui-ktx:2.3.0-alpha02
|    |    +--- androidx.navigation:navigation-ui:2.3.0-alpha02
|    |    |    +--- androidx.navigation:navigation-runtime:2.3.0-alpha02 (*)
|    |    |    +--- com.google.android.material:material:1.0.0 -> 1.9.0 (*)
|    |    |    \--- androidx.transition:transition:1.3.0 -> 1.4.1 (*)
|    |    +--- androidx.navigation:navigation-runtime-ktx:2.3.0-alpha02 (*)
|    |    \--- org.jetbrains.kotlin:kotlin-stdlib:1.3.60 -> 1.9.24 (*)
|    +--- androidx.lifecycle:lifecycle-viewmodel-ktx:2.4.0 -> 2.6.1 (*)
|    \--- androidx.lifecycle:lifecycle-viewmodel:2.4.0 -> 2.6.1 (*)
+--- project :neuro
|    +--- org.jetbrains.kotlin:kotlin-stdlib:1.9.24 (*)
|    \--- org.jetbrains.kotlinx:kotlinx-coroutines-core:1.6.0 -> 1.6.4 (*)
+--- project :privypass
+--- project :ui-component
|    +--- androidx.databinding:viewbinding:8.1.4 (*)
|    +--- org.jetbrains.kotlin:kotlin-stdlib:1.9.24 (*)
|    +--- androidx.core:core-ktx:1.6.0 -> 1.8.0 (*)
|    +--- androidx.appcompat:appcompat:1.4.2 -> 1.6.1 (*)
|    +--- com.google.android.material:material:1.9.0 (*)
|    \--- androidx.constraintlayout:constraintlayout:2.0.4 -> 2.1.4 (*)
+--- project :buku-bluetooth-printer
|    +--- androidx.databinding:viewbinding:8.1.4 (*)
|    +--- org.jetbrains.kotlin:kotlin-stdlib:1.9.24 (*)
|    +--- org.jetbrains.kotlin:kotlin-parcelize-runtime:1.9.24 (*)
|    +--- androidx.core:core-ktx:1.6.0 -> 1.8.0 (*)
|    +--- androidx.appcompat:appcompat:1.4.2 -> 1.6.1 (*)
|    +--- androidx.lifecycle:lifecycle-extensions:2.2.0
|    |    +--- androidx.lifecycle:lifecycle-runtime:2.2.0 -> 2.6.1 (*)
|    |    +--- androidx.arch.core:core-common:2.1.0 -> 2.2.0 (*)
|    |    +--- androidx.arch.core:core-runtime:2.1.0 -> 2.2.0 (*)
|    |    +--- androidx.fragment:fragment:1.2.0 -> 1.5.7 (*)
|    |    +--- androidx.lifecycle:lifecycle-common:2.2.0 -> 2.6.1 (*)
|    |    +--- androidx.lifecycle:lifecycle-livedata:2.2.0 -> 2.6.1 (*)
|    |    +--- androidx.lifecycle:lifecycle-process:2.2.0 -> 2.6.1 (*)
|    |    +--- androidx.lifecycle:lifecycle-service:2.2.0 -> 2.6.1 (*)
|    |    \--- androidx.lifecycle:lifecycle-viewmodel:2.2.0 -> 2.6.1 (*)
|    +--- com.google.android.material:material:1.9.0 (*)
|    +--- androidx.swiperefreshlayout:swiperefreshlayout:1.1.0
|    |    +--- androidx.annotation:annotation:1.1.0 -> 1.3.0
|    |    +--- androidx.core:core:1.1.0 -> 1.8.0 (*)
|    |    \--- androidx.interpolator:interpolator:1.0.0 (*)
|    \--- io.github.pilgr:paperdb:2.7.1
|         +--- com.esotericsoftware:kryo:4.0.1
|         |    +--- com.esotericsoftware:reflectasm:1.11.3
|         |    |    \--- org.ow2.asm:asm:5.0.4
|         |    +--- com.esotericsoftware:minlog:1.3.0
|         |    \--- org.objenesis:objenesis:2.5.1
|         \--- de.javakaffee:kryo-serializers:0.40
|              \--- com.esotericsoftware:kryo:4.0.0 -> 4.0.1 (*)
+--- project :stealth
|    \--- org.jetbrains.kotlin:kotlin-stdlib:1.9.24 (*)
+--- project :stealth-compiler
|    +--- org.jetbrains.kotlin:kotlin-stdlib:1.9.24 (*)
|    +--- project :stealth (*)
|    +--- com.google.auto.service:auto-service:1.0.1
|    |    +--- com.google.auto.service:auto-service-annotations:1.0.1
|    |    +--- com.google.auto:auto-common:1.2
|    |    |    \--- com.google.guava:guava:31.0.1-jre
|    |    |         +--- com.google.guava:failureaccess:1.0.1
|    |    |         +--- com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava
|    |    |         +--- com.google.code.findbugs:jsr305:3.0.2
|    |    |         +--- org.checkerframework:checker-qual:3.12.0
|    |    |         +--- com.google.errorprone:error_prone_annotations:2.7.1 -> 2.15.0
|    |    |         \--- com.google.j2objc:j2objc-annotations:1.3
|    |    \--- com.google.guava:guava:31.0.1-jre (*)
|    +--- javax.inject:javax.inject:1
|    \--- com.squareup:kotlinpoet:1.10.0
|         +--- org.jetbrains.kotlin:kotlin-reflect:1.5.31
|         |    \--- org.jetbrains.kotlin:kotlin-stdlib:1.5.31 -> 1.9.24 (*)
|         \--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.5.31 -> 1.8.20 (*)
+--- id.bureau:device-intelligence:4.2.0
|    +--- id.bureau:network:4.2.0
|    |    +--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.6.21 -> 1.8.20 (*)
|    |    +--- io.michaelrocks:paranoid-core:0.3.7
|    |    +--- androidx.core:core-ktx:1.8.0 (*)
|    |    +--- com.squareup.retrofit2:retrofit:2.9.0
|    |    |    \--- com.squareup.okhttp3:okhttp:3.14.9 -> 4.11.0
|    |    |         +--- com.squareup.okio:okio:3.2.0
|    |    |         |    \--- com.squareup.okio:okio-jvm:3.2.0
|    |    |         |         +--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.6.20 -> 1.8.20 (*)
|    |    |         |         \--- org.jetbrains.kotlin:kotlin-stdlib-common:1.6.20 -> 1.9.24 (*)
|    |    |         +--- org.jetbrains.kotlin:kotlin-stdlib:1.6.20 -> 1.9.24 (*)
|    |    |         \--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.6.20 -> 1.8.20 (*)
|    |    +--- com.squareup.retrofit2:converter-gson:2.9.0
|    |    |    +--- com.squareup.retrofit2:retrofit:2.9.0 (*)
|    |    |    \--- com.google.code.gson:gson:2.8.5 -> 2.10.1
|    |    \--- com.squareup.okhttp3:logging-interceptor:4.11.0
|    |         +--- com.squareup.okhttp3:okhttp:4.11.0 (*)
|    |         \--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.6.10 -> 1.8.20 (*)
|    +--- id.bureau:base:4.2.0
|    |    +--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.6.21 -> 1.8.20 (*)
|    |    +--- io.michaelrocks:paranoid-core:0.3.7
|    |    +--- androidx.core:core-ktx:1.8.0 (*)
|    |    \--- org.jetbrains.kotlinx:kotlinx-coroutines-android:1.6.4 (*)
|    +--- id.bureau:checkRoot:4.2.0
|    |    +--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.6.21 -> 1.8.20 (*)
|    |    +--- io.michaelrocks:paranoid-core:0.3.7
|    |    \--- androidx.core:core-ktx:1.8.0 (*)
|    +--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.6.21 -> 1.8.20 (*)
|    +--- io.michaelrocks:paranoid-core:0.3.7
|    +--- androidx.core:core-ktx:1.8.0 (*)
|    +--- com.google.android.gms:play-services-ads-identifier:18.0.1
|    |    \--- com.google.android.gms:play-services-basement:18.0.0 -> 18.4.0
|    |         +--- androidx.collection:collection:1.0.0 -> 1.1.0 (*)
|    |         +--- androidx.core:core:1.2.0 -> 1.8.0 (*)
|    |         \--- androidx.fragment:fragment:1.1.0 -> 1.5.7 (*)
|    +--- com.squareup.retrofit2:retrofit:2.9.0 (*)
|    +--- com.google.android.gms:play-services-location:17.0.0 -> 21.0.1
|    |    +--- com.google.android.gms:play-services-base:18.1.0 -> 18.5.0
|    |    |    +--- androidx.collection:collection:1.0.0 -> 1.1.0 (*)
|    |    |    +--- androidx.core:core:1.2.0 -> 1.8.0 (*)
|    |    |    +--- androidx.fragment:fragment:1.0.0 -> 1.5.7 (*)
|    |    |    +--- com.google.android.gms:play-services-basement:18.4.0 (*)
|    |    |    \--- com.google.android.gms:play-services-tasks:18.2.0
|    |    |         \--- com.google.android.gms:play-services-basement:18.4.0 (*)
|    |    +--- com.google.android.gms:play-services-basement:18.1.0 -> 18.4.0 (*)
|    |    \--- com.google.android.gms:play-services-tasks:18.0.2 -> 18.2.0 (*)
|    +--- com.google.code.gson:gson:2.10.1
|    +--- com.google.android.gms:play-services-appset:16.1.0
|    |    +--- com.google.android.gms:play-services-base:18.5.0 (*)
|    |    +--- com.google.android.gms:play-services-basement:18.4.0 (*)
|    |    \--- com.google.android.gms:play-services-tasks:18.2.0 (*)
|    +--- org.jetbrains.kotlinx:kotlinx-coroutines-play-services:1.6.4
|    |    +--- org.jetbrains.kotlinx:kotlinx-coroutines-core:1.6.4 (*)
|    |    +--- org.jetbrains.kotlinx:kotlinx-coroutines-bom:1.6.4 (*)
|    |    +--- com.google.android.gms:play-services-tasks:16.0.1 -> 18.2.0 (*)
|    |    \--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.6.21 -> 1.8.20 (*)
|    +--- com.google.dagger:dagger-android:2.41
|    |    +--- com.google.dagger:dagger:2.41 -> 2.48
|    |    |    \--- javax.inject:javax.inject:1
|    |    +--- com.google.dagger:dagger-lint-aar:2.41 -> 2.48
|    |    +--- androidx.annotation:annotation:1.2.0 -> 1.3.0
|    |    \--- javax.inject:javax.inject:1
|    \--- com.google.dagger:dagger-android-support:2.41
|         +--- com.google.dagger:dagger:2.41 -> 2.48 (*)
|         +--- com.google.dagger:dagger-android:2.41 (*)
|         +--- com.google.dagger:dagger-lint-aar:2.41 -> 2.48
|         +--- androidx.activity:activity:1.3.1 -> 1.6.0 (*)
|         +--- androidx.annotation:annotation:1.2.0 -> 1.3.0
|         +--- androidx.appcompat:appcompat:1.3.1 -> 1.6.1 (*)
|         +--- androidx.fragment:fragment:1.3.6 -> 1.5.7 (*)
|         +--- androidx.lifecycle:lifecycle-common:2.3.1 -> 2.6.1 (*)
|         +--- androidx.lifecycle:lifecycle-viewmodel:2.3.1 -> 2.6.1 (*)
|         +--- androidx.lifecycle:lifecycle-viewmodel-savedstate:2.3.1 -> 2.6.1 (*)
|         \--- javax.inject:javax.inject:1
+--- com.mixpanel.android:mixpanel-android:7.5.2
+--- androidx.constraintlayout:constraintlayout:2.0.4 -> 2.1.4 (*)
+--- androidx.multidex:multidex:2.0.0
+--- androidx.recyclerview:recyclerview:1.0.0 -> 1.3.0 (*)
+--- androidx.cardview:cardview:1.0.0 (*)
+--- androidx.legacy:legacy-support-v4:1.0.0
|    +--- androidx.core:core:1.0.0 -> 1.8.0 (*)
|    +--- androidx.media:media:1.0.0 -> 1.3.1
|    |    +--- androidx.collection:collection:1.1.0 (*)
|    |    \--- androidx.core:core:1.3.0 -> 1.8.0 (*)
|    +--- androidx.legacy:legacy-support-core-utils:1.0.0 (*)
|    +--- androidx.legacy:legacy-support-core-ui:1.0.0
|    |    +--- androidx.annotation:annotation:1.0.0 -> 1.3.0
|    |    +--- androidx.core:core:1.0.0 -> 1.8.0 (*)
|    |    +--- androidx.legacy:legacy-support-core-utils:1.0.0 (*)
|    |    +--- androidx.customview:customview:1.0.0 -> 1.1.0 (*)
|    |    +--- androidx.viewpager:viewpager:1.0.0 (*)
|    |    +--- androidx.coordinatorlayout:coordinatorlayout:1.0.0 -> 1.1.0 (*)
|    |    +--- androidx.drawerlayout:drawerlayout:1.0.0 -> 1.1.1 (*)
|    |    +--- androidx.slidingpanelayout:slidingpanelayout:1.0.0
|    |    |    +--- androidx.annotation:annotation:1.0.0 -> 1.3.0
|    |    |    +--- androidx.core:core:1.0.0 -> 1.8.0 (*)
|    |    |    \--- androidx.customview:customview:1.0.0 -> 1.1.0 (*)
|    |    +--- androidx.interpolator:interpolator:1.0.0 (*)
|    |    +--- androidx.swiperefreshlayout:swiperefreshlayout:1.0.0 -> 1.1.0 (*)
|    |    +--- androidx.asynclayoutinflater:asynclayoutinflater:1.0.0
|    |    |    +--- androidx.annotation:annotation:1.0.0 -> 1.3.0
|    |    |    \--- androidx.core:core:1.0.0 -> 1.8.0 (*)
|    |    \--- androidx.cursoradapter:cursoradapter:1.0.0 (*)
|    \--- androidx.fragment:fragment:1.0.0 -> 1.5.7 (*)
+--- androidx.activity:activity-ktx:1.1.0 -> 1.6.0 (*)
+--- androidx.core:core-ktx:1.6.0 -> 1.8.0 (*)
+--- androidx.navigation:navigation-fragment-ktx:2.3.0-alpha02 (*)
+--- androidx.navigation:navigation-ui-ktx:2.3.0-alpha02 (*)
+--- androidx.appcompat:appcompat:1.4.2 -> 1.6.1 (*)
+--- androidx.fragment:fragment-ktx:1.4.1 -> 1.5.7 (*)
+--- androidx.work:work-runtime-ktx:2.7.0
|    +--- androidx.work:work-runtime:2.7.0
|    |    +--- androidx.annotation:annotation-experimental:1.0.0 -> 1.1.0
|    |    +--- com.google.guava:listenablefuture:1.0 -> 9999.0-empty-to-avoid-conflict-with-guava
|    |    +--- androidx.lifecycle:lifecycle-livedata:2.1.0 -> 2.6.1 (*)
|    |    +--- androidx.startup:startup-runtime:1.0.0 -> 1.1.1 (*)
|    |    +--- androidx.core:core:1.6.0 -> 1.8.0 (*)
|    |    +--- androidx.room:room-runtime:2.2.5 -> 2.5.2
|    |    |    +--- androidx.annotation:annotation-experimental:1.1.0
|    |    |    +--- androidx.arch.core:core-runtime:2.0.1 -> 2.2.0 (*)
|    |    |    +--- androidx.room:room-common:2.5.2
|    |    |    |    +--- androidx.annotation:annotation:1.3.0
|    |    |    |    \--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.7.20 -> 1.8.20 (*)
|    |    |    +--- androidx.sqlite:sqlite:2.3.1
|    |    |    |    +--- androidx.annotation:annotation:1.0.0 -> 1.3.0
|    |    |    |    \--- org.jetbrains.kotlin:kotlin-stdlib:1.7.20 -> 1.9.24 (*)
|    |    |    \--- androidx.sqlite:sqlite-framework:2.3.1
|    |    |         +--- androidx.annotation:annotation:1.2.0 -> 1.3.0
|    |    |         +--- androidx.sqlite:sqlite:2.3.1 (*)
|    |    |         \--- org.jetbrains.kotlin:kotlin-stdlib:1.7.20 -> 1.9.24 (*)
|    |    +--- androidx.sqlite:sqlite:2.1.0 -> 2.3.1 (*)
|    |    +--- androidx.sqlite:sqlite-framework:2.1.0 -> 2.3.1 (*)
|    |    +--- androidx.core:core:1.1.0 -> 1.8.0 (*)
|    |    \--- androidx.lifecycle:lifecycle-service:2.1.0 -> 2.6.1 (*)
|    +--- org.jetbrains.kotlin:kotlin-stdlib:1.5.30 -> 1.9.24 (*)
|    \--- org.jetbrains.kotlinx:kotlinx-coroutines-android:1.5.0 -> 1.6.4 (*)
+--- androidx.hilt:hilt-work:1.0.0
|    +--- androidx.annotation:annotation:1.1.0 -> 1.3.0
|    +--- androidx.hilt:hilt-common:1.0.0
|    |    \--- com.google.dagger:hilt-core:2.35 -> 2.48
|    |         +--- com.google.dagger:dagger:2.48 (*)
|    |         +--- com.google.code.findbugs:jsr305:3.0.2
|    |         \--- javax.inject:javax.inject:1
|    +--- androidx.work:work-runtime:2.3.4 -> 2.7.0 (*)
|    \--- com.google.dagger:hilt-android:2.35 -> 2.48
|         +--- com.google.dagger:dagger:2.48 (*)
|         +--- com.google.dagger:dagger-lint-aar:2.48
|         +--- com.google.dagger:hilt-core:2.48 (*)
|         +--- com.google.code.findbugs:jsr305:3.0.2
|         +--- androidx.activity:activity:1.5.1 -> 1.6.0 (*)
|         +--- androidx.annotation:annotation:1.2.0 -> 1.3.0
|         +--- androidx.fragment:fragment:1.5.1 -> 1.5.7 (*)
|         +--- androidx.lifecycle:lifecycle-common:2.5.1 -> 2.6.1 (*)
|         +--- androidx.lifecycle:lifecycle-viewmodel:2.5.1 -> 2.6.1 (*)
|         +--- androidx.lifecycle:lifecycle-viewmodel-savedstate:2.5.1 -> 2.6.1 (*)
|         +--- androidx.savedstate:savedstate:1.2.0 -> 1.2.1 (*)
|         +--- javax.inject:javax.inject:1
|         \--- org.jetbrains.kotlin:kotlin-stdlib:1.9.0 -> 1.9.24 (*)
+--- androidx.lifecycle:lifecycle-extensions:2.2.0 (*)
+--- androidx.lifecycle:lifecycle-livedata-core:2.5.1 -> 2.6.1 (*)
+--- androidx.lifecycle:lifecycle-livedata-ktx:2.5.1 -> 2.6.1
|    +--- androidx.lifecycle:lifecycle-livedata:2.6.1 (*)
|    +--- androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.1 (*)
|    +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.10 -> 1.9.24 (*)
|    +--- org.jetbrains.kotlinx:kotlinx-coroutines-core:1.6.4 (*)
|    +--- androidx.lifecycle:lifecycle-common:2.6.1 (c)
|    +--- androidx.lifecycle:lifecycle-livedata:2.6.1 (c)
|    +--- androidx.lifecycle:lifecycle-livedata-core:2.6.1 (c)
|    +--- androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.1 (c)
|    +--- androidx.lifecycle:lifecycle-process:2.6.1 (c)
|    +--- androidx.lifecycle:lifecycle-runtime:2.6.1 (c)
|    +--- androidx.lifecycle:lifecycle-runtime-ktx:2.6.1 (c)
|    +--- androidx.lifecycle:lifecycle-service:2.6.1 (c)
|    +--- androidx.lifecycle:lifecycle-viewmodel:2.6.1 (c)
|    +--- androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.1 (c)
|    \--- androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.1 (c)
+--- androidx.lifecycle:lifecycle-runtime-ktx:2.5.1 -> 2.6.1 (*)
+--- androidx.room:room-runtime:2.5.0 -> 2.5.2 (*)
+--- androidx.room:room-ktx:2.5.0 -> 2.5.2
|    +--- androidx.room:room-common:2.5.2 (*)
|    +--- androidx.room:room-runtime:2.5.2 (*)
|    +--- org.jetbrains.kotlin:kotlin-stdlib:1.7.20 -> 1.9.24 (*)
|    \--- org.jetbrains.kotlinx:kotlinx-coroutines-android:1.6.4 (*)
+--- androidx.room:room-rxjava2:2.5.0
|    +--- androidx.arch.core:core-runtime:2.0.1 -> 2.2.0 (*)
|    +--- androidx.room:room-common:2.5.0 -> 2.5.2 (*)
|    +--- androidx.room:room-runtime:2.5.0 -> 2.5.2 (*)
|    +--- io.reactivex.rxjava2:rxjava:2.2.9 -> 2.2.19
|    |    \--- org.reactivestreams:reactive-streams:1.0.3
|    \--- org.jetbrains.kotlin:kotlin-stdlib:1.7.20 -> 1.9.24 (*)
+--- com.google.android.material:material:1.9.0 (*)
+--- com.google.android.gms:play-services-location:21.0.1 (*)
+--- com.google.android.gms:play-services-auth-api-phone:17.0.0 -> 17.4.0
|    +--- com.google.android.gms:play-services-base:17.1.0 -> 18.5.0 (*)
|    +--- com.google.android.gms:play-services-basement:17.1.0 -> 18.4.0 (*)
|    \--- com.google.android.gms:play-services-tasks:17.0.0 -> 18.2.0 (*)
+--- com.google.android.gms:play-services-base:17.0.0 -> 18.5.0 (*)
+--- com.google.android.gms:play-services-identity:17.0.0
|    +--- com.google.android.gms:play-services-base:17.0.0 -> 18.5.0 (*)
|    \--- com.google.android.gms:play-services-basement:17.0.0 -> 18.4.0 (*)
+--- com.google.android.gms:play-services-auth:17.0.0
|    +--- androidx.fragment:fragment:1.0.0 -> 1.5.7 (*)
|    +--- androidx.loader:loader:1.0.0 (*)
|    +--- com.google.android.gms:play-services-auth-api-phone:17.0.0 -> 17.4.0 (*)
|    +--- com.google.android.gms:play-services-auth-base:17.0.0
|    |    +--- androidx.collection:collection:1.0.0 -> 1.1.0 (*)
|    |    +--- com.google.android.gms:play-services-base:17.0.0 -> 18.5.0 (*)
|    |    +--- com.google.android.gms:play-services-basement:17.0.0 -> 18.4.0 (*)
|    |    \--- com.google.android.gms:play-services-tasks:17.0.0 -> 18.2.0 (*)
|    +--- com.google.android.gms:play-services-base:17.0.0 -> 18.5.0 (*)
|    +--- com.google.android.gms:play-services-basement:17.0.0 -> 18.4.0 (*)
|    \--- com.google.android.gms:play-services-tasks:17.0.0 -> 18.2.0 (*)
+--- com.google.android.gms:play-services-tasks:18.0.0 -> 18.2.0 (*)
+--- com.google.android.play:app-update:2.1.0
|    +--- com.google.android.gms:play-services-basement:18.1.0 -> 18.4.0 (*)
|    +--- com.google.android.gms:play-services-tasks:18.0.2 -> 18.2.0 (*)
|    \--- com.google.android.play:core-common:2.0.3
+--- com.google.firebase:firebase-bom:30.2.0
|    +--- com.google.firebase:firebase-perf:20.1.0 (c)
|    +--- com.google.firebase:firebase-storage-ktx:20.0.1 (c)
|    +--- com.google.firebase:firebase-perf-ktx:20.1.0 (c)
|    +--- com.google.firebase:firebase-analytics:21.0.0 (c)
|    +--- com.google.firebase:firebase-auth:21.0.6 (c)
|    +--- com.google.firebase:firebase-config-ktx:21.1.0 (c)
|    +--- com.google.firebase:firebase-firestore-ktx:24.2.0 (c)
|    +--- com.google.firebase:firebase-messaging:23.0.6 (c)
|    +--- com.google.firebase:firebase-crashlytics:18.2.11 (c)
|    +--- com.google.firebase:firebase-dynamic-links:21.0.1 (c)
|    +--- com.google.firebase:firebase-encoders:17.0.0 (c)
|    +--- com.google.firebase:firebase-common:20.1.1 (c)
|    +--- com.google.firebase:firebase-config:21.1.0 (c)
|    +--- com.google.firebase:firebase-installations:17.0.1 (c)
|    +--- com.google.firebase:firebase-common-ktx:20.1.1 (c)
|    +--- com.google.firebase:firebase-storage:20.0.1 (c)
|    \--- com.google.firebase:firebase-firestore:24.2.0 (c)
+--- com.google.firebase:firebase-messaging -> 23.0.6
|    +--- androidx.annotation:annotation:1.2.0 -> 1.3.0
|    +--- com.google.android.datatransport:transport-api:3.0.0
|    |    \--- androidx.annotation:annotation:1.1.0 -> 1.3.0
|    +--- com.google.android.datatransport:transport-backend-cct:3.1.6
|    |    +--- androidx.annotation:annotation:1.1.0 -> 1.3.0
|    |    +--- com.google.android.datatransport:transport-api:3.0.0 (*)
|    |    +--- com.google.android.datatransport:transport-runtime:3.1.6
|    |    |    +--- androidx.annotation:annotation:1.1.0 -> 1.3.0
|    |    |    +--- com.google.android.datatransport:transport-api:3.0.0 (*)
|    |    |    +--- com.google.firebase:firebase-encoders:17.0.0
|    |    |    |    \--- androidx.annotation:annotation:1.1.0 -> 1.3.0
|    |    |    +--- com.google.firebase:firebase-encoders-proto:16.0.0
|    |    |    |    +--- androidx.annotation:annotation:1.1.0 -> 1.3.0
|    |    |    |    \--- com.google.firebase:firebase-encoders:17.0.0 (*)
|    |    |    \--- javax.inject:javax.inject:1
|    |    +--- com.google.firebase:firebase-encoders:17.0.0 (*)
|    |    \--- com.google.firebase:firebase-encoders-json:18.0.0
|    |         +--- androidx.annotation:annotation:1.1.0 -> 1.3.0
|    |         \--- com.google.firebase:firebase-encoders:17.0.0 (*)
|    +--- com.google.android.datatransport:transport-runtime:3.1.6 (*)
|    +--- com.google.android.gms:play-services-base:18.0.1 -> 18.5.0 (*)
|    +--- com.google.android.gms:play-services-basement:18.0.0 -> 18.4.0 (*)
|    +--- com.google.android.gms:play-services-cloud-messaging:17.0.1
|    |    +--- com.google.android.gms:play-services-basement:18.0.0 -> 18.4.0 (*)
|    |    \--- com.google.android.gms:play-services-tasks:18.0.0 -> 18.2.0 (*)
|    +--- com.google.android.gms:play-services-stats:17.0.2
|    |    +--- androidx.legacy:legacy-support-core-utils:1.0.0 (*)
|    |    \--- com.google.android.gms:play-services-basement:18.0.0 -> 18.4.0 (*)
|    +--- com.google.android.gms:play-services-tasks:18.0.1 -> 18.2.0 (*)
|    +--- com.google.errorprone:error_prone_annotations:2.9.0 -> 2.15.0
|    +--- com.google.firebase:firebase-common:20.1.1
|    |    +--- com.google.android.gms:play-services-basement:18.0.0 -> 18.4.0 (*)
|    |    +--- com.google.android.gms:play-services-tasks:18.0.1 -> 18.2.0 (*)
|    |    \--- com.google.firebase:firebase-components:17.0.0
|    |         +--- androidx.annotation:annotation:1.1.0 -> 1.3.0
|    |         \--- com.google.firebase:firebase-annotations:16.0.0 -> 16.1.0
|    +--- com.google.firebase:firebase-components:17.0.0 (*)
|    +--- com.google.firebase:firebase-datatransport:18.1.5
|    |    +--- androidx.annotation:annotation:1.1.0 -> 1.3.0
|    |    +--- com.google.android.datatransport:transport-api:3.0.0 (*)
|    |    +--- com.google.android.datatransport:transport-backend-cct:3.1.6 (*)
|    |    +--- com.google.android.datatransport:transport-runtime:3.1.6 (*)
|    |    +--- com.google.firebase:firebase-common:20.1.1 (*)
|    |    \--- com.google.firebase:firebase-components:17.0.0 (*)
|    +--- com.google.firebase:firebase-encoders:17.0.0 (*)
|    +--- com.google.firebase:firebase-encoders-json:18.0.0 (*)
|    +--- com.google.firebase:firebase-encoders-proto:16.0.0 (*)
|    +--- com.google.firebase:firebase-iid-interop:17.1.0
|    |    +--- com.google.android.gms:play-services-basement:17.0.0 -> 18.4.0 (*)
|    |    \--- com.google.android.gms:play-services-tasks:17.0.0 -> 18.2.0 (*)
|    +--- com.google.firebase:firebase-installations:17.0.1
|    |    +--- com.google.android.gms:play-services-tasks:18.0.1 -> 18.2.0 (*)
|    |    +--- com.google.firebase:firebase-common:20.1.0 -> 20.1.1 (*)
|    |    +--- com.google.firebase:firebase-components:17.0.0 (*)
|    |    \--- com.google.firebase:firebase-installations-interop:17.0.1
|    |         +--- com.google.android.gms:play-services-tasks:18.0.1 -> 18.2.0 (*)
|    |         \--- com.google.firebase:firebase-annotations:16.0.0 -> 16.1.0
|    +--- com.google.firebase:firebase-installations-interop:17.0.1 (*)
|    \--- com.google.firebase:firebase-measurement-connector:19.0.0
|         +--- com.google.android.gms:play-services-basement:17.0.0 -> 18.4.0 (*)
|         \--- com.google.firebase:firebase-annotations:16.0.0 -> 16.1.0
+--- com.google.firebase:firebase-auth:19.2.0 -> 21.0.6
|    +--- androidx.browser:browser:1.4.0 -> 1.5.0 (*)
|    +--- androidx.collection:collection:1.0.0 -> 1.1.0 (*)
|    +--- androidx.fragment:fragment:1.0.0 -> 1.5.7 (*)
|    +--- androidx.localbroadcastmanager:localbroadcastmanager:1.0.0 (*)
|    +--- com.google.android.gms:play-services-auth-api-phone:17.4.0 (*)
|    +--- com.google.android.gms:play-services-basement:18.0.0 -> 18.4.0 (*)
|    +--- com.google.android.gms:play-services-safetynet:17.0.0
|    |    +--- com.google.android.gms:play-services-base:17.0.0 -> 18.5.0 (*)
|    |    +--- com.google.android.gms:play-services-basement:17.0.0 -> 18.4.0 (*)
|    |    \--- com.google.android.gms:play-services-tasks:17.0.0 -> 18.2.0 (*)
|    +--- com.google.android.gms:play-services-tasks:18.0.1 -> 18.2.0 (*)
|    +--- com.google.firebase:firebase-auth-interop:20.0.0
|    |    +--- com.google.android.gms:play-services-basement:17.0.0 -> 18.4.0 (*)
|    |    +--- com.google.android.gms:play-services-tasks:17.0.0 -> 18.2.0 (*)
|    |    +--- com.google.firebase:firebase-annotations:16.0.0 -> 16.1.0
|    |    \--- com.google.firebase:firebase-common:20.0.0 -> 20.1.1 (*)
|    +--- com.google.firebase:firebase-common:20.1.0 -> 20.1.1 (*)
|    \--- com.google.firebase:firebase-components:17.0.0 (*)
+--- com.google.firebase:firebase-firestore-ktx:20.2.0 -> 24.2.0
|    +--- androidx.annotation:annotation:1.1.0 -> 1.3.0
|    +--- com.google.firebase:firebase-common:20.1.1 (*)
|    +--- com.google.firebase:firebase-common-ktx:20.1.1
|    |    +--- androidx.annotation:annotation:1.1.0 -> 1.3.0
|    |    +--- com.google.firebase:firebase-common:20.1.1 (*)
|    |    +--- com.google.firebase:firebase-components:17.0.0 (*)
|    |    \--- org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.3.72 -> 1.8.20 (*)
|    +--- com.google.firebase:firebase-components:17.0.0 (*)
|    +--- com.google.firebase:firebase-firestore:24.2.0
|    |    +--- androidx.annotation:annotation:1.1.0 -> 1.3.0
|    |    +--- com.google.android.gms:play-services-base:18.0.1 -> 18.5.0 (*)
|    |    +--- com.google.android.gms:play-services-basement:18.0.0 -> 18.4.0 (*)
|    |    +--- com.google.android.gms:play-services-tasks:18.0.1 -> 18.2.0 (*)
|    |    +--- com.google.firebase:firebase-annotations:16.1.0
|    |    +--- com.google.firebase:firebase-appcheck-interop:16.0.0
|    |    |    +--- com.google.android.gms:play-services-base:18.0.1 -> 18.5.0 (*)
|    |    |    +--- com.google.android.gms:play-services-tasks:18.0.1 -> 18.2.0 (*)
|    |    |    +--- com.google.firebase:firebase-common:20.1.1 (*)
|    |    |    \--- com.google.firebase:firebase-components:17.0.0 (*)
|    |    +--- com.google.firebase:firebase-auth-interop:19.0.2 -> 20.0.0 (*)
|    |    +--- com.google.firebase:firebase-common:20.1.1 (*)
|    |    +--- com.google.firebase:firebase-components:17.0.0 (*)
|    |    +--- com.google.firebase:firebase-database-collection:18.0.1
|    |    |    \--- com.google.android.gms:play-services-base:18.0.1 -> 18.5.0 (*)
|    |    +--- com.google.firebase:protolite-well-known-types:18.0.0
|    |    |    \--- com.google.protobuf:protobuf-javalite:3.14.0 -> 3.19.2
|    |    +--- com.squareup.okhttp:okhttp:2.7.5
|    |    |    \--- com.squareup.okio:okio:1.6.0 -> 3.2.0 (*)
|    |    +--- io.grpc:grpc-android:1.44.1
|    |    |    +--- io.grpc:grpc-core:1.44.1
|    |    |    |    +--- io.grpc:grpc-api:1.44.1
|    |    |    |    |    +--- io.grpc:grpc-context:1.44.1
|    |    |    |    |    +--- com.google.code.findbugs:jsr305:3.0.2
|    |    |    |    |    +--- com.google.guava:guava:30.1.1-android -> 31.0.1-jre (*)
|    |    |    |    |    \--- com.google.errorprone:error_prone_annotations:2.9.0 -> 2.15.0
|    |    |    |    +--- com.google.code.gson:gson:2.8.9 -> 2.10.1
|    |    |    |    +--- com.google.android:annotations:4.1.1.4
|    |    |    |    +--- org.codehaus.mojo:animal-sniffer-annotations:1.19
|    |    |    |    +--- com.google.errorprone:error_prone_annotations:2.9.0 -> 2.15.0
|    |    |    |    +--- com.google.guava:guava:30.1.1-android -> 31.0.1-jre (*)
|    |    |    |    \--- io.perfmark:perfmark-api:0.23.0
|    |    |    \--- com.google.guava:guava:30.1.1-android -> 31.0.1-jre (*)
|    |    +--- io.grpc:grpc-okhttp:1.44.1
|    |    |    +--- io.grpc:grpc-core:1.44.1 (*)
|    |    |    +--- com.squareup.okhttp:okhttp:2.7.4 -> 2.7.5 (*)
|    |    |    +--- com.squareup.okio:okio:1.17.5 -> 3.2.0 (*)
|    |    |    +--- com.google.guava:guava:30.1.1-android -> 31.0.1-jre (*)
|    |    |    \--- io.perfmark:perfmark-api:0.23.0
|    |    +--- io.grpc:grpc-protobuf-lite:1.44.1
|    |    |    +--- io.grpc:grpc-api:1.44.1 (*)
|    |    |    +--- com.google.protobuf:protobuf-javalite:3.19.2
|    |    |    +--- com.google.code.findbugs:jsr305:3.0.2
|    |    |    \--- com.google.guava:guava:30.1.1-android -> 31.0.1-jre (*)
|    |    \--- io.grpc:grpc-stub:1.44.1
|    |         +--- io.grpc:grpc-api:1.44.1 (*)
|    |         +--- com.google.guava:guava:30.1.1-android -> 31.0.1-jre (*)
|    |         \--- com.google.errorprone:error_prone_annotations:2.9.0 -> 2.15.0
|    \--- org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.3.72 -> 1.8.20 (*)
+--- com.google.firebase:firebase-dynamic-links:19.1.0 -> 21.0.1
|    +--- androidx.annotation:annotation:1.2.0 -> 1.3.0
|    +--- com.google.android.gms:play-services-base:18.0.1 -> 18.5.0 (*)
|    +--- com.google.android.gms:play-services-basement:18.0.0 -> 18.4.0 (*)
|    +--- com.google.android.gms:play-services-tasks:18.0.1 -> 18.2.0 (*)
|    +--- com.google.firebase:firebase-auth-interop:20.0.0 (*)
|    +--- com.google.firebase:firebase-common:20.1.0 -> 20.1.1 (*)
|    +--- com.google.firebase:firebase-components:17.0.0 (*)
|    \--- com.google.firebase:firebase-measurement-connector:19.0.0 (*)
+--- com.google.firebase:firebase-analytics:17.5.0 -> 21.0.0
|    +--- com.google.android.gms:play-services-measurement:21.0.0
|    |    +--- androidx.collection:collection:1.0.0 -> 1.1.0 (*)
|    |    +--- androidx.legacy:legacy-support-core-utils:1.0.0 (*)
|    |    +--- com.google.android.gms:play-services-ads-identifier:18.0.0 -> 18.0.1 (*)
|    |    +--- com.google.android.gms:play-services-basement:18.0.0 -> 18.4.0 (*)
|    |    +--- com.google.android.gms:play-services-measurement-base:21.0.0
|    |    |    \--- com.google.android.gms:play-services-basement:18.0.0 -> 18.4.0 (*)
|    |    +--- com.google.android.gms:play-services-measurement-impl:21.0.0
|    |    |    +--- androidx.collection:collection:1.0.0 -> 1.1.0 (*)
|    |    |    +--- androidx.core:core:1.0.0 -> 1.8.0 (*)
|    |    |    +--- com.google.android.gms:play-services-ads-identifier:18.0.0 -> 18.0.1 (*)
|    |    |    +--- com.google.android.gms:play-services-basement:18.0.0 -> 18.4.0 (*)
|    |    |    +--- com.google.android.gms:play-services-measurement-base:21.0.0 (*)
|    |    |    \--- com.google.android.gms:play-services-stats:17.0.2 (*)
|    |    \--- com.google.android.gms:play-services-stats:17.0.2 (*)
|    +--- com.google.android.gms:play-services-measurement-api:21.0.0
|    |    +--- com.google.android.gms:play-services-ads-identifier:18.0.0 -> 18.0.1 (*)
|    |    +--- com.google.android.gms:play-services-basement:18.0.0 -> 18.4.0 (*)
|    |    +--- com.google.android.gms:play-services-measurement-base:21.0.0 (*)
|    |    +--- com.google.android.gms:play-services-measurement-sdk-api:21.0.0
|    |    |    +--- com.google.android.gms:play-services-basement:18.0.0 -> 18.4.0 (*)
|    |    |    \--- com.google.android.gms:play-services-measurement-base:21.0.0 (*)
|    |    +--- com.google.android.gms:play-services-tasks:18.0.1 -> 18.2.0 (*)
|    |    +--- com.google.firebase:firebase-common:20.1.0 -> 20.1.1 (*)
|    |    +--- com.google.firebase:firebase-components:17.0.0 (*)
|    |    +--- com.google.firebase:firebase-installations:17.0.1 (*)
|    |    +--- com.google.firebase:firebase-installations-interop:17.0.0 -> 17.0.1 (*)
|    |    \--- com.google.firebase:firebase-measurement-connector:19.0.0 (*)
|    \--- com.google.android.gms:play-services-measurement-sdk:21.0.0
|         +--- androidx.collection:collection:1.0.0 -> 1.1.0 (*)
|         +--- com.google.android.gms:play-services-basement:18.0.0 -> 18.4.0 (*)
|         +--- com.google.android.gms:play-services-measurement-base:21.0.0 (*)
|         \--- com.google.android.gms:play-services-measurement-impl:21.0.0 (*)
+--- com.google.firebase:firebase-storage-ktx:19.2.0 -> 20.0.1
|    +--- androidx.annotation:annotation:1.1.0 -> 1.3.0
|    +--- com.google.android.gms:play-services-tasks:18.0.1 -> 18.2.0 (*)
|    +--- com.google.firebase:firebase-common:20.1.0 -> 20.1.1 (*)
|    +--- com.google.firebase:firebase-common-ktx:20.1.0 -> 20.1.1 (*)
|    +--- com.google.firebase:firebase-components:17.0.0 (*)
|    +--- com.google.firebase:firebase-storage:20.0.1
|    |    +--- androidx.annotation:annotation:1.1.0 -> 1.3.0
|    |    +--- com.google.android.gms:play-services-base:18.0.1 -> 18.5.0 (*)
|    |    +--- com.google.android.gms:play-services-tasks:18.0.1 -> 18.2.0 (*)
|    |    +--- com.google.firebase:firebase-appcheck-interop:16.0.0-beta02 -> 16.0.0 (*)
|    |    +--- com.google.firebase:firebase-auth-interop:18.0.0 -> 20.0.0 (*)
|    |    +--- com.google.firebase:firebase-common:20.1.0 -> 20.1.1 (*)
|    |    \--- com.google.firebase:firebase-components:17.0.0 (*)
|    \--- org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.3.72 -> 1.8.20 (*)
+--- com.google.firebase:firebase-config-ktx:20.0.0 -> 21.1.0
|    +--- androidx.annotation:annotation:1.1.0 -> 1.3.0
|    +--- com.google.firebase:firebase-abt:21.0.1
|    |    +--- com.google.android.gms:play-services-basement:18.0.0 -> 18.4.0 (*)
|    |    +--- com.google.firebase:firebase-common:20.1.0 -> 20.1.1 (*)
|    |    +--- com.google.firebase:firebase-components:17.0.0 (*)
|    |    \--- com.google.firebase:firebase-measurement-connector:18.0.0 -> 19.0.0 (*)
|    +--- com.google.firebase:firebase-common:20.1.1 (*)
|    +--- com.google.firebase:firebase-common-ktx:20.1.1 (*)
|    +--- com.google.firebase:firebase-components:17.0.0 (*)
|    +--- com.google.firebase:firebase-config:21.1.0
|    |    +--- com.google.android.gms:play-services-tasks:18.0.1 -> 18.2.0 (*)
|    |    +--- com.google.firebase:firebase-abt:21.0.1 (*)
|    |    +--- com.google.firebase:firebase-common:20.1.1 (*)
|    |    +--- com.google.firebase:firebase-components:17.0.0 (*)
|    |    +--- com.google.firebase:firebase-installations:17.0.1 (*)
|    |    +--- com.google.firebase:firebase-installations-interop:17.0.1 (*)
|    |    \--- com.google.firebase:firebase-measurement-connector:18.0.0 -> 19.0.0 (*)
|    +--- com.google.firebase:firebase-installations:17.0.1 (*)
|    +--- com.google.firebase:firebase-installations-interop:17.0.1 (*)
|    \--- org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.3.72 -> 1.8.20 (*)
+--- com.google.code.gson:gson:2.9.1 -> 2.10.1
+--- com.android.volley:volley:1.2.0
+--- com.android.installreferrer:installreferrer:1.1.2
+--- com.facebook.android:facebook-android-sdk:[5,9) -> 8.2.0
|    +--- com.facebook.android:facebook-core:8.2.0
|    |    +--- com.parse.bolts:bolts-android:1.4.0
|    |    |    +--- com.parse.bolts:bolts-tasks:1.4.0
|    |    |    \--- com.parse.bolts:bolts-applinks:1.4.0
|    |    |         \--- com.parse.bolts:bolts-tasks:1.4.0
|    |    +--- androidx.annotation:annotation:1.1.0 -> 1.3.0
|    |    +--- androidx.legacy:legacy-support-core-utils:1.0.0 (*)
|    |    \--- com.android.installreferrer:installreferrer:1.0 -> 1.1.2
|    +--- com.facebook.android:facebook-common:8.2.0
|    |    +--- com.facebook.android:facebook-core:8.2.0 (*)
|    |    +--- androidx.legacy:legacy-support-v4:1.0.0 (*)
|    |    +--- androidx.appcompat:appcompat:1.1.0 -> 1.6.1 (*)
|    |    +--- androidx.cardview:cardview:1.0.0 (*)
|    |    +--- androidx.browser:browser:1.0.0 -> 1.5.0 (*)
|    |    \--- com.google.zxing:core:3.3.3
|    +--- com.facebook.android:facebook-login:8.2.0
|    |    +--- com.facebook.android:facebook-core:8.2.0 (*)
|    |    +--- com.facebook.android:facebook-common:8.2.0 (*)
|    |    \--- androidx.appcompat:appcompat:1.1.0 -> 1.6.1 (*)
|    +--- com.facebook.android:facebook-share:8.2.0
|    |    +--- com.facebook.android:facebook-core:8.2.0 (*)
|    |    +--- com.facebook.android:facebook-common:8.2.0 (*)
|    |    \--- androidx.appcompat:appcompat:1.1.0 -> 1.6.1 (*)
|    +--- com.facebook.android:facebook-applinks:8.2.0
|    |    +--- com.facebook.android:facebook-core:8.2.0 (*)
|    |    +--- com.parse.bolts:bolts-android:1.4.0 (*)
|    |    +--- androidx.annotation:annotation:1.1.0 -> 1.3.0
|    |    \--- androidx.legacy:legacy-support-core-utils:1.0.0 (*)
|    +--- com.facebook.android:facebook-messenger:8.2.0
|    |    +--- com.facebook.android:facebook-core:8.2.0 (*)
|    |    \--- com.parse.bolts:bolts-android:1.4.0 (*)
|    +--- com.facebook.android:facebook-gamingservices:8.2.0
|    |    +--- com.facebook.android:facebook-core:8.2.0 (*)
|    |    +--- com.facebook.android:facebook-common:8.2.0 (*)
|    |    \--- com.facebook.android:facebook-share:8.2.0 (*)
|    \--- org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.3.31 -> 1.8.20 (*)
+--- com.facebook.shimmer:shimmer:0.5.0
|    \--- androidx.annotation:annotation:1.0.1 -> 1.3.0
+--- io.reactivex.rxjava2:rxkotlin:2.4.0
|    +--- io.reactivex.rxjava2:rxjava:2.2.10 -> 2.2.19 (*)
|    \--- org.jetbrains.kotlin:kotlin-stdlib:1.3.40 -> 1.9.24 (*)
+--- io.reactivex.rxjava2:rxandroid:2.1.1
|    \--- io.reactivex.rxjava2:rxjava:2.2.6 -> 2.2.19 (*)
+--- com.squareup.retrofit2:retrofit:2.9.0 (*)
+--- com.squareup.retrofit2:converter-gson:2.9.0 (*)
+--- com.squareup.okhttp3:okhttp:4.8.1 -> 4.11.0 (*)
+--- com.squareup.okhttp3:logging-interceptor:4.8.1 -> 4.11.0 (*)
+--- com.amplitude:android-sdk:2.16.0
|    \--- com.squareup.okhttp3:okhttp:3.9.0 -> 4.11.0 (*)
+--- com.appsflyer:af-android-sdk:6.2.3
|    \--- com.appsflyer:oaid:6.2.4
+--- com.google.firebase:firebase-crashlytics -> 18.2.11
|    +--- com.google.android.datatransport:transport-api:3.0.0 (*)
|    +--- com.google.android.datatransport:transport-backend-cct:3.1.5 -> 3.1.6 (*)
|    +--- com.google.android.datatransport:transport-runtime:3.1.5 -> 3.1.6 (*)
|    +--- com.google.android.gms:play-services-tasks:18.0.1 -> 18.2.0 (*)
|    +--- com.google.firebase:firebase-annotations:16.0.0 -> 16.1.0
|    +--- com.google.firebase:firebase-common:20.1.1 (*)
|    +--- com.google.firebase:firebase-components:17.0.0 (*)
|    +--- com.google.firebase:firebase-encoders:17.0.0 (*)
|    +--- com.google.firebase:firebase-encoders-json:18.0.0 (*)
|    +--- com.google.firebase:firebase-installations:17.0.1 (*)
|    +--- com.google.firebase:firebase-installations-interop:17.0.1 (*)
|    \--- com.google.firebase:firebase-measurement-connector:18.0.2 -> 19.0.0 (*)
+--- androidx.paging:paging-runtime-ktx:3.0.0
|    +--- androidx.paging:paging-runtime:3.0.0
|    |    +--- androidx.paging:paging-common:3.0.0
|    |    |    +--- androidx.annotation:annotation:1.1.0 -> 1.3.0
|    |    |    +--- androidx.arch.core:core-common:2.1.0 -> 2.2.0 (*)
|    |    |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.4.31 -> 1.9.24 (*)
|    |    |    \--- org.jetbrains.kotlinx:kotlinx-coroutines-core:1.4.3 -> 1.6.4 (*)
|    |    +--- androidx.paging:paging-common-ktx:3.0.0
|    |    |    \--- androidx.paging:paging-common:3.0.0 (*)
|    |    +--- androidx.lifecycle:lifecycle-livedata-ktx:2.2.0 -> 2.6.1 (*)
|    |    +--- androidx.lifecycle:lifecycle-runtime-ktx:2.2.0 -> 2.6.1 (*)
|    |    +--- androidx.lifecycle:lifecycle-viewmodel-ktx:2.2.0 -> 2.6.1 (*)
|    |    +--- androidx.recyclerview:recyclerview:1.2.0 -> 1.3.0 (*)
|    |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.4.31 -> 1.9.24 (*)
|    |    +--- org.jetbrains.kotlinx:kotlinx-coroutines-android:1.4.3 -> 1.6.4 (*)
|    |    \--- androidx.core:core-ktx:1.2.0 -> 1.8.0 (*)
|    \--- androidx.paging:paging-common-ktx:3.0.0 (*)
+--- com.github.bumptech.glide:glide:4.9.0 -> 4.15.0
|    +--- com.github.bumptech.glide:gifdecoder:4.15.0
|    |    \--- androidx.annotation:annotation:1.3.0
|    +--- com.github.bumptech.glide:disklrucache:4.15.0
|    +--- com.github.bumptech.glide:annotations:4.15.0
|    +--- androidx.fragment:fragment:1.3.6 -> 1.5.7 (*)
|    +--- androidx.vectordrawable:vectordrawable-animated:1.1.0 (*)
|    +--- androidx.exifinterface:exifinterface:1.3.3
|    |    \--- androidx.annotation:annotation:1.2.0 -> 1.3.0
|    \--- androidx.tracing:tracing:1.0.0 (*)
+--- com.hbb20:ccp:2.7.1
|    +--- androidx.recyclerview:recyclerview:1.2.1 -> 1.3.0 (*)
|    +--- androidx.appcompat:appcompat:1.3.1 -> 1.6.1 (*)
|    +--- androidx.core:core:1.6.0 -> 1.8.0 (*)
|    +--- io.michaelrocks:libphonenumber-android:8.12.52
|    \--- androidx.cardview:cardview:1.0.0 (*)
+--- com.github.angga-bw:ShiftColorPicker:c71dbb1a99
|    \--- com.android.support:support-v4:26.1.0 -> androidx.legacy:legacy-support-v4:1.0.0 (*)
+--- com.github.angga-bw:android-simple-tooltip:0598b236e0
|    \--- com.android.support:support-annotations:28.0.0 -> androidx.annotation:annotation:1.3.0
+--- com.airbnb.android:lottie:3.4.2
|    +--- androidx.appcompat:appcompat:1.0.0 -> 1.6.1 (*)
|    \--- com.squareup.okio:okio:1.17.4 -> 3.2.0 (*)
+--- com.github.skydoves:powerspinner:1.0.9
|    +--- androidx.databinding:viewbinding:3.6.3 -> 8.1.4 (*)
|    +--- org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.3.70 -> 1.8.20 (*)
|    +--- androidx.appcompat:appcompat:1.1.0 -> 1.6.1 (*)
|    +--- androidx.recyclerview:recyclerview:1.1.0 -> 1.3.0 (*)
|    \--- androidx.preference:preference:1.1.1
|         +--- androidx.appcompat:appcompat:1.1.0 -> 1.6.1 (*)
|         +--- androidx.core:core:1.1.0 -> 1.8.0 (*)
|         +--- androidx.fragment:fragment:1.2.4 -> 1.5.7 (*)
|         +--- androidx.recyclerview:recyclerview:1.0.0 -> 1.3.0 (*)
|         +--- androidx.annotation:annotation:1.1.0 -> 1.3.0
|         \--- androidx.collection:collection:1.0.0 -> 1.1.0 (*)
+--- net.yslibrary.keyboardvisibilityevent:keyboardvisibilityevent:3.0.0-RC3
|    +--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.4.30 -> 1.8.20 (*)
|    \--- androidx.lifecycle:lifecycle-runtime-ktx:2.3.0 -> 2.6.1 (*)
+--- com.tbuonomo:dotsindicator:4.3
|    +--- androidx.appcompat:appcompat:1.4.1 -> 1.6.1 (*)
|    +--- androidx.dynamicanimation:dynamicanimation:1.0.0 (*)
|    +--- org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.6.21 -> 1.8.20 (*)
|    \--- androidx.viewpager2:viewpager2:1.0.0 (*)
+--- com.journeyapps:zxing-android-embedded:4.1.0
+--- com.google.zxing:core:3.3.0 -> 3.3.3
+--- com.github.PhilJay:MPAndroidChart:v3.0.3
+--- com.github.razir.progressbutton:progressbutton:2.1.0
|    +--- androidx.appcompat:appcompat:1.1.0 -> 1.6.1 (*)
|    +--- androidx.swiperefreshlayout:swiperefreshlayout:1.0.0 -> 1.1.0 (*)
|    \--- org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.3.61 -> 1.8.20 (*)
+--- androidx.camera:camera-camera2:1.1.0-beta02
|    +--- androidx.camera:camera-core:1.1.0-beta02
|    |    +--- androidx.annotation:annotation:1.2.0 -> 1.3.0
|    |    +--- androidx.lifecycle:lifecycle-livedata:2.1.0 -> 2.6.1 (*)
|    |    +--- com.google.guava:listenablefuture:1.0 -> 9999.0-empty-to-avoid-conflict-with-guava
|    |    +--- androidx.annotation:annotation-experimental:1.1.0
|    |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.6.10 -> 1.9.24 (*)
|    |    +--- androidx.exifinterface:exifinterface:1.3.2 -> 1.3.3 (*)
|    |    +--- androidx.core:core:1.1.0 -> 1.8.0 (*)
|    |    +--- androidx.concurrent:concurrent-futures:1.0.0 -> 1.1.0 (*)
|    |    +--- androidx.lifecycle:lifecycle-common:2.1.0 -> 2.6.1 (*)
|    |    \--- com.google.auto.value:auto-value-annotations:1.6.3
|    +--- androidx.annotation:annotation:1.2.0 -> 1.3.0
|    +--- androidx.core:core:1.1.0 -> 1.8.0 (*)
|    +--- androidx.concurrent:concurrent-futures:1.0.0 -> 1.1.0 (*)
|    +--- com.google.guava:listenablefuture:1.0 -> 9999.0-empty-to-avoid-conflict-with-guava
|    \--- com.google.auto.value:auto-value-annotations:1.6.3
+--- androidx.camera:camera-lifecycle:1.1.0-beta02
|    +--- androidx.lifecycle:lifecycle-common:2.1.0 -> 2.6.1 (*)
|    +--- com.google.guava:listenablefuture:1.0 -> 9999.0-empty-to-avoid-conflict-with-guava
|    +--- androidx.camera:camera-core:1.1.0-beta02 (*)
|    +--- androidx.core:core:1.1.0 -> 1.8.0 (*)
|    +--- com.google.auto.value:auto-value-annotations:1.6.3
|    \--- androidx.concurrent:concurrent-futures:1.0.0 -> 1.1.0 (*)
+--- androidx.camera:camera-view:1.1.0-beta02
|    +--- androidx.lifecycle:lifecycle-common:2.0.0 -> 2.6.1 (*)
|    +--- androidx.annotation:annotation:1.2.0 -> 1.3.0
|    +--- androidx.camera:camera-core:1.1.0-beta02 (*)
|    +--- androidx.camera:camera-lifecycle:1.1.0-beta02 (*)
|    +--- androidx.annotation:annotation-experimental:1.1.0-rc01 -> 1.1.0
|    +--- com.google.guava:listenablefuture:1.0 -> 9999.0-empty-to-avoid-conflict-with-guava
|    +--- androidx.core:core:1.3.2 -> 1.8.0 (*)
|    +--- androidx.concurrent:concurrent-futures:1.0.0 -> 1.1.0 (*)
|    +--- com.google.auto.value:auto-value-annotations:1.6.3
|    \--- androidx.appcompat:appcompat:1.1.0 -> 1.6.1 (*)
+--- androidx.camera:camera-video:1.1.0-beta02
|    +--- androidx.annotation:annotation:1.2.0 -> 1.3.0
|    +--- androidx.camera:camera-core:1.1.0-beta02 (*)
|    +--- androidx.core:core:1.1.0 -> 1.8.0 (*)
|    +--- androidx.concurrent:concurrent-futures:1.0.0 -> 1.1.0 (*)
|    \--- com.google.auto.value:auto-value-annotations:1.6.3
+--- org.jetbrains.kotlinx:kotlinx-coroutines-core:1.6.0 -> 1.6.4 (*)
+--- org.jetbrains.kotlinx:kotlinx-coroutines-android:1.6.0 -> 1.6.4 (*)
+--- id.zelory:compressor:3.0.1
|    +--- org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.3.61 -> 1.8.20 (*)
|    \--- org.jetbrains.kotlinx:kotlinx-coroutines-core:1.3.3 -> 1.6.4 (*)
+--- com.google.mlkit:face-detection:16.1.5
|    +--- com.google.android.datatransport:transport-api:2.2.1 -> 3.0.0 (*)
|    +--- com.google.android.datatransport:transport-backend-cct:2.3.3 -> 3.1.6 (*)
|    +--- com.google.android.datatransport:transport-runtime:2.2.6 -> 3.1.6 (*)
|    +--- com.google.android.gms:play-services-base:18.0.1 -> 18.5.0 (*)
|    +--- com.google.android.gms:play-services-basement:18.0.0 -> 18.4.0 (*)
|    +--- com.google.android.gms:play-services-mlkit-face-detection:17.0.1
|    |    +--- com.google.android.datatransport:transport-api:2.2.1 -> 3.0.0 (*)
|    |    +--- com.google.android.datatransport:transport-backend-cct:2.3.3 -> 3.1.6 (*)
|    |    +--- com.google.android.datatransport:transport-runtime:2.2.6 -> 3.1.6 (*)
|    |    +--- com.google.android.gms:play-services-base:18.0.1 -> 18.5.0 (*)
|    |    +--- com.google.android.gms:play-services-basement:18.0.0 -> 18.4.0 (*)
|    |    +--- com.google.android.gms:play-services-tasks:18.0.1 -> 18.2.0 (*)
|    |    +--- com.google.android.odml:image:1.0.0-beta1
|    |    +--- com.google.firebase:firebase-components:16.1.0 -> 17.0.0 (*)
|    |    +--- com.google.firebase:firebase-encoders:16.1.0 -> 17.0.0 (*)
|    |    +--- com.google.firebase:firebase-encoders-json:17.1.0 -> 18.0.0 (*)
|    |    +--- com.google.mlkit:common:18.1.0
|    |    |    +--- androidx.core:core:1.0.0 -> 1.8.0 (*)
|    |    |    +--- com.google.android.datatransport:transport-api:2.2.1 -> 3.0.0 (*)
|    |    |    +--- com.google.android.datatransport:transport-backend-cct:2.3.3 -> 3.1.6 (*)
|    |    |    +--- com.google.android.datatransport:transport-runtime:2.2.6 -> 3.1.6 (*)
|    |    |    +--- com.google.android.gms:play-services-base:18.0.1 -> 18.5.0 (*)
|    |    |    +--- com.google.android.gms:play-services-basement:18.0.0 -> 18.4.0 (*)
|    |    |    +--- com.google.android.gms:play-services-tasks:18.0.1 -> 18.2.0 (*)
|    |    |    +--- com.google.firebase:firebase-components:16.1.0 -> 17.0.0 (*)
|    |    |    +--- com.google.firebase:firebase-encoders:16.1.0 -> 17.0.0 (*)
|    |    |    \--- com.google.firebase:firebase-encoders-json:17.1.0 -> 18.0.0 (*)
|    |    +--- com.google.mlkit:vision-common:17.1.0
|    |    |    +--- androidx.exifinterface:exifinterface:1.0.0 -> 1.3.3 (*)
|    |    |    +--- com.google.android.datatransport:transport-api:2.2.1 -> 3.0.0 (*)
|    |    |    +--- com.google.android.datatransport:transport-backend-cct:2.3.3 -> 3.1.6 (*)
|    |    |    +--- com.google.android.datatransport:transport-runtime:2.2.6 -> 3.1.6 (*)
|    |    |    +--- com.google.android.gms:play-services-base:18.0.1 -> 18.5.0 (*)
|    |    |    +--- com.google.android.gms:play-services-basement:18.0.0 -> 18.4.0 (*)
|    |    |    +--- com.google.android.gms:play-services-tasks:18.0.1 -> 18.2.0 (*)
|    |    |    +--- com.google.android.odml:image:1.0.0-beta1
|    |    |    +--- com.google.firebase:firebase-components:16.1.0 -> 17.0.0 (*)
|    |    |    +--- com.google.firebase:firebase-encoders:16.1.0 -> 17.0.0 (*)
|    |    |    +--- com.google.firebase:firebase-encoders-json:17.1.0 -> 18.0.0 (*)
|    |    |    \--- com.google.mlkit:common:18.1.0 (*)
|    |    \--- com.google.mlkit:vision-interfaces:16.0.0
|    |         +--- com.google.android.gms:play-services-basement:18.0.0 -> 18.4.0 (*)
|    |         \--- com.google.android.gms:play-services-tasks:18.0.1 -> 18.2.0 (*)
|    +--- com.google.android.gms:play-services-tasks:18.0.1 -> 18.2.0 (*)
|    +--- com.google.firebase:firebase-components:16.1.0 -> 17.0.0 (*)
|    +--- com.google.firebase:firebase-encoders:16.1.0 -> 17.0.0 (*)
|    +--- com.google.firebase:firebase-encoders-json:17.1.0 -> 18.0.0 (*)
|    \--- com.google.mlkit:common:18.1.0 (*)
+--- com.squareup.retrofit2:adapter-rxjava2:2.9.0
|    +--- com.squareup.retrofit2:retrofit:2.9.0 (*)
|    +--- io.reactivex.rxjava2:rxjava:2.0.0 -> 2.2.19 (*)
|    \--- org.reactivestreams:reactive-streams:1.0.3
+--- io.reactivex.rxjava2:rxjava:2.2.19 (*)
+--- com.github.kal72:RackMonthPicker:1.6.1
|    +--- androidx.appcompat:appcompat:1.1.0 -> 1.6.1 (*)
|    +--- junit:junit:4.12
|    |    \--- org.hamcrest:hamcrest-core:1.3
|    \--- androidx.recyclerview:recyclerview:1.1.0 -> 1.3.0 (*)
+--- com.github.dewinjm:monthyear-picker:1.0.2
|    \--- com.android.support:appcompat-v7:28.0.0 -> androidx.appcompat:appcompat:1.6.1 (*)
+--- com.github.mrmike:ok2curl:0.8.0
|    +--- com.squareup.okhttp3:okhttp:4.9.3 -> 4.11.0 (*)
|    \--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.6.20 -> 1.8.20 (*)
+--- com.google.android.gms:play-services-maps:18.0.0 -> 18.1.0
|    +--- androidx.fragment:fragment:1.0.0 -> 1.5.7 (*)
|    +--- com.google.android.gms:play-services-base:18.0.1 -> 18.5.0 (*)
|    \--- com.google.android.gms:play-services-basement:18.0.0 -> 18.4.0 (*)
+--- com.google.android.exoplayer:exoplayer:2.15.1
|    +--- com.google.android.exoplayer:exoplayer-core:2.15.1
|    |    +--- androidx.annotation:annotation:1.2.0 -> 1.3.0
|    |    +--- com.google.android.exoplayer:exoplayer-common:2.15.1
|    |    |    +--- androidx.annotation:annotation:1.2.0 -> 1.3.0
|    |    |    \--- com.google.guava:guava:27.1-android -> 31.0.1-jre (*)
|    |    \--- com.google.android.exoplayer:exoplayer-extractor:2.15.1
|    |         +--- androidx.annotation:annotation:1.2.0 -> 1.3.0
|    |         \--- com.google.android.exoplayer:exoplayer-common:2.15.1 (*)
|    +--- com.google.android.exoplayer:exoplayer-dash:2.15.1
|    |    +--- com.google.android.exoplayer:exoplayer-core:2.15.1 (*)
|    |    \--- androidx.annotation:annotation:1.2.0 -> 1.3.0
|    +--- com.google.android.exoplayer:exoplayer-hls:2.15.1
|    |    +--- androidx.annotation:annotation:1.2.0 -> 1.3.0
|    |    \--- com.google.android.exoplayer:exoplayer-core:2.15.1 (*)
|    +--- com.google.android.exoplayer:exoplayer-rtsp:2.15.1
|    |    +--- androidx.annotation:annotation:1.2.0 -> 1.3.0
|    |    \--- com.google.android.exoplayer:exoplayer-core:2.15.1 (*)
|    +--- com.google.android.exoplayer:exoplayer-smoothstreaming:2.15.1
|    |    +--- com.google.android.exoplayer:exoplayer-core:2.15.1 (*)
|    |    \--- androidx.annotation:annotation:1.2.0 -> 1.3.0
|    +--- com.google.android.exoplayer:exoplayer-transformer:2.15.1
|    |    +--- androidx.annotation:annotation:1.2.0 -> 1.3.0
|    |    \--- com.google.android.exoplayer:exoplayer-core:2.15.1 (*)
|    \--- com.google.android.exoplayer:exoplayer-ui:2.15.1
|         +--- com.google.android.exoplayer:exoplayer-core:2.15.1 (*)
|         +--- androidx.annotation:annotation:1.2.0 -> 1.3.0
|         +--- androidx.recyclerview:recyclerview:1.2.1 -> 1.3.0 (*)
|         \--- androidx.media:media:1.3.1 (*)
+--- com.google.android.exoplayer:exoplayer-hls:2.15.1 (*)
+--- com.google.android.exoplayer:exoplayer-ui:2.15.1 (*)
+--- com.auth0.android:jwtdecode:2.0.1
|    +--- androidx.annotation:annotation:1.1.0 -> 1.3.0
|    \--- com.google.code.gson:gson:2.8.9 -> 2.10.1
+--- com.google.firebase:firebase-perf -> 20.1.0
|    +--- androidx.annotation:annotation:1.1.0 -> 1.3.0
|    +--- androidx.appcompat:appcompat:1.2.0 -> 1.6.1 (*)
|    +--- com.google.android.datatransport:transport-api:3.0.0 (*)
|    +--- com.google.android.gms:play-services-tasks:18.0.1 -> 18.2.0 (*)
|    +--- com.google.dagger:dagger:2.27 -> 2.48 (*)
|    +--- com.google.firebase:firebase-common:20.1.1 (*)
|    +--- com.google.firebase:firebase-components:17.0.0 (*)
|    +--- com.google.firebase:firebase-config:21.0.2 -> 21.1.0 (*)
|    +--- com.google.firebase:firebase-datatransport:18.1.4 -> 18.1.5 (*)
|    +--- com.google.firebase:firebase-installations:17.0.1 (*)
|    +--- com.google.firebase:firebase-installations-interop:17.0.1 (*)
|    +--- com.google.firebase:protolite-well-known-types:18.0.0 (*)
|    +--- com.google.protobuf:protobuf-javalite:3.17.3 -> 3.19.2
|    \--- com.squareup.okhttp3:okhttp:3.12.1 -> 4.11.0 (*)
+--- com.google.firebase:firebase-perf-ktx -> 20.1.0
|    +--- androidx.annotation:annotation:1.1.0 -> 1.3.0
|    +--- com.google.firebase:firebase-common:20.1.1 (*)
|    +--- com.google.firebase:firebase-common-ktx:20.1.1 (*)
|    +--- com.google.firebase:firebase-components:17.0.0 (*)
|    +--- com.google.firebase:firebase-perf:20.1.0 (*)
|    \--- org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.3.72 -> 1.8.20 (*)
+--- com.zoho.salesiq:mobilisten:7.1.1
|    +--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.20 (*)
|    +--- androidx.appcompat:appcompat:1.6.1 (*)
|    +--- androidx.recyclerview:recyclerview:1.3.0 (*)
|    +--- androidx.cardview:cardview:1.0.0 (*)
|    +--- com.google.android.material:material:1.9.0 (*)
|    +--- androidx.dynamicanimation:dynamicanimation:1.0.0 (*)
|    +--- androidx.browser:browser:1.5.0 (*)
|    +--- androidx.constraintlayout:constraintlayout:2.1.4 (*)
|    +--- androidx.swiperefreshlayout:swiperefreshlayout:1.1.0 (*)
|    +--- androidx.fragment:fragment-ktx:1.5.7 (*)
|    +--- androidx.room:room-runtime:2.5.2 (*)
|    +--- androidx.lifecycle:lifecycle-livedata-ktx:2.6.1 (*)
|    +--- androidx.room:room-ktx:2.5.2 (*)
|    +--- com.github.bumptech.glide:glide:4.15.0 (*)
|    +--- com.squareup.retrofit2:retrofit:2.9.0 (*)
|    +--- com.squareup.okhttp3:okhttp:4.11.0 (*)
|    +--- com.squareup.okhttp3:logging-interceptor:4.9.0 -> 4.11.0 (*)
|    +--- com.squareup.retrofit2:converter-gson:2.9.0 (*)
|    +--- org.jetbrains.kotlinx:kotlinx-coroutines-core:1.6.4 (*)
|    +--- com.google.android.gms:play-services-maps:18.1.0 (*)
|    +--- com.google.android.gms:play-services-location:21.0.1 (*)
|    \--- androidx.webkit:webkit:1.7.0
|         +--- androidx.annotation:annotation:1.2.0 -> 1.3.0
|         \--- androidx.core:core:1.1.0 -> 1.8.0 (*)
+--- com.google.dagger:hilt-android:2.48 (*)
+--- com.gu.android:toolargetool:0.3.0
|    +--- androidx.appcompat:appcompat:1.2.0 -> 1.6.1 (*)
|    \--- org.jetbrains.kotlin:kotlin-stdlib:1.4.20 -> 1.9.24 (*)
+--- com.survicate:survicate-sdk:1.7.6
|    +--- androidx.appcompat:appcompat:1.4.1 -> 1.6.1 (*)
|    +--- androidx.cardview:cardview:1.0.0 (*)
|    +--- androidx.recyclerview:recyclerview:1.2.1 -> 1.3.0 (*)
|    +--- androidx.constraintlayout:constraintlayout:2.1.4 (*)
|    +--- androidx.transition:transition:1.4.1 (*)
|    +--- androidx.annotation:annotation:1.3.0
|    +--- com.squareup.moshi:moshi:1.12.0
|    |    +--- com.squareup.okio:okio:2.10.0 -> 3.2.0 (*)
|    |    \--- org.jetbrains.kotlin:kotlin-stdlib:1.4.31 -> 1.9.24 (*)
|    +--- com.squareup.moshi:moshi-kotlin:1.12.0
|    |    +--- com.squareup.moshi:moshi:1.12.0 (*)
|    |    +--- org.jetbrains.kotlin:kotlin-reflect:1.4.31 -> 1.5.31 (*)
|    |    \--- org.jetbrains.kotlin:kotlin-stdlib:1.4.31 -> 1.9.24 (*)
|    \--- org.jetbrains.kotlinx:kotlinx-coroutines-android:1.3.9 -> 1.6.4 (*)
\--- org.jetbrains.kotlin:kotlin-parcelize-runtime:1.9.24 (*)

(c) - A dependency constraint, not a dependency. The dependency affected by the constraint occurs elsewhere in the tree.
(*) - Indicates repeated occurrences of a transitive dependency subtree. Gradle expands transitive dependency subtrees only once per project; repeat occurrences only display the root of the subtree, followed by this annotation.

A web-based, searchable dependency report is available by adding the --scan option.

BUILD SUCCESSFUL in 2s
1 actionable task: 1 executed
