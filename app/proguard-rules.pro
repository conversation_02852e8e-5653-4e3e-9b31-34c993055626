# Add project specific ProGuard rules here.
# You can control the set of applied configuration files using the
# proguardFiles setting in build.gradle.
#
# For more details, see
#   http://developer.android.com/guide/developing/tools/proguard.html

# If your project uses WebView with JS, uncomment the following
# and specify the fully qualified class name to the JavaScript interface
# class:
#-keepclassmembers class fqcn.of.javascript.interface.for.webview {
#   public *;
#}

# Uncomment this to preserve the line number information for
# debugging stack traces.
#-keepattributes SourceFile,LineNumberTable

# If you keep the line number information, uncomment this to
# hide the original source file name.
#-renamesourcefileattribute SourceFile

-optimizationpasses 5
-dontusemixedcaseclassnames
-dontskipnonpubliclibraryclasses
-dontskipnonpubliclibraryclassmembers
-dontpreverify
-verbose
-optimizations !code/simplification/arithmetic,!field/*,!class/merging/*,!code/allocation/variable

##--------------- Begin: BukuWarung ---------------
-keep class com.google.firebase.**
-dontwarn com.google.android.gms.location.**
-keep class com.google.android.gms.location.** { *; }

# Kotlin reflection and desugaring compatibility
-keep class kotlin.reflect.** { *; }
-keep class kotlin.reflect.jvm.internal.** { *; }
-dontwarn kotlin.reflect.jvm.internal.**
-keep class j$.util.List$-EL { *; }
-dontwarn j$.util.List$-EL
-keep class j$.util.** { *; }
-dontwarn j$.util.**

# API entities
-keep class com.bukuwarung.data.password.api.model.** { *; }
-keep class com.bukuwarung.activities.edc.orderdetail.model.** { *; }
-keep class com.bukuwarung.api.model.** { *; }
-keep class com.bukuwarung.model.** { *; }
-keep class com.bukuwarung.activities.geolocation.data.model.** { *; }
-keep class com.bukuwarung.api.ApiService { *; }
-keep class com.bukuwarung.activities.transactionreport.download.** { *; }
-keep class com.bukuwarung.activities.customer.download.** { *; }
-keep class com.bukuwarung.payments.data.** { *; }
-keep class com.bukuwarung.los.model.** { *; }
-keep class com.bukuwarung.enums.** { *; }
-keep public class com.bukuwarung.android.sdk.** { public protected *; }
-keep class com.bukuwarung.streaks.data.model.** { *; }
-keep class com.bukuwarung.activities.profile.model.ProgressBarItemLiannya { *; }
-keep class com.bukuwarung.activities.print.NotesMissionStep { *; }
-keep class com.bukuwarung.activities.businessdashboard.model.BusinessDashboardCard { *; }
-keep class com.bukuwarung.activities.businessdashboard.model.* { *;}

# Database entities
-keep class com.bukuwarung.database.dto.** { *; }
-keep class com.bukuwarung.database.entity.** { *; }
-keep class com.bukuwarung.location.UserLocationDto { *; }
-keep class com.bukuwarung.database.helper.Category { *; }

# Entities saved in Local Data
-keep class com.bukuwarung.dialogs.selectableobjectdialog.SelectableObject { *; }
-keep class com.bukuwarung.dialogs.businessselector.BusinessType { *; }
-keep class com.bukuwarung.activities.expense.category.** { *; }

# App Signature
-keep class com.bukuwarung.activities.onboarding.helper.AppSignatureHelper { *; }

-keep class com.bukuwarung.base_android.DataBinderMapperImpl { *; }
-keep class * extends androidx.fragment.app.Fragment{}
-keepnames class * extends android.os.Parcelable
-keepnames class * extends java.io.Serializable
-keep class kotlinx.** { *; }

#-printmapping out.map
-keepparameternames
-renamesourcefileattribute SourceFile
-keepattributes Exceptions,InnerClasses,Signature,Deprecated,SourceFile,LineNumberTable,EnclosingMethod

# Preserve all annotations.

-keepattributes *Annotation*

# Preserve all .class method names.

-keepclassmembernames class * {
    java.lang.Class class$(java.lang.String);
    java.lang.Class class$(java.lang.String, boolean);
}

# Preserve all native method names and the names of their classes.

-keepclasseswithmembernames class * {
    native <methods>;
}

# Preserve the special static methods that are required in all enumeration
# classes.

-keepclassmembers class * extends java.lang.Enum {
    public static **[] values();
    public static ** valueOf(java.lang.String);
}

# Explicitly preserve all serialization members. The Serializable interface
# is only a marker interface, so it wouldn't save them.
# You can comment this out if your library doesn't use serialization.
# If your code contains serializable classes that have to be backward
# compatible, please refer to the manual.

-keepclassmembers class * implements java.io.Serializable {
    static final long serialVersionUID;
    static final java.io.ObjectStreamField[] serialPersistentFields;
    private void writeObject(java.io.ObjectOutputStream);
    private void readObject(java.io.ObjectInputStream);
    java.lang.Object writeReplace();
    java.lang.Object readResolve();
}

##--------------- End: BukuWarung ---------------

-keep class com.delight.**  { *; }
-keep class com.google.firebase.iid.FirebaseInstanceIdService
##--------------- End: Push Amplification ---------------

##--------------- Begin: Amplitude Related ---------------
-keep class com.google.android.gms.ads.** { *; }
-dontwarn okio.**
##--------------- End: Amplitude ---------------
##--------------- Begin: FCM Related ---------------
-keep class com.facebook.**
##--------------- End: FCM Related ---------------

##--------------- Begin: Retrofit ---------------

# Retrofit does reflection on generic parameters. InnerClasses is required to use Signature and
# EnclosingMethod is required to use InnerClasses.
-keepattributes Signature, InnerClasses, EnclosingMethod

# Retrofit does reflection on method and parameter annotations.
-keepattributes RuntimeVisibleAnnotations, RuntimeVisibleParameterAnnotations

# Retain service method parameters when optimizing.
-keepclassmembers,allowshrinking,allowobfuscation interface * {
    @retrofit2.http.* <methods>;
}

# Ignore annotation used for build tooling.
-dontwarn org.codehaus.mojo.animal_sniffer.IgnoreJRERequirement

# Ignore JSR 305 annotations for embedding nullability information.
-dontwarn javax.annotation.**

# Guarded by a NoClassDefFoundError try/catch and only used when on the classpath.
-dontwarn kotlin.Unit

# Top-level functions that can only be used by Kotlin.
-dontwarn retrofit2.KotlinExtensions

# With R8 full mode, it sees no subtypes of Retrofit interfaces since they are created with a Proxy
# and replaces all potential values with null. Explicitly keeping the interfaces prevents this.
-if interface * { @retrofit2.http.* <methods>; }
-keep,allowobfuscation interface <1>

##--------------- End: Retrofit ---------------

##--------------- Begin: OkHttp3 ---------------
-keepattributes Signature
-keepattributes *Annotation*
-keep class okhttp3.** { *; }
-keep interface okhttp3.** { *; }
-dontwarn okhttp3.**
-dontnote okhttp3.**
##--------------- End: OkHttp3 ---------------

##--------------- Begin: Okio ---------------
-keep class sun.misc.Unsafe { *; }
-dontwarn java.nio.file.*
-dontwarn org.codehaus.mojo.animal_sniffer.IgnoreJRERequirement
##--------------- End: Okio ---------------

##--------------- Begin: Gson ---------------
-keep class sun.misc.Unsafe { *; }
-keep class com.google.gson.stream.** { *; }
##--------------- End: Gson ---------------

# Prevent proguard from stripping interface information from TypeAdapter, TypeAdapterFactory,
# JsonSerializer, JsonDeserializer instances (so they can be used in @JsonAdapter)
-keep class * extends com.google.gson.TypeAdapter
-keep class * implements com.google.gson.TypeAdapterFactory
-keep class * implements com.google.gson.JsonSerializer
-keep class * implements com.google.gson.JsonDeserializer

# Keep native methods
-keepclassmembers class * {
    native <methods>;
}
##------------ Begin: Uninstall Appsflyer--------
-dontwarn com.appsflyer.**
-keep public class com.google.firebase.messaging.FirebaseMessagingService {
  public *;
}
##------------ End: Uninstall Appsflyer----------

-keep public class com.bukuwarung.activities.onboarding.WelcomeScreens { * ;}
-keep public class com.bukuwarung.activities.bulktransaction.data.BulkTransactionData { *;}
-keep public class com.bukuwarung.activities.profile.ProfilePins { *;}
-keep public class com.bukuwarung.activities.expense.data.* { *;}
-keep public class com.bukuwarung.activities.WebviewActivity { *;}
-keep public class com.bukuwarung.activities.homepage.data.* { *;}
-keep public class com.bukuwarung.activities.transaction.* { *;}
-keep public class com.bukuwarung.activities.autorecordtransaction.* { *; }

-keep public class com.bukuwarung.payments.data.model.* {*; }
-keep public class com.bukuwarung.payments.data.model.QrisResponse {*; }
-keep public class com.bukuwarung.analytics.appsflyer.data.model.* {*; }

-keep public class com.bukuwarung.activities.onboarding.* { *;}
-keep public class com.bukuwarung.referral.model.* { *;}
-keep public class com.bukuwarung.referral.model.ReferralDataResponse { *;}
-keep public class com.bukuwarung.data.referral.** {*;}
-keep public class com.bukuwarung.activities.home.data.** {*;}
-keep public class com.bukuwarung.activities.supplier.model.* { *; }
-keep public class com.bukuwarung.game.model.GameResponse { *; }
-keep public class com.bukuwarung.datasync.model.*{*;}
-keep public class com.bukuwarung.session.* { *;}
-keep public class com.bukuwarung.activities.homepage.data.RefreeEntryPointData.* {*;}


-keep public class com.survicate.surveys.entities.**
-keep public class com.survicate.surveys.infrastructure.network.**
-keep public class com.survicate.surveys.infrastructure.serialization.**

-keep,allowoptimization class * implements androidx.viewbinding.ViewBinding {
    public static *** bind(android.view.View);
    public static *** inflate(...);
}

-keepclassmembers,allowobfuscation class * {
    @com.google.gson.annotations.SerializedName <fields>;
}

-keep class * implements java.io.Serializable { *; }

# Smartech/Netcore Base SDK
-dontwarn com.netcore.android.**
-keep class com.netcore.android.**{*;}
-keep class * implements com.netcore.android.**.* {*;}
-keep class * extends com.netcore.android.**.* {*;}

#Hansel SDK
-dontwarn io.hansel.**
-keep class io.hansel.**{*;}
-keep class * implements io.hansel.**.* {*;}
-keep class * extends io.hansel.**.* {*;}

-keep class com.netcore.views.** { *; }
-keep class com.android.volley.** { *; }
-keep class com.android.volley.VolleyError { *;}

-keepattributes *Annotation*
-keepclassmembers class ** {
    @org.greenrobot.eventbus.Subscribe <methods>;
}
-keep enum org.greenrobot.eventbus.ThreadMode { *; }

-keep class com.bukuwarung.model.request** { *; }
-keep class com.bukuwarung.model.response** { *; }

# This is also needed for R8 in compat mode since multiple
# optimizations will remove the generic signature such as class
# merging and argument removal.
-keep class com.google.gson.reflect.TypeToken { *; }
-keep class * extends com.google.gson.reflect.TypeToken
