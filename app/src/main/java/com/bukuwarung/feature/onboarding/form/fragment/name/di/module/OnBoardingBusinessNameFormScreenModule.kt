package com.bukuwarung.feature.onboarding.form.fragment.name.di.module

import androidx.lifecycle.SavedStateHandle
import com.bukuwarung.base.udf.api.state.StateManager
import com.bukuwarung.base.udf.implementation.state.DefaultStateManager
import com.bukuwarung.feature.onboarding.form.fragment.name.screen.OnBoardingBusinessNameFormState
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.components.ViewModelComponent

@Module
@InstallIn(ViewModelComponent::class)
object OnBoardingBusinessNameFormScreenModule {
    @Provides
    fun provideOnBoardingBusinessNameFormState(): OnBoardingBusinessNameFormState {
        return OnBoardingBusinessNameFormState(
            businessName = "",
            businessNameErrorMessage = "",
            errorMessage = "",
            isProgressCompleted = false,
        )
    }

    @Provides
    fun provideStateManager(
        initState: OnBoardingBusinessNameFormState,
        savedStateHandle: SavedStateHandle,
    ): StateManager<OnBoardingBusinessNameFormState> {
        return DefaultStateManager(initState, savedStateHandle)
    }
}
