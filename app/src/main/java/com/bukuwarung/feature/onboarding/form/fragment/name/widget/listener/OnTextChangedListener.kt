package com.bukuwarung.feature.onboarding.form.fragment.name.widget.listener

import android.text.Editable
import android.text.TextWatcher

class OnTextChangedListener(
    private val action: (text: String) -> Unit,
) : TextWatcher {
    override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}

    override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}

    override fun afterTextChanged(editor: Editable?) {
        action(editor?.toString().orEmpty())
    }
}
