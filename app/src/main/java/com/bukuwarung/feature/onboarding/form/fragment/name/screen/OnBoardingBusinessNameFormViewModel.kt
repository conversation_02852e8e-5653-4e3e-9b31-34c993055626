package com.bukuwarung.feature.onboarding.form.fragment.name.screen

import androidx.lifecycle.viewModelScope
import com.bukuwarung.R
import com.bukuwarung.base.lector.api.Lector
import com.bukuwarung.base.remote.api.NetworkMonitor
import com.bukuwarung.base.telemetry.api.Telemetry
import com.bukuwarung.base.telemetry.di.qualifier.AmplitudeTelemetry
import com.bukuwarung.base.telemetry.di.qualifier.AppsFlyerTelemetry
import com.bukuwarung.base.telemetry.di.qualifier.FirebaseTelemetry
import com.bukuwarung.base.udf.api.screen.UdfViewModel
import com.bukuwarung.base.udf.api.state.StateManager
import com.bukuwarung.data.risk.api.RiskRepository
import com.bukuwarung.data.risk.api.model.BusinessBlacklistedError
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class OnBoardingBusinessNameFormViewModel @Inject constructor(
    stateManager: StateManager<OnBoardingBusinessNameFormState>,
    private val lector: Lector,
    private val networkMonitor: NetworkMonitor,
    private val riskRepository: RiskRepository,
    @AmplitudeTelemetry private val amplitudeTelemetry: Telemetry,
    @AppsFlyerTelemetry private val appsFlyerTelemetry: Telemetry,
    @FirebaseTelemetry private val firebaseTelemetry: Telemetry,
) : UdfViewModel<OnBoardingBusinessNameFormState>(stateManager) {
    fun updateBusinessName(businessName: String) {
        produce { state ->
            state.copy(
                businessName = businessName,
                businessNameErrorMessage = "",
            )
        }
    }

    fun clearErrorMessage() {
        produce { state ->
            state.copy(errorMessage = "")
        }
    }

    fun validateBusinessName(businessName: String) {
        viewModelScope.launch {
            if (checkIfFormatInvalid(businessName)) return@launch
            if (checkIfBlacklisted(businessName)) return@launch

            setProgressCompleted()
        }
    }

    private fun checkIfFormatInvalid(businessName: String): Boolean {
        val isValid = businessName.matches(regex = "[A-Za-z0-9 ]*".toRegex())
        if (isValid) return false

        produce { state ->
            val message = lector.literate(
                resId = R.string.business_name_invalid_char_message,
                "(^\$*.[]{}()?-\"!#%&/\\,><':;|_~`) \uD83D\uDE03",
            )
            state.copy(businessNameErrorMessage = message)
        }
        return true
    }

    private suspend fun checkIfBlacklisted(businessName: String): Boolean {
        if (!networkMonitor.isConnected) {
            val message = lector.literate(resId = R.string.no_internet_error)
            produce { state ->
                state.copy(errorMessage = message)
            }
            return true
        }

        val response = riskRepository.validateBusinessName(businessName)
        if (response !is BusinessBlacklistedError) return false

        produce { state ->
            state.copy(businessNameErrorMessage = response.message)
        }
        return true
    }

    private fun setProgressCompleted() {
        produce { state ->
            state.copy(isProgressCompleted = true)
        }
    }

    fun recordAnalytics(event: String, parameter: Map<String, Any>) {
        amplitudeTelemetry.record(event, parameter)
        appsFlyerTelemetry.record(event, parameter)
        firebaseTelemetry.record(event, parameter)
    }
}
