package com.bukuwarung.feature.onboarding.form.screen

import com.bukuwarung.data.onboarding.api.model.OnBoardingCategory
import com.bukuwarung.data.onboarding.api.model.OnBoardingForm
import com.bukuwarung.data.onboarding.api.model.OnBoardingRedirectionPolicy
import java.io.Serializable

data class OnBoardingFormState(
    val forms: List<OnBoardingForm>?,
    val totalPages: Int,
    val currentPage: Int,
    val isFormCompleted: Boolean,
    val isHandleNavigation: Boolean,
    val selectedBusinessName: String,
    val selectedBusinessCategory: OnBoardingCategory?,
    val selectedBusinessGoal: OnBoardingCategory?,
    val selectedBusinessPastUsage: OnBoardingCategory?,
    val tempBusinessName: String,
    val tempBusinessCategory: OnBoardingCategory?,
    val tempBusinessGoal: OnBoardingCategory?,
    val tempBusinessPastUsage: OnBoardingCategory?,
    val onBoardingRedirectionPolicy: OnBoardingRedirectionPolicy?,
    val tabRedirection: String,
) : Serializable
