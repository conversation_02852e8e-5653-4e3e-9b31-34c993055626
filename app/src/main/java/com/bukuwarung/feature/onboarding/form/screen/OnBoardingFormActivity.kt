package com.bukuwarung.feature.onboarding.form.screen

import android.os.Bundle
import androidx.activity.viewModels
import androidx.fragment.app.Fragment
import com.bukuwarung.activities.onboarding.GeneralLoadingDialog
import com.bukuwarung.activities.onboarding.form.StepIndicator
import com.bukuwarung.base.neuro.api.route
import com.bukuwarung.base.udf.api.screen.UdfActivity
import com.bukuwarung.base.udf.api.screen.viewBinding
import com.bukuwarung.base.udf.api.state.observeState
import com.bukuwarung.data.onboarding.api.model.OnBoardingCategory
import com.bukuwarung.data.onboarding.api.model.OnBoardingForm
import com.bukuwarung.data.onboarding.api.model.OnBoardingRedirectionPolicy
import com.bukuwarung.databinding.ActivityOnBoardingNewFormBinding
import com.bukuwarung.feature.onboarding.form.adapter.OnBoardingFormPagerAdapter
import com.bukuwarung.feature.onboarding.form.fragment.category.screen.OnBoardingBusinessCategoryFormFragment
import com.bukuwarung.feature.onboarding.form.fragment.goal.screen.OnBoardingBusinessGoalFormFragment
import com.bukuwarung.feature.onboarding.form.fragment.name.screen.OnBoardingBusinessNameFormFragment
import com.bukuwarung.feature.onboarding.form.fragment.past.screen.OnBoardingBusinessPastUsageFormFragment
import com.bukuwarung.neuro.api.Neuro
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class OnBoardingFormActivity : UdfActivity() {
    @Inject
    lateinit var neuro: Neuro

    private val viewBinding: ActivityOnBoardingNewFormBinding by viewBinding()

    private val viewModel: OnBoardingFormViewModel by viewModels()

    override fun onBackPressed() {}

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(viewBinding.root)

        setupView()
        observeState(source = viewModel.stateFlow, action = ::render)

        viewModel.fetchOnBoardingForms()
        viewModel.fetchOnBoardingRedirectionPolicy()
        viewModel.fetchTabRedirection()

        // Post registration
        viewModel.createUserAccount()
        viewModel.recordAnalytics(
            event = "success_registration",
            parameter = mapOf(),
        )
        viewModel.trackActivity(
            activity = this,
            event = "success_registration",
            parameter = mapOf()
        )
    }

    private fun setupView() {
        viewBinding.viewPager.also { viewPager ->
            viewPager.disableSwipe()
        }
    }

    private fun render(state: OnBoardingFormState) {
        handleFormCompletion(state)
        handleNavigation(state)

        renderFormPager(state)
        renderFormPage(state)
        renderPageIndicator(state)
    }

    private fun renderFormPager(state: OnBoardingFormState) {
        val totalPages = state.totalPages
        if (totalPages >= 0) return

        val forms = state.forms ?: return
        val formPages = mutableListOf<Fragment>()
        var totalValidPages = 0

        forms.forEach { form ->
            when (form) {
                OnBoardingForm.BusinessName -> {
                    formPages.add(OnBoardingBusinessNameFormFragment())
                    totalValidPages++
                }
                OnBoardingForm.BusinessCategory -> {
                    formPages.add(OnBoardingBusinessCategoryFormFragment())
                    totalValidPages++
                }
                OnBoardingForm.BusinessGoal -> {
                    formPages.add(OnBoardingBusinessGoalFormFragment())
                    totalValidPages++
                }
                OnBoardingForm.BusinessPastUsage -> {
                    formPages.add(OnBoardingBusinessPastUsageFormFragment())
                    totalValidPages++
                }
                else -> {}
            }
        }

        viewBinding.viewPager.also { viewPager ->
            viewPager.adapter = OnBoardingFormPagerAdapter(
                fragmentManager = supportFragmentManager,
                pageList = formPages,
            )
        }

        viewModel.setTotalPages(totalValidPages)
        viewModel.recordAnalytics(
            event = "open_business_detail_dialog",
            parameter = mapOf("page_type" to forms.joinToString("_") { it.id }),
        )
    }

    private fun renderFormPage(state: OnBoardingFormState) {
        val totalPages = state.totalPages
        if (totalPages < 0) return

        val currentPage = state.currentPage
        viewBinding.viewPager.also { viewPager ->
            viewPager.setCurrentItem(currentPage, true)
        }
    }

    private fun renderPageIndicator(state: OnBoardingFormState) {
        val totalPages = state.totalPages
        if (totalPages < 0) return

        val currentPage = state.currentPage
        viewBinding.stepContainer.also { container ->
            container.removeAllViews()

            repeat(totalPages) { page ->
                val indicator = StepIndicator(context = this).apply {
                    setIndicatorEnabled(page <= currentPage)
                }
                container.addView(indicator)
            }
        }
    }

    private fun handleFormCompletion(state: OnBoardingFormState) {
        if (!state.isFormCompleted) return
        if (state.isHandleNavigation) return
        showLoadingDialog()

        recordAnalytics(
            businessName = state.selectedBusinessName,
            businessCategory = state.selectedBusinessCategory,
            businessGoal = state.selectedBusinessGoal,
            businessPastUsage = state.selectedBusinessPastUsage,
            redirectionPolicy = state.onBoardingRedirectionPolicy,
            tabRedirection = state.tabRedirection,
        )

        finalize(
            businessName = state.selectedBusinessName,
            businessCategory = state.selectedBusinessCategory,
        )
    }

    private fun handleNavigation(state: OnBoardingFormState) {
        if (!state.isHandleNavigation) return

        neuro.route(
            context = this,
            path = "/main",
            query = mapOf(
                "IS_NEW_LOGIN_EXTRA" to true,
                "tab_name" to "HOME",
            ),
            navigator = ::startActivity,
        )
        finish()
    }

    private fun showLoadingDialog() {
        GeneralLoadingDialog.createInstance().show(supportFragmentManager, GeneralLoadingDialog.TAG)
    }

    private fun recordAnalytics(
        businessName: String,
        businessCategory: OnBoardingCategory?,
        businessGoal: OnBoardingCategory?,
        businessPastUsage: OnBoardingCategory?,
        redirectionPolicy: OnBoardingRedirectionPolicy?,
        tabRedirection: String,
    ) {
        viewModel.recordAnalytics(
            event = "landing_popup",
            parameter = mapOf(
                "business_name" to businessName,
                "business_type_name" to businessCategory?.name.orEmpty(),
                "type" to businessCategory?.resourceId.orEmpty(),
                "usage_goal" to businessGoal?.name.orEmpty(),
                "usage_past" to businessPastUsage?.name.orEmpty(),
            ),
        )

        viewModel.recordProfile(
            parameter = mapOf(
                "business_name" to businessName,
                "business_type" to businessCategory?.resourceId.orEmpty(),
                "business_type_name" to businessCategory?.name.orEmpty(),
                "usage_goal" to businessGoal?.name.orEmpty(),
                "usage_past" to businessPastUsage?.name.orEmpty()
            ),
        )
    }

    private fun finalize(businessName: String, businessCategory: OnBoardingCategory?) {
        viewModel.syncBusiness(businessName, businessCategory)
        viewModel.postAppsFlyerId()
    }
}
