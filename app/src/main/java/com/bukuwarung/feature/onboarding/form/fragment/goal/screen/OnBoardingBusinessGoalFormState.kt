package com.bukuwarung.feature.onboarding.form.fragment.goal.screen

import com.bukuwarung.data.onboarding.api.model.OnBoardingCategory
import java.io.Serializable

data class OnBoardingBusinessGoalFormState(
    val businessGoals: List<OnBoardingCategory>?,
    val isBusinessGoalsInitialized: <PERSON><PERSON>an,
    val selectedBusinessGoal: OnBoardingCategory?,
    val isProgressCompleted: Boolean,
) : Serializable
