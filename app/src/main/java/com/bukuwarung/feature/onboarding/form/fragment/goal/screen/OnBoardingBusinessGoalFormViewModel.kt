package com.bukuwarung.feature.onboarding.form.fragment.goal.screen

import androidx.lifecycle.viewModelScope
import com.bukuwarung.base.data.api.Response
import com.bukuwarung.base.telemetry.api.Telemetry
import com.bukuwarung.base.telemetry.di.qualifier.AmplitudeTelemetry
import com.bukuwarung.base.telemetry.di.qualifier.AppsFlyerTelemetry
import com.bukuwarung.base.telemetry.di.qualifier.FirebaseTelemetry
import com.bukuwarung.base.udf.api.screen.UdfViewModel
import com.bukuwarung.base.udf.api.state.StateManager
import com.bukuwarung.data.onboarding.api.OnBoardingRepository
import com.bukuwarung.data.onboarding.api.model.OnBoardingCategory
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class OnBoardingBusinessGoalFormViewModel @Inject constructor(
    stateManager: StateManager<OnBoardingBusinessGoalFormState>,
    private val onBoardingRepository: OnBoardingRepository,
    @AmplitudeTelemetry private val amplitudeTelemetry: Telemetry,
    @AppsFlyerTelemetry private val appsFlyerTelemetry: Telemetry,
    @FirebaseTelemetry private val firebaseTelemetry: Telemetry,
) : UdfViewModel<OnBoardingBusinessGoalFormState>(stateManager) {
    fun fetchBusinessGoals() {
        viewModelScope.launch {
            val response = onBoardingRepository.fetchOnBoardingBusinessGoals()
            handleResponse(response)
        }
    }

    private fun handleResponse(response: Response<List<OnBoardingCategory>>) {
        produce { state ->
            if (response is Response.Success) {
                state.copy(businessGoals = response.data)
            } else {
                state.copy(businessGoals = listOf())
            }
        }
    }

    fun setBusinessGoalsInitialized() {
        produce { state ->
            state.copy(isBusinessGoalsInitialized = true)
        }
    }

    fun selectBusinessGoal(businessGoal: OnBoardingCategory) {
        produce { state ->
            state.copy(selectedBusinessGoal = businessGoal)
        }
    }

    fun setProgressCompleted() {
        produce { state ->
            state.copy(isProgressCompleted = true)
        }
    }

    fun recordAnalytics(event: String, parameter: Map<String, Any>) {
        amplitudeTelemetry.record(event, parameter)
        appsFlyerTelemetry.record(event, parameter)
        firebaseTelemetry.record(event, parameter)
    }
}
