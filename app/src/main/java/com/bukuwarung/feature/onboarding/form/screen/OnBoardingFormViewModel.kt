package com.bukuwarung.feature.onboarding.form.screen

import android.app.Activity
import androidx.lifecycle.viewModelScope
import com.bukuwarung.base.data.api.Response
import com.bukuwarung.base.telemetry.api.Telemetry
import com.bukuwarung.base.telemetry.di.qualifier.AmplitudeTelemetry
import com.bukuwarung.base.telemetry.di.qualifier.AppsFlyerTelemetry
import com.bukuwarung.base.telemetry.di.qualifier.FirebaseTelemetry
import com.bukuwarung.base.tracker.api.Tracker
import com.bukuwarung.base.tracker.di.qualifier.SurvicateTracker
import com.bukuwarung.base.udf.api.screen.UdfViewModel
import com.bukuwarung.base.udf.api.state.StateManager
import com.bukuwarung.data.analytics.api.AnalyticsRepository
import com.bukuwarung.data.app.api.AppRepository
import com.bukuwarung.data.business.api.BusinessRepository
import com.bukuwarung.data.business.api.model.Business
import com.bukuwarung.data.onboarding.api.OnBoardingRepository
import com.bukuwarung.data.onboarding.api.model.OnBoardingCategory
import com.bukuwarung.data.onboarding.api.model.OnBoardingForm
import com.bukuwarung.data.onboarding.api.model.OnBoardingRedirectionPolicy
import com.bukuwarung.data.user.api.UserRepository
import com.bukuwarung.utils.Utility
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import java.util.Date
import javax.inject.Inject

@HiltViewModel
class OnBoardingFormViewModel @Inject constructor(
    stateManager: StateManager<OnBoardingFormState>,
    private val appRepository: AppRepository,
    private val analyticsRepository: AnalyticsRepository,
    private val businessRepository: BusinessRepository,
    private val onBoardingRepository: OnBoardingRepository,
    private val userRepository: UserRepository,
    @SurvicateTracker private val survicateTracker: Tracker,
    @AmplitudeTelemetry private val amplitudeTelemetry: Telemetry,
    @AppsFlyerTelemetry private val appsFlyerTelemetry: Telemetry,
    @FirebaseTelemetry private val firebaseTelemetry: Telemetry,
) : UdfViewModel<OnBoardingFormState>(stateManager) {
    fun fetchOnBoardingForms() {
        viewModelScope.launch {
            val response = onBoardingRepository.fetchOnBoardingForms()
            handleOnBoardingFormResponse(response)
        }
    }

    fun fetchOnBoardingRedirectionPolicy() {
        viewModelScope.launch {
            val response = onBoardingRepository.fetchOnBoardingRedirectionPolicy()
            handleOnBoardingRedirectionResponse(response)
        }
    }

    fun fetchTabRedirection() {
        viewModelScope.launch {
            val tabName = onBoardingRepository.readTabRedirection()
            handleTabRedirectionResponse(tabName)
        }
    }

    private fun handleOnBoardingFormResponse(response: Response<List<OnBoardingForm>>) {
        produce { state ->
            if (response is Response.Success) {
                state.copy(forms = response.data)
            } else {
                state.copy(forms = listOf())
            }
        }
    }

    private fun handleOnBoardingRedirectionResponse(response: Response<OnBoardingRedirectionPolicy>) {
        if (response !is Response.Success) return
        produce { state ->
            state.copy(onBoardingRedirectionPolicy = response.data)
        }
    }

    private fun handleTabRedirectionResponse(tabName: String) {
        produce { state ->
            state.copy(tabRedirection = tabName)
        }
    }

    fun setTotalPages(totalPages: Int) {
        produce { state ->
            state.copy(totalPages = totalPages)
        }
    }

    fun submitBusinessName(businessName: String) {
        produce { state ->
            state.copy(selectedBusinessName = businessName)
        }
        proceedNextPage()
    }

    fun submitBusinessCategory(businessCategory: OnBoardingCategory) {
        produce { state ->
            state.copy(selectedBusinessCategory = businessCategory)
        }
        proceedNextPage()
    }

    fun submitBusinessGoal(businessGoal: OnBoardingCategory) {
        produce { state ->
            state.copy(selectedBusinessGoal = businessGoal)
        }
        proceedNextPage()
    }

    fun submitBusinessPastUsage(businessPastUsage: OnBoardingCategory) {
        produce { state ->
            state.copy(selectedBusinessPastUsage = businessPastUsage)
        }
        proceedNextPage()
    }

    private fun proceedNextPage() {
        produce { state ->
            val totalPages = state.totalPages
            val currentPage = state.currentPage

            if (currentPage == totalPages - 1) {
                state.copy(isFormCompleted = true)
            } else {
                state.copy(currentPage = currentPage + 1)
            }
        }
    }

    fun recordAnalytics(event: String, parameter: Map<String, Any>) {
        amplitudeTelemetry.record(event, parameter)
        appsFlyerTelemetry.record(event, parameter)
        firebaseTelemetry.record(event, parameter)
    }

    fun recordProfile(parameter: Map<String, Any>) {
        amplitudeTelemetry.recordProfile(parameter)
    }

    fun trackActivity(activity: Activity, event: String, parameter: Map<String, Any>) {
        survicateTracker.track(activity, event, parameter)
    }

    fun createUserAccount() {
        viewModelScope.launch {
            userRepository.saveNewUser(isNewUser = true)

            val business = createDefaultBusiness()
            businessRepository.createBusiness(business)
            businessRepository.storeBusiness(business)
            businessRepository.saveBusinessId(businessId = business.businessId.orEmpty())
            businessRepository.saveBusinessName(businessName = business.name.orEmpty())
            businessRepository.saveBusinessSync(sync = true)

            val isEnabled = onBoardingRepository.fetchStreakFeatureFlag()
            if (isEnabled) onBoardingRepository.saveOnBoardingTutorial(showTutorial = false)
        }
    }

    private suspend fun createDefaultBusiness(): Business {
        val isGuestUser = userRepository.readIsGuestUser()
        val userId = userRepository.readUserId()
        val deviceId = userRepository.readDeviceId()

        return Business.default.copy(
            businessId = Utility.uuid(),
            ownerId = userId,
            ownerName = "BukuWarung",
            category = Business.Category.default.copy(
                categoryId = "-1",
                name = "",
            ),
            name = "Usaha Saya",
            phone = userId.takeIf { !isGuestUser }.orEmpty(),
            createdByDevice = deviceId,
            modifiedByDevice = deviceId,
            createdAt = Date(),
            lastModifiedAt = Date(),
            temp = Business.Temp.default.copy(
                enableSmsAlerts = true,
                businessImageUploadPending = 0,
                createdByUser = userId,
                updatedByUser = userId,
                dirty = 1,
                serverSeq = 0L,
                isGuest = isGuestUser,
            )
        )
    }

    fun syncBusiness(businessName: String, businessCategory: OnBoardingCategory?) {
        viewModelScope.launch {
            appRepository.saveInitialSync(true)
            businessRepository.saveBusinessSync(true)

            userRepository.saveNewUser(isNewUser = true)
            userRepository.saveRequireProfileSetup(false)

            val business = businessRepository.retrieveBusinessById(
                businessId = businessRepository.readBusinessId(),
            )

            val updatedBusiness = business?.copy(
                name = businessName,
                category = business.category?.copy(
                    categoryId = businessCategory?.resourceId,
                    name = businessCategory?.name,
                ),
            )

            updatedBusiness?.let {
                businessRepository.createBusiness(it)
                businessRepository.editBusiness(it)
            }

            produce { state ->
                state.copy(isHandleNavigation = true)
            }
        }
    }

    fun postAppsFlyerId() {
        viewModelScope.launch {
            val userId = userRepository.readUserId()
            val advertisingId = appRepository.readAdvertisingId()

            analyticsRepository.postAppsFlyerId(userId, advertisingId)
        }
    }
}
