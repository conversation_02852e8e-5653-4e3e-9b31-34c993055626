package com.bukuwarung.feature.onboarding.form.fragment.past.screen

import android.os.Bundle
import android.view.View
import androidx.fragment.app.activityViewModels
import androidx.fragment.app.viewModels
import androidx.recyclerview.widget.GridLayoutManager
import com.bukuwarung.R
import com.bukuwarung.base.lector.api.Lector
import com.bukuwarung.base.udf.api.screen.UdfFragment
import com.bukuwarung.base.udf.api.screen.viewBinding
import com.bukuwarung.base.udf.api.state.observeState
import com.bukuwarung.databinding.FragmentOnBoardingBusinessPastUsageFormBinding
import com.bukuwarung.feature.onboarding.form.fragment.adapter.OnBoardingCategoryAdapter
import com.bukuwarung.feature.onboarding.form.newScreen.NewOnBoardingFormViewModel
import com.bukuwarung.feature.onboarding.form.screen.OnBoardingFormViewModel
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class OnBoardingBusinessPastUsageFormFragment :
    UdfFragment(layoutRes = R.layout.fragment_on_boarding_business_past_usage_form) {
    @Inject
    lateinit var lector: Lector

    private val viewBinding: FragmentOnBoardingBusinessPastUsageFormBinding by viewBinding()

    private val viewModel: OnBoardingBusinessPastUsageFormViewModel by viewModels()

    private val onBoardingFormViewModel: OnBoardingFormViewModel by activityViewModels()

    private val adapter: OnBoardingCategoryAdapter by lazy {
        OnBoardingCategoryAdapter(onSelectListener = viewModel::selectBusinessPastUsage)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        setupView()
        viewLifecycleOwner.observeState(source = viewModel.stateFlow, action = ::render)

        viewModel.fetchBusinessPastUsages()
    }

    private fun setupView() {
        viewBinding.headerLabel.also { label ->
            label.text = lector.literate(resId = R.string.business_past_usage)
        }

        viewBinding.subHeaderLabel.also { label ->
            label.text = lector.literate(resId = R.string.business_past_usage_description)
        }

        viewBinding.captionLabel.also { label ->
            label.text = lector.literate(resId = R.string.select_one)
        }

        viewBinding.businessPastUsageList.also { list ->
            list.adapter = adapter
            list.layoutManager = GridLayoutManager(requireContext(), 3)
        }

        viewBinding.businessPastUsageProceedButton.also { button ->
            button.text = lector.literate(resId = R.string.next)
            button.isEnabled = false
        }
    }

    private fun render(state: OnBoardingBusinessPastUsageFormState) {
        handleProgressCompletion(state)

        renderBusinessPastUsages(state)
        renderProceedButton(state)
    }

    private fun handleProgressCompletion(state: OnBoardingBusinessPastUsageFormState) {
        val isProgressCompleted = state.isProgressCompleted
        if (!isProgressCompleted) return

        val selectedBusinessPastUsage = state.selectedBusinessPastUsage ?: return
        onBoardingFormViewModel.submitBusinessPastUsage(businessPastUsage = selectedBusinessPastUsage)

        viewModel.recordAnalytics(
            event = "continue_business_detail_dialogue",
            parameter = mapOf(
                "page_name" to "usage_past",
                "usage_past" to selectedBusinessPastUsage.name,
            ),
        )
    }

    private fun renderBusinessPastUsages(state: OnBoardingBusinessPastUsageFormState) {
        val isInitialized = state.isBusinessPastUsagesInitialized
        if (isInitialized) return

        val businessPastUsages = state.businessPastUsages ?: return
        adapter.submitList(businessPastUsages)

        viewModel.setBusinessPastUsagesInitialized()
    }

    private fun renderProceedButton(state: OnBoardingBusinessPastUsageFormState) {
        viewBinding.businessPastUsageProceedButton.also { button ->
            val selectedBusinessPastUsage = state.selectedBusinessPastUsage
            button.isEnabled = selectedBusinessPastUsage != null

            button.setOnClickListener {
                viewModel.setProgressCompleted()
            }
        }
    }
}
