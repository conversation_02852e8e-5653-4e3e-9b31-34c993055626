package com.bukuwarung.feature.onboarding.form.adapter;

import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.FragmentStatePagerAdapter

class OnBoardingFormPagerAdapter(
    fragmentManager: FragmentManager,
    pagerBehavior: Int = BEHAVIOR_RESUME_ONLY_CURRENT_FRAGMENT,
    private val pageList: List<Fragment>,
) : FragmentStatePagerAdapter(fragmentManager, pagerBehavior) {
    init {
        require(pageList.isNotEmpty()) { "pageList cannot be empty" }
    }

    override fun getCount(): Int = pageList.size

    override fun getItem(position: Int): Fragment {
        if (position !in pageList.indices) return pageList[0]
        return pageList[position]
    }
}
