package com.bukuwarung.feature.onboarding.form.fragment.goal.screen

import android.os.Bundle
import android.view.View
import androidx.fragment.app.activityViewModels
import androidx.fragment.app.viewModels
import androidx.recyclerview.widget.GridLayoutManager
import com.bukuwarung.R
import com.bukuwarung.base.lector.api.Lector
import com.bukuwarung.base.udf.api.screen.UdfFragment
import com.bukuwarung.base.udf.api.screen.viewBinding
import com.bukuwarung.base.udf.api.state.observeState
import com.bukuwarung.databinding.FragmentOnBoardingBusinessGoalFormBinding
import com.bukuwarung.databinding.FragmentOnBoardingBusinessGoalFormMergedBinding
import com.bukuwarung.feature.onboarding.form.fragment.adapter.OnBoardingCategoryAdapter
import com.bukuwarung.feature.onboarding.form.newScreen.NewOnBoardingFormViewModel
import com.bukuwarung.feature.onboarding.form.screen.OnBoardingFormViewModel
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class OnBoardingBusinessGoalFormMergedFragment :
    UdfFragment(layoutRes = R.layout.fragment_on_boarding_business_goal_form_merged) {
    @Inject
    lateinit var lector: Lector

    private val viewBinding: FragmentOnBoardingBusinessGoalFormMergedBinding by viewBinding()

    private val viewModel: OnBoardingBusinessGoalFormViewModel by viewModels()

    private val onBoardingFormViewModel: NewOnBoardingFormViewModel by activityViewModels()

    private val adapter: OnBoardingCategoryAdapter by lazy {
        OnBoardingCategoryAdapter(onSelectListener = viewModel::selectBusinessGoal)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        setupView()
        viewLifecycleOwner.observeState(source = viewModel.stateFlow, action = ::render)

        viewModel.fetchBusinessGoals()
    }

    private fun setupView() {
        viewBinding.headerLabel.also { label ->
            label.text = lector.literate(resId = R.string.business_goal)
        }

        viewBinding.subHeaderLabel.also { label ->
            label.text = lector.literate(resId = R.string.business_goal_description)
        }

        viewBinding.captionLabel.also { label ->
            label.text = lector.literate(resId = R.string.select_one)
        }

        viewBinding.businessGoalList.also { list ->
            list.adapter = adapter
            list.layoutManager = GridLayoutManager(requireContext(), 3)
        }

        viewBinding.businessGoalProceedButton.also { button ->
            button.text = lector.literate(resId = R.string.next)
            button.isEnabled = false
        }
    }

    private fun render(state: OnBoardingBusinessGoalFormState) {
        handleProgressCompletion(state)

        renderBusinessGoals(state)
        renderProceedButton(state)
    }

    private fun handleProgressCompletion(state: OnBoardingBusinessGoalFormState) {
        val isProgressCompleted = state.isProgressCompleted
        if (!isProgressCompleted) return

        val selectedBusinessGoal = state.selectedBusinessGoal ?: return
        onBoardingFormViewModel.submitBusinessGoal(businessGoal = selectedBusinessGoal)

        viewModel.recordAnalytics(
            event = "continue_business_detail_dialogue",
            parameter = mapOf(
                "page_name" to "usage_goal",
                "usage_goal" to selectedBusinessGoal.name,
            ),
        )
    }

    private fun renderBusinessGoals(state: OnBoardingBusinessGoalFormState) {
        val isInitialized = state.isBusinessGoalsInitialized
        if (isInitialized) return

        val businessGoal = state.businessGoals ?: return
        adapter.submitList(businessGoal)

        viewModel.setBusinessGoalsInitialized()
    }

    private fun renderProceedButton(state: OnBoardingBusinessGoalFormState) {
        viewBinding.businessGoalProceedButton.also { button ->
            val selectedBusinessGoal = state.selectedBusinessGoal
            onBoardingFormViewModel.setTempBusinessGoal(state.selectedBusinessGoal)
            button.isEnabled = selectedBusinessGoal != null

            button.setOnClickListener {
                viewModel.setProgressCompleted()
            }
        }
    }
}
