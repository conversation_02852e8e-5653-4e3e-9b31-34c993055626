package com.bukuwarung.feature.onboarding.form.fragment.goal.di.module

import androidx.lifecycle.SavedStateHandle
import com.bukuwarung.base.udf.api.state.StateManager
import com.bukuwarung.base.udf.implementation.state.DefaultStateManager
import com.bukuwarung.feature.onboarding.form.fragment.goal.screen.OnBoardingBusinessGoalFormState
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.components.ViewModelComponent

@Module
@InstallIn(ViewModelComponent::class)
object OnBoardingBusinessGoalFormScreenModule {
    @Provides
    fun provideOnBoardingBusinessGoalFormState(): OnBoardingBusinessGoalFormState {
        return OnBoardingBusinessGoalFormState(
            businessGoals = null,
            isBusinessGoalsInitialized = false,
            selectedBusinessGoal = null,
            isProgressCompleted = false,
        )
    }

    @Provides
    fun provideStateManager(
        initState: OnBoardingBusinessGoalFormState,
        savedStateHandle: SavedStateHandle,
    ): StateManager<OnBoardingBusinessGoalFormState> {
        return DefaultStateManager(initState, savedStateHandle)
    }
}
