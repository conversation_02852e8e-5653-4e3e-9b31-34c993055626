package com.bukuwarung.feature.onboarding.form.fragment.name.screen

import android.content.res.ColorStateList
import android.os.Bundle
import android.view.View
import androidx.core.content.ContextCompat
import androidx.fragment.app.activityViewModels
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import com.bukuwarung.R
import com.bukuwarung.base.lector.api.Lector
import com.bukuwarung.base.messenger.api.Messenger
import com.bukuwarung.base.udf.api.screen.UdfFragment
import com.bukuwarung.base.udf.api.screen.viewBinding
import com.bukuwarung.base.udf.api.state.observeState
import com.bukuwarung.databinding.FragmentOnBoardingBusinessNameFormBinding
import com.bukuwarung.databinding.FragmentOnBoardingBusinessNameFormMergedBinding
import com.bukuwarung.feature.onboarding.form.fragment.name.widget.listener.OnTextChangedListener
import com.bukuwarung.feature.onboarding.form.newScreen.NewOnBoardingFormViewModel
import com.bukuwarung.feature.onboarding.form.screen.OnBoardingFormViewModel
import com.bukuwarung.utils.InputUtils
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.flow.callbackFlow
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import javax.inject.Inject

@AndroidEntryPoint
class OnBoardingBusinessNameFormMergedFragment :
    UdfFragment(layoutRes = R.layout.fragment_on_boarding_business_name_form_merged) {
    @Inject
    lateinit var lector: Lector

    @Inject
    lateinit var messenger: Messenger

    private val viewBinding: FragmentOnBoardingBusinessNameFormMergedBinding by viewBinding()

    private val viewModel: OnBoardingBusinessNameFormViewModel by viewModels()

    private val onBoardingFormViewModel: NewOnBoardingFormViewModel by activityViewModels()

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        setupView()
        viewLifecycleOwner.observeState(source = viewModel.stateFlow, action = ::render)
    }

    private fun setupView() {
        viewBinding.headerLabel.also { label ->
            label.text = lector.literate(resId = R.string.greetings)
        }

        viewBinding.businessNameInputLayout.also { inputLayout ->
            inputLayout.hint = lector.literate(resId = R.string.business_name_input)
            inputLayout.requestFocus()
        }

        viewBinding.businessNameInput.also { input ->
            callbackFlow {
                val onTextChangedListener = OnTextChangedListener(::trySend)

                input.addTextChangedListener(onTextChangedListener)
                awaitClose { input.removeTextChangedListener(onTextChangedListener) }
            }
                .onEach(viewModel::updateBusinessName)
                .launchIn(scope = viewLifecycleOwner.lifecycleScope)
        }

        viewBinding.businessNameErrorLabel.also { label ->
            label.visibility = View.GONE
        }

        viewBinding.businessNameProceedButton.also { button ->
            button.text = lector.literate(resId = R.string.next)
            button.isEnabled = false
        }
    }

    private fun render(state: OnBoardingBusinessNameFormState) {
        handleErrorMessage(state)
        handleProgressCompletion(state)

        renderBusinessNameInputLayout(state)
        renderBusinessNameErrorLabel(state)
        renderProceedButton(state)
    }

    private fun handleErrorMessage(state: OnBoardingBusinessNameFormState) {
        val errorMessage = state.errorMessage
        if (errorMessage.isBlank()) return

        messenger.displayToast(requireContext(), errorMessage)
        viewModel.clearErrorMessage()
    }

    private fun handleProgressCompletion(state: OnBoardingBusinessNameFormState) {
        val isProgressCompleted = state.isProgressCompleted
        if (!isProgressCompleted) return

        val businessName = state.businessName
        onBoardingFormViewModel.submitBusinessName(businessName)

        viewModel.recordAnalytics(
            event = "continue_business_detail_dialogue",
            parameter = mapOf(
                "page_name" to "business_name",
                "business_name" to businessName,
            ),
        )
    }

    private fun renderBusinessNameInputLayout(state: OnBoardingBusinessNameFormState) {
        viewBinding.businessNameInputLayout.also { inputLayout ->
            val errorMessage = state.businessNameErrorMessage
            val color = R.color.colorPrimary.takeIf { errorMessage.isBlank() } ?: R.color.red_80

            inputLayout.boxStrokeColor = ContextCompat.getColor(requireContext(), color)
            inputLayout.hintTextColor = ColorStateList.valueOf(
                ContextCompat.getColor(requireContext(), color)
            )
        }
    }

    private fun renderBusinessNameErrorLabel(state: OnBoardingBusinessNameFormState) {
        viewBinding.businessNameErrorLabel.also { label ->
            val errorMessage = state.businessNameErrorMessage

            if (errorMessage.isBlank()) {
                label.visibility = View.GONE
            } else {
                label.text = errorMessage
                label.visibility = View.VISIBLE
            }
        }
    }

    private fun renderProceedButton(state: OnBoardingBusinessNameFormState) {
        viewBinding.businessNameProceedButton.also { button ->
            val businessName = state.businessName
            onBoardingFormViewModel.setTempBusinessName(state.businessName)
            button.isEnabled = businessName.isNotBlank()

            button.setOnClickListener {
                viewModel.validateBusinessName(businessName)
                InputUtils.hideKeyBoardWithCheck(activity)
            }
        }
    }
}
