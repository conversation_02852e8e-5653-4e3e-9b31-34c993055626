package com.bukuwarung.feature.onboarding.form.di.module

import androidx.lifecycle.SavedStateHandle
import com.bukuwarung.base.udf.api.state.StateManager
import com.bukuwarung.base.udf.implementation.state.DefaultStateManager
import com.bukuwarung.feature.onboarding.form.newScreen.NewOnBoardingFormState
import com.bukuwarung.feature.onboarding.form.screen.OnBoardingFormState
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.components.ViewModelComponent

@Module
@InstallIn(ViewModelComponent::class)
object OnBoardingFormScreenModule {
    @Provides
    fun provideOnBoardingFormState(): OnBoardingFormState {
        return OnBoardingFormState(
            forms = null,
            totalPages = -1,
            currentPage = 0,
            isFormCompleted = false,
            isHandleNavigation = false,
            selectedBusinessName = "",
            selectedBusinessCategory = null,
            selectedBusinessGoal = null,
            selectedBusinessPastUsage = null,
            tempBusinessName = "",
            tempBusinessCategory = null,
            tempBusinessGoal = null,
            tempBusinessPastUsage = null,
            onBoardingRedirectionPolicy = null,
            tabRedirection = "",
        )
    }

    @Provides
    fun provideNewOnBoardingFormState(): NewOnBoardingFormState {
        return NewOnBoardingFormState(
            forms = null,
            totalPages = -1,
            currentPage = 0,
            isFormCompleted = false,
            isHandleNavigation = false,
            selectedBusinessName = "",
            selectedBusinessCategory = null,
            selectedBusinessGoal = null,
            selectedBusinessPastUsage = null,
            onBoardingRedirectionPolicy = null,
            tabRedirection = "",
        )
    }

    @Provides
    fun provideStateManager(
        initState: OnBoardingFormState,
        savedStateHandle: SavedStateHandle,
    ): StateManager<OnBoardingFormState> {
        return DefaultStateManager(initState, savedStateHandle)
    }

    @Provides
    fun provideNewStateManager(
        initState: NewOnBoardingFormState,
        savedStateHandle: SavedStateHandle,
    ): StateManager<NewOnBoardingFormState> {
        return DefaultStateManager(initState, savedStateHandle)
    }
}
