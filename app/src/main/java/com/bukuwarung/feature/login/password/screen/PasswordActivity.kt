package com.bukuwarung.feature.login.password.screen

import android.app.Activity
import android.content.ComponentName
import android.content.Intent
import android.os.Build
import android.os.Bundle
import android.text.*
import android.text.method.LinkMovementMethod
import android.text.style.ClickableSpan
import android.view.View
import androidx.activity.viewModels
import androidx.core.content.ContextCompat
import androidx.core.os.bundleOf
import com.bukuwarung.BukuWarungKeys
import com.bukuwarung.R
import com.bukuwarung.activities.home.MainActivity
import com.bukuwarung.activities.onboarding.GeneralLoadingDialog
import com.bukuwarung.activities.onboarding.LoginViewModel
import com.bukuwarung.activities.onboarding.NewVerifyOtpActivity
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.base.udf.api.screen.UdfActivity
import com.bukuwarung.base.udf.api.screen.viewBinding
import com.bukuwarung.base.udf.api.state.observeState
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.constants.AppConst
import com.bukuwarung.constants.RemoteConfigConst
import com.bukuwarung.databinding.ActivityPasswordBinding
import com.bukuwarung.dialogs.NoInternetDialogFragment
import com.bukuwarung.enums.NotificationChannel
import com.bukuwarung.feature.auth.login.numericcaptcha.screen.NumericCaptchaDialogFragment
import com.bukuwarung.feature.onboarding.form.newScreen.NewOnBoardingFormActivity
import com.bukuwarung.feature.onboarding.form.screen.OnBoardingFormActivity
import com.bukuwarung.neuro.api.Neuro
import com.bukuwarung.preference.AppConfigManager
import com.bukuwarung.session.SessionManager
import com.bukuwarung.ui_component.component.button.BukuButton
import com.bukuwarung.ui_component.utils.showView
import com.bukuwarung.utils.*
import com.google.android.gms.common.api.ApiException
import com.google.android.gms.common.api.CommonStatusCodes.NETWORK_ERROR
import com.google.android.gms.common.api.CommonStatusCodes.TIMEOUT
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class PasswordActivity : UdfActivity() {

    @Inject
    lateinit var neuro: Neuro

    private val viewBinding: ActivityPasswordBinding by viewBinding()

    private val viewModel: PasswordScreenViewModel by viewModels()

    private val loginViewModel : LoginViewModel by viewModels()
    private var phone = ""
    private var countryCode = ""
    private var userId = ""
    private var autoVerify = false

    lateinit var span1: ClickableSpan
    lateinit var span2: ClickableSpan
    private var showCaptcha = false
    lateinit var textWatcher : TextWatcher

    private var isPasswordVisible: Boolean = false

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(viewBinding.root)

        setupView()
        observeState(source = viewModel.stateFlow, action = ::render)

        setupNumericCaptchaListener()
        viewModel.initialize()
    }

    private fun setupView() {
        handleVisitEvent()
        showScreen()
        viewBinding.btnEnter.disableButton()

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M &&
            Build.VERSION.SDK_INT < Build.VERSION_CODES.Q) {
            if (!PermissonUtil.hasPhoneStatePermission()) {
                PermissonUtil.requestPhoneStatePermission(this@PasswordActivity)
            }
        }

        viewBinding.tvForgotPasswordLabel.setOnClickListener {
            hideErrorAndAlert()
            registerForgotPasswordClick()
            viewModel.requestOtp(phone,userId,NotificationChannel.SMS.value,countryCode)
            showProgressState()
        }

        viewBinding.toolbar.setNavigationOnClickListener({
            // back button pressed
            onBackPressed()
        })

        if (intent.hasExtra(VERIFY_OTP_PARAM_PHONE)) {
            val intent = intent
            phone = intent.getStringExtra(VERIFY_OTP_PARAM_PHONE) ?: ""
            countryCode = intent.getStringExtra(VERIFY_OTP_PARAM_COUNTRY_CODE) ?: ""
            userId = intent.getStringExtra(USER_ID) ?: ""
            showCaptcha = intent.getBooleanExtra(SHOW_CAPTCHA, false)
        }

        viewBinding.btnEnter.setOnClickListener {
            hideErrorAndAlert()
            checkBeforeValidatePassword(viewBinding.tvPassword.text.toString())
        }

        textWatcher = object : TextWatcher {

            override fun afterTextChanged(s: Editable) {}

            override fun beforeTextChanged(
                s: CharSequence, start: Int,
                count: Int, after: Int
            ) {
            }

            override fun onTextChanged(
                s: CharSequence, start: Int,
                before: Int, count: Int
            ) {
                if(s.length>0){
                    viewBinding.btnEnter.setButtonType(BukuButton.ButtonType.YELLOW_60.string)
                    viewBinding.btnEnter.isEnabled = true
                }else {
                    viewBinding.btnEnter.disableButton()
                }
            }
        }
        viewBinding.tvPassword.addTextChangedListener(textWatcher)
        viewBinding.tvPassword.onFocusChangeListener = View.OnFocusChangeListener { view, hasFocus -> if(hasFocus) registerPasswordFieldClick(AnalyticsConst.CURRENT_PASSWORD)}

        viewBinding.passwordLayout.setEndIconOnClickListener {
            isPasswordVisible = !isPasswordVisible
            if(isPasswordVisible) {
                viewBinding.tvPassword.inputType = InputType.TYPE_TEXT_VARIATION_VISIBLE_PASSWORD
            }else{
                viewBinding.tvPassword.inputType = AppConst.INPUT_TYPE_PASSWORD
            }
            registerPasswordVisiblityClick("current_password",isPasswordVisible)
        }
    }

    override fun onBackPressed() {
        registerOnBackPressClick()
        super.onBackPressed()
    }

    private fun registerPasswordFieldClick(fieldType : String) {
        val propBuilder = AppAnalytics.PropBuilder()
        propBuilder.put(AnalyticsConst.FIELD, fieldType)
        propBuilder.put(AnalyticsConst.SCREEN_NAME, AnalyticsConst.PASSWORD_SCREEN)
        AppAnalytics.trackEvent(AnalyticsConst.EVENT_SET_PASSWORD_BOX_CLICK, propBuilder)
    }

    private fun registerPasswordVisiblityClick(fieldType : String,action : Boolean) {
        val propBuilder = AppAnalytics.PropBuilder()
        propBuilder.put(AnalyticsConst.FIELD, fieldType)
        propBuilder.put(AnalyticsConst.ACTION, if (action) AnalyticsConst.SHOW else AnalyticsConst.HIDE)
        propBuilder.put(AnalyticsConst.SCREEN_NAME, AnalyticsConst.PASSWORD_SCREEN)
        AppAnalytics.trackEvent(AnalyticsConst.EVENT_SET_PASSWORD_SHOW_CLICK, propBuilder)
    }

    private fun registerOnBackPressClick() {
        val propBuilder = AppAnalytics.PropBuilder()
        propBuilder.put(AnalyticsConst.SCREEN_NAME, AnalyticsConst.PASSWORD_SCREEN)
        AppAnalytics.trackEvent(AnalyticsConst.EVENT_BACK_BUTTON_PRESS, propBuilder)
    }

    private fun registerForgotPasswordClick() {
        val propBuilder = AppAnalytics.PropBuilder()
        AppAnalytics.trackEvent(AnalyticsConst.EVENT_FORGOT_PASSWORD_CLICK, propBuilder)
    }

    private fun handleVisitEvent() {
        val propBuilder = AppAnalytics.PropBuilder()
        AppAnalytics.trackEvent(AnalyticsConst.EVENT_LOGIN_PAGE_VISIT, propBuilder)
    }

    private fun checkBeforeValidatePassword(password: String) {
        // validate password from server
        viewModel.validatePassword(phone,countryCode,password,userId, "", "", 0)
        showProgressState()
    }

    private fun goToHome() {
        finishAffinity()
        val intent = Intent(this, MainActivity::class.java)
        startActivity(intent)
    }

    private fun render(state: PasswordScreenState) {
        showScreen()
        handleNavigation(state)
        handlePasswordValidation(state)
        if(state.proceedWithLogin) proceedWithLogin()
        if(state.goToVerifyOtp) goToVerifyOtp(phone, countryCode)
        renderEnterButton(state)
        handleError(state.error)
    }

    private fun handleError(state: ResponseError?) {

        if(state==null){
//            viewBinding.alertLayout.hideView()
//            viewBinding.tvError.hideView()
        }
        else{
            if(state.statusCode.equals(455)) {
                viewBinding.tvError.showView()
                viewBinding.tvError.text = state.errorMessage
                viewBinding.alertLayout.hideView()
                if(!state.forResendOtp)registerSubmitEvent(AnalyticsConst.STATUS_FAIL,AnalyticsConst.WRONG_PASSWORD)
            }else if (state.statusCode.equals(403)){
                viewBinding.tvError.showView()
                viewBinding.tvError.text = state.errorMessage
                // if remaining time to verify is not null,then user is blocked
                setUpAlert(state.remainingTimeToVerify)
                if(!state.forResendOtp)registerSubmitEvent(AnalyticsConst.STATUS_FAIL,AnalyticsConst.WRONG_PASSWORD)
            }
            else{
                setUpAlert(null,state.errorMessage)
                if(!state.forResendOtp)registerSubmitEvent(AnalyticsConst.STATUS_FAIL,AnalyticsConst.SERVER_ERROR)
            }
            viewModel.setLoginError(null)
        }
    }

    fun hideErrorAndAlert(){
        viewBinding.alertLayout.hideView()
        viewBinding.tvError.hideView()
    }

    private fun registerSubmitEvent(status : String, failureReason: String) {
        val propBuilder = AppAnalytics.PropBuilder()
        propBuilder.put(AnalyticsConst.STATUS, status)
        propBuilder.put(AnalyticsConst.FAIL_REASON, failureReason)
        AppAnalytics.trackEvent(AnalyticsConst.EVENT_LOGIN_PASSWORD_SUBMIT, propBuilder)
    }

    fun setUpAlert(remainingTime : Int? ,errorMessage : String="") {
        viewBinding.alertLayout.showView()
        viewBinding.tvWarningTitle.showView()
        viewBinding.tvWarningTxt.showView()

        if(remainingTime!=null){
            viewBinding.tvForgotPasswordLabel.also {
                it.setTextColor(getColorCompat(R.color.black40))
                it.setOnClickListener {  }
            }
            viewBinding.tvPassword.removeTextChangedListener(textWatcher)
            viewBinding.btnEnter.disableButton()

            viewBinding.tvWarningTitle.text =    getString(R.string.rest_of_trails_run_out)
            viewBinding.tvWarningTxt.text = getString(R.string.contact_customer_care)
        }else {
            viewBinding.tvWarningTitle.text =  errorMessage
            viewBinding.tvWarningTxt.hideView()
//            viewBinding.tvForgotPasswordLabel.also {
//                it.setTextColor(R.color.black40)
//                it.setOnClickListener {  }
//            }
//            viewBinding.tvPassword.removeTextChangedListener(textWatcher)
//            viewBinding.btnEnter.disableButton()
        }

        span1 = object : ClickableSpan() {
            override fun onClick(textView: View) {
                openWaBotHelp()
//                goToWhatsAppForCustomerService()
            }
        }

        val ss = SpannableString(getString(R.string.contact_customer_care))

        ss.setSpan(span1, 36, 53, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)

        viewBinding.tvWarningTxt.setText(ss)
        viewBinding.tvWarningTxt.setMovementMethod(LinkMovementMethod.getInstance())
    }

    private fun openWaBotHelp() {
        var bundle = Bundle()
        bundle.putString(AnalyticsConst.ENTRY_POINT,AnalyticsConst.PASSWORD_PAGE)
        WhatsAppUtils.openWABotWithHelpText(this, getString(R.string.wa_help_text_general), bundle)
    }


    private fun goToWhatsAppForCustomerService() {
        val sendIntent = Intent("android.intent.action.MAIN")
        sendIntent.component = ComponentName("com.whatsapp", "com.whatsapp.Conversation")
        sendIntent.action = Intent.ACTION_SEND
        sendIntent.type = "text/plain"
        sendIntent.putExtra(Intent.EXTRA_TEXT, "")
        sendIntent.putExtra("jid", AppConfigManager.getInstance().whatsappId + "@s.whatsapp.net")
        sendIntent.setPackage("com.whatsapp")
        startActivity(sendIntent)
    }

    private fun proceedWithLogin() {
        registerSubmitEvent(AnalyticsConst.STATUS_SUCCESS,AnalyticsConst.NONE)
        try {
            if(!SessionManager.getInstance().hasExistingBusiness() && Utility.isEqual(phone,SessionManager.getInstance().userIdForExistingBusinessCheck) && RemoteConfigUtils.OnBoarding.getOnBoardingVariant() == RemoteConfigConst.USE_NEW_ONBOARDING_VARIANT){
                SessionManager.getInstance().setIsExistingOldUser(false)
                if(RemoteConfigUtils.getOnboardingType()==1){
                    startActivity(Intent(this, NewOnBoardingFormActivity::class.java))
                }else{
                    startActivity(Intent(this, OnBoardingFormActivity::class.java))
                }
                finish()
            }else {
                MainActivity.startActivityAndClearTop(this, LoginUtils.IS_NEW_LOGIN_EXTRA, true)
                MainActivity().sendAppsFlyerId()
                finishAffinity()
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private fun handlePasswordValidation(state: PasswordScreenState) {
        if(state.passwordValidatedData!=null)
        goToHome()
    }

    private fun goToVerifyOtp(phone: String, countryCode: String) {
        SessionManager.getInstance().tryCount=0
        val intent = NewVerifyOtpActivity.createIntent(
            this,
            phone = phone,
            otpCode = "-",
            countryCode = countryCode,
            useCase = NewVerifyOtpActivity.Companion.UseCase.VERIFY_OTP_FORGOT_PASSWORD
        )
        startActivity(intent)
        finish()
    }



    private fun handleNavigation(state: PasswordScreenState) {
        if (!state.isHandleNavigation) return
//        neuro.navigate(
//            context = this,
//            path = "/main",
//            query = mapOf(
//                "IS_NEW_LOGIN_EXTRA" to true,
//                "tab_name" to "HOME",
//            ),
//        )
        finish()
    }

    private fun showLoadingDialog() {
        GeneralLoadingDialog.createInstance().show(supportFragmentManager, GeneralLoadingDialog.TAG)
    }


    private fun showScreen() {
        viewBinding.progressbar.visibility = View.GONE
        viewBinding.successLayout.visibility = View.VISIBLE
        viewBinding.serverErrorLayout.visibility = View.GONE
    }

    private fun showErrorState() {
        with(viewBinding) {
            viewBinding.progressbar.hideView()
            viewBinding.serverErrorLayout.showView()
            viewBinding.successLayout.hideView()
        }
    }

    private fun showProgressState() {
        with(viewBinding) {
            viewBinding.progressbar.showView()
            viewBinding.serverErrorLayout.hideView()
            viewBinding.successLayout.hideView()
        }
    }

    private fun renderPasswordError(state: PasswordScreenState) {
        val isLoginCaptchaError = state.loginCaptchaError
        if (!isLoginCaptchaError) return

        viewBinding.tvPassword.setText(getString(R.string.login_captcha_error))
    }

    private fun renderEnterButton(state: PasswordScreenState) {
        val showCaptcha = showCaptcha
        val captchaAttempts = state.captchaAttempts
        val loginAttempts = state.loginAttempts
        val captchaVariant = state.captchaVariant
        val fallbackToNumericCaptcha = state.fallbackToNumericCaptcha

        viewBinding.btnEnter.also { view ->
            view.setOnClickListener {
                hideErrorAndAlert()
                val password = viewBinding.tvPassword.text.toString()
                if ((loginAttempts == 0 && showCaptcha) || loginAttempts > 0) {
                    if (captchaVariant == 1 && !fallbackToNumericCaptcha) {
//                        showGoogleRecaptcha(
//                            countryCode = countryCode,
//                            phone = phone,
//                            userId = userId,
//                            password = password,
//                            captchaAttempts = captchaAttempts,
//                            loginAttempts = loginAttempts,
//                        )

                        viewModel.recordAnalytics(
                            event = "captcha_verification_visit",
                            parameter = mapOf(
                                "captcha_input_attempts" to captchaAttempts,
                                "captcha_variant" to "google_recaptcha",
                            ),
                        )
                    } else {
                        showNumericCaptcha(
                            countryCode = countryCode,
                            phone = phone,
                            userId = userId,
                            password = password,
                            captchaAttempts = captchaAttempts,
                            loginAttempts = loginAttempts,
                        )

                        viewModel.recordAnalytics(
                            event = "captcha_verification_visit",
                            parameter = mapOf(
                                "captcha_input_attempts" to captchaAttempts,
                                "captcha_variant" to "numeric_captcha",
                            ),
                        )
                    }
                } else {
                    checkBeforeValidatePassword(password)
                }
            }
        }
    }

//    private fun showGoogleRecaptcha(
//        countryCode: String,
//        phone: String,
//        userId: String,
//        password: String,
//        captchaAttempts: Int,
//        loginAttempts: Int,
//    ) = try {
//        SafetyNet.getClient(this)
//            .verifyWithRecaptcha(BukuWarungKeys.SITE_KEY.orEmpty())
//            .addOnSuccessListener({ it.run() }, { response ->
//                val token = response.tokenResult
//                if (token != null) {
//                    viewModel.validatePassword(phone,countryCode,password, userId, token, captchaType = "RECAPTCHA", captchaAttempts)
//                    showProgressState()
//                    viewModel.recordAnalytics(
//                        event = "captcha_verification_submit",
//                        parameter = mapOf(
//                            "captcha_input_attempts" to captchaAttempts + 1,
//                            "captcha_variant" to "google_recaptcha",
//                        ),
//                    )
//                } else {
//                    viewModel.setFallbackToNumericCaptcha()
//                    viewModel.recordAnalytics(
//                        event = "captcha_verification_complete",
//                        parameter = mapOf(
//                            "status" to "failed",
//                            "fail_reason" to "system_error_4",
//                            "captcha_input_attempts" to captchaAttempts + 1,
//                            "captcha_variant" to "google_recaptcha",
//                        ),
//                    )
//                }
//            })
//            .addOnFailureListener({ it.run() }, { e ->
//                e.recordException()
//
//                if (e is ApiException) {
//                    when (e.statusCode) {
//                        TIMEOUT -> {
//                            viewModel.recordAnalytics(
//                                event = "captcha_verification_complete",
//                                parameter = mapOf(
//                                    "status" to "failed",
//                                    "fail_reason" to "lost_connection",
//                                    "captcha_input_attempts" to captchaAttempts + 1,
//                                    "captcha_variant" to "google_recaptcha",
//                                ),
//                            )
//                        }
//                        NETWORK_ERROR -> {
//                            showNoInternetDialog()
//                            viewModel.recordAnalytics(
//                                event = "captcha_verification_complete",
//                                parameter = mapOf(
//                                    "status" to "failed",
//                                    "fail_reason" to "lost_connection",
//                                    "captcha_input_attempts" to captchaAttempts + 1,
//                                    "captcha_variant" to "google_recaptcha",
//                                ),
//                            )
//                        }
//                        else -> {
//                            viewModel.setFallbackToNumericCaptcha()
//                            viewModel.recordAnalytics(
//                                event = "captcha_verification_complete",
//                                parameter = mapOf(
//                                    "status" to "failed",
//                                    "fail_reason" to "system_error_2",
//                                    "captcha_input_attempts" to captchaAttempts + 1,
//                                    "captcha_variant" to "google_recaptcha",
//                                ),
//                            )
//                        }
//                    }
//                } else {
//                    viewModel.setFallbackToNumericCaptcha()
//                    viewModel.recordAnalytics(
//                        event = "captcha_verification_complete",
//                        parameter = mapOf(
//                            "status" to "failed",
//                            "fail_reason" to "system_error_3",
//                            "captcha_input_attempts" to captchaAttempts + 1,
//                            "captcha_variant" to "google_recaptcha",
//                        ),
//                    )
//                }
//            })
//    } catch (e: Exception) {
//        e.recordException()
//        viewModel.setFallbackToNumericCaptcha()
//    }

    private fun showNumericCaptcha(
        countryCode: String,
        phone: String,
        userId: String,
        password: String,
        captchaAttempts: Int,
        loginAttempts: Int,
    ) {
        NumericCaptchaDialogFragment().apply {
            arguments = bundleOf(
                "requestKey" to NUMERIC_CAPTCHA_REQUEST_KEY,
                "countryCode" to countryCode,
                "phone" to phone,
                "userId" to userId,
                "password" to password,
                "captchaAttempts" to captchaAttempts,
                "loginAttempts" to loginAttempts,
            )
            isCancelable = false
        }.show(supportFragmentManager, NUMERIC_CAPTCHA_TAG)
    }

    private fun setupNumericCaptchaListener() {
        supportFragmentManager.setFragmentResultListener(NUMERIC_CAPTCHA_REQUEST_KEY, this) { _, data ->
            val captchaAttempts = data.getInt("captchaAttempts")
            val loginAttempts = data.getInt("loginAttempts")

            val sessionToken = data.getString("sessionToken")
            val idToken = data.getString("idToken")

            val loginError = data.getSerializable("loginError") as? ResponseError

            viewModel.recordCaptchaAttempts(captchaAttempts)
            viewModel.recordLoginAttempts(loginAttempts)

            if (sessionToken != null && idToken != null) {
                viewModel.handlePostLogin(phone, sessionToken, idToken)
            } else if (loginError != null) {
                viewModel.setLoginError(loginError)
            }
        }
    }

    private fun registerLoginEvent(isSuccess : Boolean,failureReason : String) {
        val propBuilder = AppAnalytics.PropBuilder()
        propBuilder.put(AnalyticsConst.STATUS, isSuccess)
        if(!isSuccess){
            propBuilder.put(AnalyticsConst.FAIL_REASON, AnalyticsConst.INITIAL)
        }
        AppAnalytics.trackEvent(AnalyticsConst.EVENT_SET_PASSWORD_SUMIT, propBuilder)
    }

    private fun showNoInternetDialog() {
        NoInternetDialogFragment().apply {
            isCancelable = false
        }.show(supportFragmentManager, NO_INTERNET_TAG)
    }

    companion object {
        const val OTP_LENGTH = 4
        private const val VERIFY_OTP_PARAM_PHONE = "phone"
        private const val VERIFY_OTP_PARAM_OTP = "otp"
        private const val VERIFY_OTP_PARAM_COUNTRY_CODE = "country"
        private const val SHOW_DIALOG = "show_dialog"
        private const val USER_ID = "user_id"
        private const val SHOW_CAPTCHA = "show_captcha"

        private const val NUMERIC_CAPTCHA_TAG = "numeric_captcha"
        private const val NUMERIC_CAPTCHA_REQUEST_KEY = "numeric_captcha_key"

        private const val NO_INTERNET_TAG = "no_internet"

        fun createIntent(
            origin: Activity?,
            phone: String?,
            otpCode: String?,
            countryCode: String,
            showDialog: Boolean,
            userId: String,
            showCaptcha: Boolean,
        ): Intent {
            val intent = Intent(origin, PasswordActivity::class.java)
            intent.putExtra(VERIFY_OTP_PARAM_PHONE, phone)
            intent.putExtra(VERIFY_OTP_PARAM_OTP, otpCode)
            intent.putExtra(VERIFY_OTP_PARAM_COUNTRY_CODE, countryCode.replace("+", ""))
            intent.putExtra(SHOW_DIALOG, showDialog)
            intent.putExtra(USER_ID, userId)
            intent.putExtra(SHOW_CAPTCHA, showCaptcha)
            return intent
        }
    }
}
