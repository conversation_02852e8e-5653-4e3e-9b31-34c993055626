package com.bukuwarung.feature.login.password.screen

import com.bukuwarung.data.password.api.model.ValidatePasswordResponse
import java.io.Serializable

data class PasswordScreenState(
    val totalPages: Int,
    val currentPage: Int,
    val isFormCompleted: <PERSON><PERSON>an,
    val isHandleNavigation: <PERSON><PERSON>an,
    val selectedBusinessName: String,
    val tabRedirection: String,
    val showLoading: <PERSON>olean,
    val startAfterVerifyOtp: <PERSON>olean,
    val firebaseAuthError: Boolean,
    val successSignInFirebase: Boolean,
    val proceedWithLogin : Boolean,
    val passwordValidatedData: ValidatePasswordResponse?,
    val goToVerifyOtp : Boolean,
    val error : ResponseError?,
    val captchaAttempts: Int,
    val loginAttempts: Int,
    val captchaVariant: Int,
    val fallbackToNumericCaptcha: <PERSON><PERSON><PERSON>,
    val loginCaptchaError: <PERSON><PERSON><PERSON>,
) : Serializable


data class ResponseError(
    val statusCode : Int,
    val errorMessage : String,
    val remainingTimeToVerify: Int?,
    val forResendOtp : Boolean=false,
    ) : Serializable