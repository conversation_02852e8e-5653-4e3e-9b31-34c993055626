package com.bukuwarung.feature.login.password.screen

import android.app.Activity
import androidx.lifecycle.viewModelScope
import com.auth0.android.jwt.JWT
import com.bukuwarung.BuildConfig
import com.bukuwarung.BukuWarungKeys
import com.bukuwarung.activities.onboarding.ClaimStoreId
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.base.data.api.Response
import com.bukuwarung.base.telemetry.api.Telemetry
import com.bukuwarung.base.telemetry.di.qualifier.AmplitudeTelemetry
import com.bukuwarung.base.telemetry.di.qualifier.AppsFlyerTelemetry
import com.bukuwarung.base.telemetry.di.qualifier.FirebaseTelemetry
import com.bukuwarung.base.tracker.api.Tracker
import com.bukuwarung.base.tracker.di.qualifier.SurvicateTracker
import com.bukuwarung.base.udf.api.screen.UdfViewModel
import com.bukuwarung.base.udf.api.state.StateManager
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.constants.AppConst
import com.bukuwarung.data.analytics.api.AnalyticsRepository
import com.bukuwarung.data.app.api.AppRepository
import com.bukuwarung.data.auth.api.AuthRepository
import com.bukuwarung.data.password.api.LoginRepository
import com.bukuwarung.data.password.api.model.ValidatePasswordRequest
import com.bukuwarung.data.password.api.model.ValidatePasswordResponse
import com.bukuwarung.data.restclient.ApiEmptyResponse
import com.bukuwarung.data.restclient.ApiErrorResponse
import com.bukuwarung.data.restclient.ApiSuccessResponse
import com.bukuwarung.data.user.api.UserRepository
import com.bukuwarung.database.repository.FirebaseRepository
import com.bukuwarung.enums.AuthAction
import com.bukuwarung.model.request.LoginRequest
import com.bukuwarung.preference.SyncManager
import com.bukuwarung.session.SessionManager
import com.bukuwarung.setup.SetupManager
import com.bukuwarung.utils.DeviceUtils
import com.bukuwarung.utils.LoginUtils
import com.bukuwarung.utils.NumberUtils
import com.google.firebase.auth.AuthResult
import com.google.firebase.auth.GetTokenResult
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import org.json.JSONObject
import javax.inject.Inject
import com.bukuwarung.data.repository.AuthRepository as LegacyAuthRepository

@HiltViewModel
class PasswordScreenViewModel @Inject constructor(
    stateManager: StateManager<PasswordScreenState>,
    private val appRepository: AppRepository,
    private val analyticsRepository: AnalyticsRepository,
    private val authRepository: AuthRepository,
    private val legacyAuthRepository: LegacyAuthRepository,
    private val loginRepository: LoginRepository,
    private val loginRepositoryOld: com.bukuwarung.data.repository.LoginRepository,
    private val userRepository: UserRepository,
    private val firebaseRepository: FirebaseRepository,
    @SurvicateTracker private val tracker: Tracker,
    @AmplitudeTelemetry private val amplitudeTelemetry: Telemetry,
    @AppsFlyerTelemetry private val appsFlyerTelemetry: Telemetry,
    @FirebaseTelemetry private val firebaseTelemetry: Telemetry,
) : UdfViewModel<PasswordScreenState>(stateManager) {

    private var countryCode = SessionManager.getInstance().countryCode

    fun initialize() {
        viewModelScope.launch {
            val variant = authRepository.fetchAuthVariant()
            produce { state ->
                state.copy(captchaVariant = variant)
            }
        }
    }

    fun recordCaptchaAttempts(attempts: Int) {
        produce { state ->
            state.copy(captchaAttempts = attempts)
        }
    }

    fun recordLoginAttempts(attempts: Int) {
        produce { state ->
            state.copy(loginAttempts = attempts)
        }
    }

    private fun handleValidatePasswordResponse(
        response: Response<ValidatePasswordResponse>,
        phone: String,
        captchaType: String,
        captchaAttempts: Int,
    ) {
        if (response is Response.Success) {
            handlePostLogin(
                phone = phone,
                sessionToken = response.data.sessionToken,
                idToken = response.data.idToken,
            )

            if (captchaType.isNotBlank()) {
                recordAnalytics(
                    event = "captcha_verification_complete",
                    parameter = mapOf(
                        "status" to "success",
                        "fail_reason" to "none",
                        "captcha_input_attempts" to captchaAttempts + 1,
                        "captcha_variant" to "google_recaptcha",
                    ),
                )
            }
        } else if (response is Response.Error){
            produce { state ->

                val responseError = JSONObject(response.meta.get("code") as String)
                val remainingTimeToVerify : Int = responseError.optInt("remainingTimeToVerify")

                state.copy(
                    passwordValidatedData = null,
                    loginAttempts = state.loginAttempts + 1,
                    error = ResponseError(response.meta.get("statusCode") as Int,response.message,if (remainingTimeToVerify!=0) remainingTimeToVerify else null,false)
                )
            }

            if (captchaType.isNotBlank()) {
                recordAnalytics(
                    event = "captcha_verification_complete",
                    parameter = mapOf(
                        "status" to "failed",
                        "fail_reason" to "system_error",
                        "captcha_input_attempts" to captchaAttempts + 1,
                        "captcha_variant" to "google_recaptcha",
                    ),
                )
            }
        }
    }

    fun handlePostLogin(
        phone: String,
        sessionToken: String,
        idToken: String,
    ) {
        val parsedJWT = JWT(idToken)
        val claim = parsedJWT.getClaim("claims").asObject(ClaimStoreId::class.java)
        claim?.let { SessionManager.getInstance().uuid = it?.auth_user_id }


        viewModelScope.launch {
            afterOtpVerify(idToken, phone, false).join()
        }
        SessionManager.getInstance().sessionToken = sessionToken
        SessionManager.getInstance().setSessionStart()
        firebaseRepository.checkAgreedTnC()
    }

    private fun afterOtpVerify(token: String?, phone: String, autoVerify: Boolean) =
        viewModelScope.launch {
            token ?: return@launch

            produce { state ->
                state.copy(showLoading = true)
            }
            produce { state ->
                state.copy(startAfterVerifyOtp = true)
            }
            SessionManager.getInstance().isMigrated = true
            signInToFirebase(token, phone)
            val tokenResult = updateRefreshToken(autoVerify, phone) ?: return@launch
            val idToken = tokenResult.token ?: return@launch
            //no handler if result null?
            //keep single place to set idToken, keep refreshing on each session
            SessionManager.getInstance().bukuwarungToken = idToken
            val phoneNumber: String? = NumberUtils.formatPhoneNumber(phone)
            SessionManager.getInstance().isGuestUser(false)
            if (SessionManager.getInstance().userId != null && SessionManager.getInstance().userId != phoneNumber) {
                SessionManager.getInstance().appState = 0
                SetupManager.getInstance().hasRestored(false)
                SyncManager.getInstance().setRestoredAll(false)
            }
            LoginUtils.prepareSession("+$countryCode", phoneNumber, token)
            SessionManager.getInstance().userLoggedInWithPin = true
            SessionManager.getInstance().userLoggedInWithPinAtleastOnce = true
            produce { state -> state.copy(proceedWithLogin = true) }
        }

    private suspend fun signInToFirebase(
        token: String,
        phone: String,
    ) {
        val authResult = signInToFirebaseAuth(token)
        produce { state ->
            if (authResult == null) {
                AppAnalytics.trackEvent("failed_firebase_login", phone, token)
                state.copy(firebaseAuthError = true)
            } else {
                state.copy(successSignInFirebase = true)
            }
        }
    }


    fun recordAnalytics(event: String, parameter: Map<String, Any>) {
        amplitudeTelemetry.record(event, parameter)
        appsFlyerTelemetry.record(event, parameter)
        firebaseTelemetry.record(event, parameter)
    }

    fun recordProfile(parameter: Map<String, Any>) {
        amplitudeTelemetry.recordProfile(parameter)
    }

    fun trackActivity(activity: Activity, event: String, parameter: Map<String, Any>) {
        tracker.track(activity, event, parameter)
    }

    fun postAppsFlyerId() {
        viewModelScope.launch {
            val userId = userRepository.readUserId()
            val advertisingId = appRepository.readAdvertisingId()

            analyticsRepository.postAppsFlyerId(userId, advertisingId)
        }
    }

        fun validatePassword(phone: String,countryCode: String, password: String, userId: String, captchaToken: String, captchaType: String, captchaAttempts: Int) {
            this.countryCode = countryCode
            produce { state ->
            state.copy(passwordValidatedData = null, error = null)
        }
        val prop = AppAnalytics.PropBuilder()
        prop.put("androidId", DeviceUtils.getAndroidId())
        prop.put("imeiNumber", SessionManager.getInstance().imeiNumber)
        prop.put("wideVineId", DeviceUtils.getWideVineId())
        prop.put("advertisingId", SessionManager.getInstance().advertisingId)
        if (BuildConfig.FLAVOR != AppConst.PRODUCTION_FLAVOR) {
            AppAnalytics.trackEvent("device_fingerprint_password", prop)
        }
        viewModelScope.launch {
            val request = ValidatePasswordRequest(
                countryCode = countryCode.replace("+", ""),
                userId = userId,
                phone = phone,
                password = password,
                deviceId = SessionManager.getInstance().deviceGUID,
                clientId = BukuWarungKeys.clientId.orEmpty(),
                clientSecret = BukuWarungKeys.clientSecret.orEmpty(),
                platform = BuildConfig.PLATFORM,
                origin = BuildConfig.ORIGIN,
                captchaResponse = captchaToken,
                captchaType = captchaType,
                androidId = DeviceUtils.getAndroidId(),
                imeiNumber = SessionManager.getInstance().imeiNumber,
                wideVineId = DeviceUtils.getWideVineId(),
                advertisingId = SessionManager.getInstance().advertisingId
            )
            val response = loginRepository.validatePassword(request)
            handleValidatePasswordResponse(response, phone, captchaType, captchaAttempts)
        }
    }

    suspend fun signInToFirebaseAuth(token: String): AuthResult? {
        return legacyAuthRepository.signInToFirebaseAuth(token)
    }

    suspend fun updateRefreshToken(autoVerify: Boolean, phone: String): GetTokenResult? {
        return legacyAuthRepository.updateRefreshToken(autoVerify, phone)
    }

    fun requestOtp(phone: String, userId: String, method: String, countryCode: String) = viewModelScope.launch {
        SessionManager.getInstance().loginMethod = method
        val lastCount: Int = SessionManager.getInstance().tryCount
        SessionManager.getInstance().tryCount = lastCount + 1
        val request = LoginRequest(
            countryCode = countryCode,
            method = method,
            action = AuthAction.LOGIN_OTP.value,
            deviceId = SessionManager.getInstance().deviceGUID,
            clientId = BukuWarungKeys.clientId.orEmpty(),
            clientSecret = BukuWarungKeys.clientSecret.orEmpty(),
        )
        when (val result = loginRepositoryOld.getOtp(request, phone)) {
            is ApiSuccessResponse -> {
                if(result?.body?.token != null) {
                    SessionManager.getInstance().opToken = result.body.token
                    produce { state ->
                        state.copy(goToVerifyOtp = true)
                    }
                } else {
                    produce {
                        state ->
                        state.copy(error = ResponseError(-1,result.body.message,null,true))
                    }
                }
            }
            is ApiErrorResponse -> {
                    produce {
                            state ->
                        state.copy(error = ResponseError(result.statusCode,result.errorMessage,null,true))
                    }
            }

            is ApiEmptyResponse -> {}
        }
    }

    private fun registerForgotPasswordVerifyOtp(status: String, failureReason: String) {
        val propBuilder = AppAnalytics.PropBuilder()
        propBuilder.put(AnalyticsConst.STATUS, status)
        propBuilder.put(AnalyticsConst.FAIL_REASON, failureReason)
        AppAnalytics.trackEvent(AnalyticsConst.EVENT_FORGOT_PASSWORD_VERIFY_OTP, propBuilder)
    }

    fun resetProceedWithLogin() {
        produce { state -> state.copy(proceedWithLogin = false) }
    }

    fun setFallbackToNumericCaptcha() {
        produce { state ->
            state.copy(fallbackToNumericCaptcha = true)
        }
    }

    fun setLoginError(error: ResponseError?) {
        produce { state ->
            state.copy(error = error)
        }
    }

}
