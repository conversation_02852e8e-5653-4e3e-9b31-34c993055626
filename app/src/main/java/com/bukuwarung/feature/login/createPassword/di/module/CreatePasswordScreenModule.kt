package com.bukuwarung.feature.onboarding.form.di.module

import androidx.lifecycle.SavedStateHandle
import com.bukuwarung.base.udf.api.state.StateManager
import com.bukuwarung.base.udf.implementation.state.DefaultStateManager
import com.bukuwarung.feature.login.createPassword.screen.CreatePasswordScreenState
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.components.ViewModelComponent

@Module
@InstallIn(ViewModelComponent::class)
object CreatePasswordScreenModule {
    @Provides
    fun provideCreatePasswordScreenState(): CreatePasswordScreenState {
        return CreatePasswordScreenState(
            totalPages = -1,
            currentPage = 0,
            isFormCompleted = false,
            isHandleNavigation = false,
            selectedBusinessName = "",
            tabRedirection = "",
            createPasswordData = null,
            error = null
        )
    }

    @Provides
    fun provideStateManager(
        initState: CreatePasswordScreenState,
        savedStateHandle: SavedStateHandle,
    ): StateManager<CreatePasswordScreenState> {
        return DefaultStateManager(initState, savedStateHandle)
    }
}
