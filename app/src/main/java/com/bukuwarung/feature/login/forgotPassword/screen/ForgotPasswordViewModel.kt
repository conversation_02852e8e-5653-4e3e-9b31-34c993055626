package com.bukuwarung.feature.login.createPassword.screen

import android.app.Activity
import androidx.lifecycle.viewModelScope
import com.bukuwarung.BuildConfig
import com.bukuwarung.BukuWarungKeys
import com.bukuwarung.base.data.api.Response
import com.bukuwarung.base.telemetry.api.Telemetry
import com.bukuwarung.base.telemetry.di.qualifier.AmplitudeTelemetry
import com.bukuwarung.base.telemetry.di.qualifier.AppsFlyerTelemetry
import com.bukuwarung.base.telemetry.di.qualifier.FirebaseTelemetry
import com.bukuwarung.base.tracker.api.Tracker
import com.bukuwarung.base.tracker.di.qualifier.SurvicateTracker
import com.bukuwarung.base.udf.api.screen.UdfViewModel
import com.bukuwarung.base.udf.api.state.StateManager
import com.bukuwarung.data.analytics.api.AnalyticsRepository
import com.bukuwarung.data.app.api.AppRepository
import com.bukuwarung.data.password.api.LoginRepository
import com.bukuwarung.data.password.api.model.UpdatePasswordRequest
import com.bukuwarung.data.password.api.model.UpdatePasswordResponse
import com.bukuwarung.data.user.api.UserRepository
import com.bukuwarung.session.SessionManager
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class ForgotPasswordViewModel @Inject constructor(
    stateManager: StateManager<ForgotPasswordScreenState>,
    private val appRepository: AppRepository,
    private val analyticsRepository: AnalyticsRepository,
    private val loginRepository: LoginRepository,
    private val userRepository: UserRepository,
    @SurvicateTracker private val tracker: Tracker,
    @AmplitudeTelemetry private val amplitudeTelemetry: Telemetry,
    @AppsFlyerTelemetry private val appsFlyerTelemetry: Telemetry,
    @FirebaseTelemetry private val firebaseTelemetry: Telemetry,
) : UdfViewModel<ForgotPasswordScreenState>(stateManager) {

    private var countryCode = SessionManager.getInstance().countryCode

    fun recordAnalytics(event: String, parameter: Map<String, Any>) {
        amplitudeTelemetry.record(event, parameter)
        appsFlyerTelemetry.record(event, parameter)
        firebaseTelemetry.record(event, parameter)
    }

    fun recordProfile(parameter: Map<String, Any>) {
        amplitudeTelemetry.recordProfile(parameter)
    }

    fun trackActivity(activity: Activity, event: String, parameter: Map<String, Any>) {
        tracker.track(activity, event, parameter)
    }

    fun postAppsFlyerId() {
        viewModelScope.launch {
            val userId = userRepository.readUserId()
            val advertisingId = appRepository.readAdvertisingId()

            analyticsRepository.postAppsFlyerId(userId, advertisingId)
        }
    }

    fun updatePassword(phone: String, countryCode: String, password: String) {
        produce { state ->
            state.copy(error = null)
        }
        viewModelScope.launch {
            val request = UpdatePasswordRequest(
                countryCode = countryCode,
                phone = phone,
                newPassword = password,
                client = BukuWarungKeys.clientId.orEmpty(),
                clientSecret = BukuWarungKeys.clientSecret.orEmpty(),
                platform = BuildConfig.PLATFORM,
                origin = BuildConfig.ORIGIN,
                device = SessionManager.getInstance().deviceGUID
            )
            val response = loginRepository.updatePassword(request)
            handleUpdatePasswordResponse(response)
        }
    }

    private fun handleUpdatePasswordResponse(response: Response<UpdatePasswordResponse>) {
        produce { state ->
            if (response is Response.Success) {
                state.copy(updatePasswordResponse = response.data)
            } else if (response is Response.Error) {
                state.copy(error = response)
            } else {
                state.copy()
            }
        }
    }

}
