package com.bukuwarung.feature.onboarding.form.di.module

import androidx.lifecycle.SavedStateHandle
import com.bukuwarung.base.udf.api.state.StateManager
import com.bukuwarung.base.udf.implementation.state.DefaultStateManager
import com.bukuwarung.feature.login.createPassword.screen.ForgotPasswordScreenState
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.components.ViewModelComponent

@Module
@InstallIn(ViewModelComponent::class)
object ForgotPasswordScreenModule {
    @Provides
    fun provideCreatePasswordScreenState(): ForgotPasswordScreenState {
        return ForgotPasswordScreenState(
            totalPages = -1,
            currentPage = 0,
            isFormCompleted = false,
            isHandleNavigation = false,
            selectedBusinessName = "",
            tabRedirection = "",
            updatePasswordResponse = null,
            error = null
        )
    }

    @Provides
    fun provideStateManager(
        initState: ForgotPasswordScreenState,
        savedStateHandle: SavedStateHandle,
    ): StateManager<ForgotPasswordScreenState> {
        return DefaultStateManager(initState, savedStateHandle)
    }
}
