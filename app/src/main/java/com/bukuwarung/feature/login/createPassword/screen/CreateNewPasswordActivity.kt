package com.bukuwarung.feature.login.createPassword.screen

import android.app.Activity
import android.content.Intent
import android.graphics.PorterDuff
import android.graphics.PorterDuffColorFilter
import android.os.Bundle
import android.text.Editable
import android.text.InputType
import android.text.TextWatcher
import android.util.Log
import android.view.View
import android.widget.TextView
import androidx.activity.viewModels
import androidx.core.content.ContextCompat
import com.bukuwarung.R
import com.bukuwarung.activities.home.MainActivity
import com.bukuwarung.activities.onboarding.NewLoginActivity
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.base.udf.api.screen.UdfActivity
import com.bukuwarung.base.udf.api.screen.viewBinding
import com.bukuwarung.base.udf.api.state.observeState
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.constants.AppConst
import com.bukuwarung.constants.RemoteConfigConst
import com.bukuwarung.databinding.ActivityCreateNewPasswordBinding
import com.bukuwarung.feature.onboarding.form.newScreen.NewOnBoardingFormActivity
import com.bukuwarung.feature.onboarding.form.screen.OnBoardingFormActivity
import com.bukuwarung.neuro.api.Neuro
import com.bukuwarung.session.SessionManager
import com.bukuwarung.ui_component.component.button.BukuButton
import com.bukuwarung.ui_component.utils.hideView
import com.bukuwarung.ui_component.utils.showView
import com.bukuwarung.utils.LoginUtils
import com.bukuwarung.utils.RemoteConfigUtils
import com.bukuwarung.utils.Utility
import com.google.api.Context
import dagger.hilt.android.AndroidEntryPoint
import java.util.regex.Matcher
import java.util.regex.Pattern
import javax.inject.Inject


@AndroidEntryPoint
class CreateNewPasswordActivity : UdfActivity() {

    val PASSWORD_PATTERN = "^(?=.*[0-9])(?=.*[a-z])(?=.*[A-Z])(?=.*[.,_!~*@#%&+])(?=\\S+$).{8,}$"
    val PASSWORD_PATTERN_MINIMUM_CHARACTER = "^(?=\\S+$).{8,}$"
    val PASSWORD_PATTERN_LOWER_CASE = ".*[a-z].*"
    val PASSWORD_PATTERN_UPPER_CASE = ".*[A-Z].*"
    val PASSWORD_PATTERN_NUMBER = ".*[0-9].*"
    val PASSWORD_PATTERN_SPECIAL_CHARACTER = ".*[.,_!~*@#%&+].*"
    var SPECIAL_CHARACTERS_NOT_ALLOWED = " $'()/:;<=>?[]^`{|}"
    val patternArraylist = ArrayList<String>()
    private var isPasswordVisible: Boolean = false
    private var isConfirmPasswordVisible: Boolean = false



    @Inject
    lateinit var neuro: Neuro

    private val viewBinding: ActivityCreateNewPasswordBinding by viewBinding()

    private val viewModel: CreateNewPasswordViewModel by viewModels()

    private var phone = ""
    private var countryCode = ""

    private fun setupView() {
        registerVisitPageEvent()

        showScreen()
        viewBinding.btnEnter.disableButton()


        viewBinding.toolbar.setNavigationOnClickListener({
            // back button pressed
            val intent = Intent(this, NewLoginActivity::class.java)
            intent.flags = Intent.FLAG_ACTIVITY_CLEAR_TOP or Intent.FLAG_ACTIVITY_SINGLE_TOP
            startActivity(intent)
            finish()
        })

        viewBinding.btnRetry.setOnClickListener{
            showProgressState()
            viewBinding.btnEnter.performClick()
        }

        phone = intent.getStringExtra(VERIFY_OTP_PARAM_PHONE) as String
        countryCode = intent.getStringExtra(VERIFY_OTP_PARAM_COUNTRY_CODE) as String

        viewBinding.btnEnter.setOnClickListener {

            if(validateNoOtherSpecialCharacter(viewBinding.tvPassword.text.toString())){
                viewBinding.tvPasswordError.showView()
                viewBinding.tvPasswordError.text = getString(R.string.other_special_characters_error)
                registerPasswordAlertAppear(AnalyticsConst.UNACCEPTED_SPECIAL_CHAR)
                return@setOnClickListener
            }else{
                viewBinding.tvPasswordError.hideView()
            }

            if(viewBinding.tvPassword.text.toString().equals(viewBinding.tvConfirmPassword.text.toString())){
                viewBinding.alert.hideView()
                viewBinding.tvConfirmPasswordError.hideView()
                viewModel.createNewPassword(phone,viewBinding.tvPassword.text.toString())
                showProgressState()
            }
            else{
                registerPasswordAlertAppear(AnalyticsConst.PASSWORD_UNMATCH)
                viewBinding.tvConfirmPasswordError.showView()
            }

        }

        viewBinding.passwordLayout.setEndIconOnClickListener {
            isPasswordVisible = !isPasswordVisible
            if(isPasswordVisible) {
                viewBinding.tvPassword.inputType = InputType.TYPE_TEXT_VARIATION_VISIBLE_PASSWORD
            }else{
                viewBinding.tvPassword.inputType = AppConst.INPUT_TYPE_PASSWORD
            }
            registerPasswordVisiblityClick("new_password",isPasswordVisible)
        }

        viewBinding.userNameLayout.setEndIconOnClickListener {
            isConfirmPasswordVisible = !isConfirmPasswordVisible
            if(isConfirmPasswordVisible) {
                viewBinding.tvConfirmPassword.inputType = InputType.TYPE_TEXT_VARIATION_VISIBLE_PASSWORD
            }else{
                viewBinding.tvConfirmPassword.inputType = AppConst.INPUT_TYPE_PASSWORD
            }
            registerPasswordVisiblityClick("confirm_password",isConfirmPasswordVisible)
        }

        val textWatcher : TextWatcher = object : TextWatcher {

            override fun afterTextChanged(s: Editable) {}

            override fun beforeTextChanged(
                s: CharSequence, start: Int,
                count: Int, after: Int
            ) {
            }

            override fun onTextChanged(
                s: CharSequence, start: Int,
                before: Int, count: Int
            ) {
                Log.d("password ->",viewBinding.tvPassword.text.toString())
                Log.d("cfn password ->",viewBinding.tvConfirmPassword.text.toString())
                if(isValidPassword(viewBinding.tvPassword.text.toString()) && viewBinding.tvConfirmPassword.text.toString().isNotEmpty()){
                    viewBinding.btnEnter.setButtonType(BukuButton.ButtonType.YELLOW_60.string)
                    viewBinding.btnEnter.isEnabled = true
                }else {
                    viewBinding.btnEnter.disableButton()
                }

                if(isValidPassword(viewBinding.tvPassword.text.toString()) &&
                    viewBinding.tvPassword.text.toString().equals(viewBinding.tvConfirmPassword.text.toString())) {
                    viewBinding.tvPasswordMatch.showView()
                    registerPasswordAlertAppear(AnalyticsConst.PASSWORD_MATCH)
                    viewBinding.tvConfirmPasswordError.hideView()
                }else{
                    viewBinding.tvPasswordMatch.hideView()
                }

                updatePasswordRequirement(viewBinding.tvPassword.text.toString())

            }
        }
        viewBinding.tvPassword.addTextChangedListener(textWatcher)
        viewBinding.tvPassword.onFocusChangeListener = View.OnFocusChangeListener { view, hasFocus -> if(hasFocus) registerPasswordFieldClick(AnalyticsConst.NEW_PASSWORD)}


        viewBinding.tvConfirmPassword.addTextChangedListener(textWatcher)
        viewBinding.tvConfirmPassword.onFocusChangeListener = View.OnFocusChangeListener { view, hasFocus -> if(hasFocus) registerPasswordFieldClick(AnalyticsConst.CONFIRM_PASSWORD)}

        patternArraylist.add(PASSWORD_PATTERN_NUMBER)
        patternArraylist.add(PASSWORD_PATTERN_MINIMUM_CHARACTER)
        patternArraylist.add(PASSWORD_PATTERN_LOWER_CASE)
        patternArraylist.add(PASSWORD_PATTERN_UPPER_CASE)
        patternArraylist.add(PASSWORD_PATTERN_SPECIAL_CHARACTER)

    }

    override fun onBackPressed() {
        super.onBackPressed()
        registerOnBackPressClick()
    }

    private fun registerVisitPageEvent() {
        val propBuilder = AppAnalytics.PropBuilder()
        propBuilder.put(AnalyticsConst.ENTRY_POINT2, AnalyticsConst.REGISTRATION_PAGE)
        propBuilder.put(AnalyticsConst.SETTING_REASON, AnalyticsConst.INITIAL)
        AppAnalytics.trackEvent(AnalyticsConst.EVENT_SET_PASSWORD_PAGE_VISIT, propBuilder)
    }
    private fun registerPasswordFieldClick(fieldType : String) {
        val propBuilder = AppAnalytics.PropBuilder()
        propBuilder.put(AnalyticsConst.FIELD, fieldType)
        propBuilder.put(AnalyticsConst.SCREEN_NAME, AnalyticsConst.CREATE_PASSWORD_SCREEN)
        AppAnalytics.trackEvent(AnalyticsConst.EVENT_SET_PASSWORD_BOX_CLICK, propBuilder)
    }

    private fun registerPasswordVisiblityClick(fieldType : String,action : Boolean) {
        val propBuilder = AppAnalytics.PropBuilder()
        propBuilder.put(AnalyticsConst.FIELD, fieldType)
        propBuilder.put(AnalyticsConst.ACTION, if (action) AnalyticsConst.SHOW else AnalyticsConst.HIDE)
        propBuilder.put(AnalyticsConst.SCREEN_NAME, AnalyticsConst.CREATE_PASSWORD_SCREEN)
        AppAnalytics.trackEvent(AnalyticsConst.EVENT_SET_PASSWORD_SHOW_CLICK, propBuilder)
    }

    private fun registerOnBackPressClick() {
        val propBuilder = AppAnalytics.PropBuilder()
        propBuilder.put(AnalyticsConst.SCREEN_NAME, AnalyticsConst.CREATE_PASSWORD_SCREEN)
        AppAnalytics.trackEvent(AnalyticsConst.EVENT_BACK_BUTTON_PRESS, propBuilder)
    }

    private fun registerPasswordAlertAppear(alert : String) {
        val propBuilder = AppAnalytics.PropBuilder()
        propBuilder.put(AnalyticsConst.ALERT, alert)
        propBuilder.put(AnalyticsConst.SCREEN_NAME, AnalyticsConst.CREATE_PASSWORD_SCREEN)
        AppAnalytics.trackEvent(AnalyticsConst.EVENT_SET_PASSWORD_ALERT_APPEAR, propBuilder)
    }

    private fun validateNoOtherSpecialCharacter(str: String): Boolean {
        for (i in 0 until SPECIAL_CHARACTERS_NOT_ALLOWED.length) {
            //Checking if the input string contain any of the specified Characters
            if (str.contains(Character.toString(SPECIAL_CHARACTERS_NOT_ALLOWED.elementAt(i)))) {
                return true
            }
        }
        return false
    }

    private fun updatePasswordRequirement(password: String) {
        patternArraylist.forEach {
            val pattern: Pattern
            val matcher: Matcher
            pattern = Pattern.compile(it)
            matcher = pattern.matcher(password)

            if (it.equals(PASSWORD_PATTERN_NUMBER)) {
                if (matcher.matches()) {
                    enableCriteria(viewBinding.passwordCriteria4, R.color.green_60)
                } else {
                    disableCriteria(viewBinding.passwordCriteria4, R.color.black20)
                }
            }
            if (it.equals(PASSWORD_PATTERN_LOWER_CASE)) {
                if (matcher.matches()) {
                    enableCriteria(viewBinding.passwordCriteria2, R.color.green_60)
                } else {
                    disableCriteria(viewBinding.passwordCriteria2, R.color.black20)
                }
            }
            if (it.equals(PASSWORD_PATTERN_UPPER_CASE)) {
                if (matcher.matches()) {
                    enableCriteria(viewBinding.passwordCriteria3, R.color.green_60)
                } else {
                    disableCriteria(viewBinding.passwordCriteria3, R.color.black20)
                }
            }
            if (it.equals(PASSWORD_PATTERN_MINIMUM_CHARACTER)) {
                if (matcher.matches()) {
                    enableCriteria(viewBinding.passwordCriteria1, R.color.green_60)
                } else {
                    disableCriteria(viewBinding.passwordCriteria1, R.color.black20)
                }
            }
            if (it.equals(PASSWORD_PATTERN_SPECIAL_CHARACTER)) {
                if (matcher.matches()) {
                    enableCriteria(viewBinding.passwordCriteria5, R.color.green_60)
                } else {
                    disableCriteria(viewBinding.passwordCriteria5, R.color.black20)
                }
            }
        }
    }

    private fun disableCriteria(tv: TextView,color: Int) {
        enableCriteria(tv,color)
    }

    private fun enableCriteria(tv : TextView,color : Int) {
        tv.setTextColor(ContextCompat.getColor(this,color))
        val drawables = tv.compoundDrawables
        drawables.forEach {
            it?.setColorFilter(
                PorterDuffColorFilter(
                    ContextCompat.getColor(
                        tv.getContext(),
                        color
                    ), PorterDuff.Mode.SRC_IN
                )
            )
        }
    }

    private fun isValidPassword(password: String) : Boolean {
        val pattern: Pattern
        val matcher: Matcher
        pattern = Pattern.compile(PASSWORD_PATTERN)
        matcher = pattern.matcher(password)
        return matcher.matches()
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
//        setContentView(R.layout.activity_create_new_password)
//        super.onCreate(savedInstanceState)
        setContentView(viewBinding.root)

        setupView()
        observeState(source = viewModel.stateFlow, action = ::render)
    }

    private fun render(state: CreatePasswordScreenState) {
        showScreen()
        handleNavigation(state)
        handlePasswordCreation(state)
        handleError(state)
    }

    private fun handleError(state: CreatePasswordScreenState) {
        if (state.error!=null){
//            viewBinding.alert.addText(state.error.message)
//            viewBinding.alert.showView()
            viewBinding.tvPasswordError.showView()
            viewBinding.tvPasswordError.text = state.error.message
            //TODO text input field border color change for error
//            viewBinding.alertLayout.hideView()
        }
    }

    private fun handlePasswordCreation(state: CreatePasswordScreenState) {

        if(state.createPasswordData==null) return
        if(state.createPasswordData.success==true){
            SessionManager.getInstance().isCreateOrForgotPasswordInitiated = false
            if(!SessionManager.getInstance().hasExistingBusiness() && Utility.isEqual(phone,SessionManager.getInstance().userIdForExistingBusinessCheck) && RemoteConfigUtils.OnBoarding.getOnBoardingVariant() == RemoteConfigConst.USE_NEW_ONBOARDING_VARIANT){
                SessionManager.getInstance().setIsExistingOldUser(false)
                if(RemoteConfigUtils.getOnboardingType()==1){
                    startActivity(Intent(this, NewOnBoardingFormActivity::class.java))
                }else{
                    startActivity(Intent(this, OnBoardingFormActivity::class.java))
                }
                finish()
            }else {
                MainActivity.startActivityAndClearTop(this, LoginUtils.IS_NEW_LOGIN_EXTRA, true)
                MainActivity().sendAppsFlyerId()
                finish()

            }
        }

        registerLoginEvent(state)

    }

    private fun registerLoginEvent(state: CreatePasswordScreenState) {
        val propBuilder = AppAnalytics.PropBuilder()
        propBuilder.put(AnalyticsConst.STATUS, state.createPasswordData?.success == true)
        propBuilder.put(AnalyticsConst.ENTRY_POINT2, AnalyticsConst.REGISTRATION_PAGE)
        propBuilder.put(AnalyticsConst.SETTING_REASON, AnalyticsConst.INITIAL)
        AppAnalytics.trackEvent(AnalyticsConst.EVENT_SET_PASSWORD_SUMIT, propBuilder)
    }

    private fun handleNavigation(state: CreatePasswordScreenState) {
        if (!state.isHandleNavigation) return
//        neuro.navigate(
//            context = this,
//            path = "/main",
//            query = mapOf(
//                "IS_NEW_LOGIN_EXTRA" to true,
//                "tab_name" to "HOME",
//            ),
//        )
        finish()
    }

    private fun showScreen() {
        viewBinding.progressbar.visibility = View.GONE
        viewBinding.successLayout.visibility = View.VISIBLE
        viewBinding.serverErrorLayout.visibility = View.GONE
    }

    private fun showErrorState() {
        with(viewBinding) {
            progressbar.hideView()
            serverErrorLayout.showView()
            successLayout.hideView()
        }
    }

    private fun showProgressState() {
        with(viewBinding) {
            progressbar.showView()
            serverErrorLayout.hideView()
            successLayout.hideView()
        }
    }

    companion object {
        const val OTP_LENGTH = 4
        private const val VERIFY_OTP_PARAM_PHONE = "phone"
        private const val VERIFY_OTP_PARAM_OTP = "otp"
        private const val VERIFY_OTP_PARAM_COUNTRY_CODE = "country"
        private const val SHOW_DIALOG = "show_dialog"
        fun createIntent(origin: Activity?, phone: String?, otpCode: String?, countryCode: String): Intent {
            val intent = Intent(origin, CreateNewPasswordActivity::class.java)
            intent.putExtra(VERIFY_OTP_PARAM_PHONE, phone)
            intent.putExtra(VERIFY_OTP_PARAM_OTP, otpCode)
            intent.putExtra(VERIFY_OTP_PARAM_COUNTRY_CODE, countryCode.replace("+", ""))
            return intent
        }
    }

}