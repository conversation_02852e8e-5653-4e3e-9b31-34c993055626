package com.bukuwarung.feature.login.createPassword.screen

import com.bukuwarung.base.data.api.Response
import com.bukuwarung.data.password.api.model.UpdatePasswordResponse
import com.bukuwarung.data.password.api.model.ValidatePasswordResponse
import java.io.Serializable

data class ForgotPasswordScreenState(
    val totalPages: Int,
    val currentPage: Int,
    val isFormCompleted: <PERSON><PERSON>an,
    val isHandleNavigation: <PERSON><PERSON><PERSON>,
    val selectedBusinessName: String,
    val tabRedirection: String,
    val updatePasswordResponse : UpdatePasswordResponse?,
    val error: Response.Error?
) : Serializable
