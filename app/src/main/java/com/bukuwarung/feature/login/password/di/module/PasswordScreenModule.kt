package com.bukuwarung.feature.onboarding.form.di.module

import androidx.lifecycle.SavedStateHandle
import com.bukuwarung.base.udf.api.state.StateManager
import com.bukuwarung.base.udf.implementation.state.DefaultStateManager
import com.bukuwarung.feature.login.password.screen.PasswordScreenState
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.components.ViewModelComponent

@Module
@InstallIn(ViewModelComponent::class)
object PasswordScreenModule {
    @Provides
    fun providePasswordScreenState(): PasswordScreenState {
        return PasswordScreenState(
            totalPages = -1,
            currentPage = 0,
            isFormCompleted = false,
            isHandleNavigation = false,
            selectedBusinessName = "",
            tabRedirection = "",
            passwordValidatedData = null,
            showLoading = false,
            startAfterVerifyOtp=false,
            firebaseAuthError=false,
            successSignInFirebase=false,
            proceedWithLogin = false,
            goToVerifyOtp = false,
            error = null,
            captchaAttempts = 0,
            loginAttempts = 0,
            captchaVariant = -1,
            fallbackToNumericCaptcha = false,
            loginCaptchaError = false,
        )
    }

    @Provides
    fun provideStateManager(
        initState: PasswordScreenState,
        savedStateHandle: SavedStateHandle,
    ): StateManager<PasswordScreenState> {
        return DefaultStateManager(initState, savedStateHandle)
    }
}
