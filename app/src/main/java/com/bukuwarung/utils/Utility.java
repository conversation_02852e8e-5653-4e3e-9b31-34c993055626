package com.bukuwarung.utils;

import static com.bukuwarung.constants.AnalyticsConst.EMPTY;
import static com.bukuwarung.constants.AnalyticsConst.FULLY_FILLED;
import static com.bukuwarung.constants.AnalyticsConst.PARTIALLY_FILLED;
import static com.bukuwarung.constants.AppConst.DEFAULT_BUSINESS_NAME;
import static com.bukuwarung.constants.AppConst.DEFAULT_OWNER_NAME;
import static com.bukuwarung.utils.DateTimeUtils.ONE_MONTH_DURATION_IN_MILLIS;
import static java.lang.Math.ceil;
import static java.lang.Math.floor;

import android.app.Activity;
import android.content.ClipData;
import android.content.ClipboardManager;
import android.content.Context;
import android.content.Intent;
import android.content.pm.ApplicationInfo;
import android.content.pm.PackageInfo;
import android.database.Cursor;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Matrix;
import android.graphics.drawable.Drawable;
import android.media.ExifInterface;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.net.Uri;
import android.os.Build;
import android.provider.MediaStore;
import android.renderscript.Allocation;
import android.renderscript.Element;
import android.renderscript.RenderScript;
import android.renderscript.ScriptIntrinsicBlur;
import android.text.TextUtils;
import android.util.Base64;
import android.view.View;
import android.view.inputmethod.InputMethodManager;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.Nullable;
import androidx.cardview.widget.CardView;
import androidx.core.content.ContextCompat;
import androidx.core.util.Pair;

import com.android.volley.AuthFailureError;
import com.android.volley.Request;
import com.android.volley.RequestQueue;
import com.android.volley.Response;
import com.android.volley.VolleyError;
import com.android.volley.toolbox.StringRequest;
import com.android.volley.toolbox.Volley;
import com.auth0.android.jwt.Claim;
import com.auth0.android.jwt.JWT;
import com.bukuwarung.Application;
import com.bukuwarung.BuildConfig;
import com.bukuwarung.R;
import com.bukuwarung.activities.geolocation.data.model.Address;
import com.bukuwarung.activities.superclasses.AppFragment;
import com.bukuwarung.activities.transaction.customer.add.AddTransactionActivity;
import com.bukuwarung.analytics.AppAnalytics;
import com.bukuwarung.constants.AnalyticsConst;
import com.bukuwarung.database.entity.BookEntity;
import com.bukuwarung.database.entity.CustomerEntity;
import com.bukuwarung.database.entity.UserProfileEntity;
import com.bukuwarung.database.repository.BusinessRepository;
import com.bukuwarung.database.repository.CustomerRepository;
import com.bukuwarung.enums.Language;
import com.bukuwarung.locale.Countries;
import com.bukuwarung.locale.Country;
import com.bukuwarung.preference.AppConfigManager;
import com.bukuwarung.preference.FeaturePrefManager;
import com.bukuwarung.session.SessionManager;
import com.bukuwarung.session.User;
import com.bukuwarung.share.ShareLayoutImage;
import com.google.android.gms.tasks.Task;
import com.google.android.gms.tasks.TaskExecutors;
import com.google.firebase.crashlytics.FirebaseCrashlytics;
import com.google.firebase.crashlytics.internal.common.CommonUtils;

import java.io.BufferedReader;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.text.DecimalFormat;
import java.text.DecimalFormatSymbols;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.Random;
import java.util.regex.Pattern;


public class Utility {

    private final static String alphabet = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ";
    private final static Pattern numberPattern = Pattern.compile("[^0-9+]");
    private final static List<String> profileFields = Arrays.asList(
            "businessName","businessPhone","bookTypeName",
            "operatingDays", "businessAddress",
            "empCount", "outletCount", "establishmentYear","production","productBuyer","monthlyTurnover");
    public final static List<String> profileBasicFields = Arrays.asList("bookName", "bookTypeName", "businessPhone");
    public final static List<String> profileInfoFields = Arrays.asList(
            "operatingDays", "operatingHourStart", "operatingHourEnd", "businessAddress");
    public final static List<String> profileAdditionalInfoFields = Arrays.asList(
            "empCount", "outletCount", "establishmentYear","production","productBuyer","monthlyTurnover");
    public final static List<String> userProfileFields = Arrays.asList(
            "userName", "userPhone", "userEmail","dateOfBirth");

    public static boolean isEqual(Object obj1, Object obj2) {
        if (obj1 == null) {
            return obj2 == null;
        }
        return obj1.equals(obj2);
    }

    public static boolean isBlank(String str) {
        return str == null || str.length() < 1;
    }

    public static boolean canUpdateExistingAmplitudeId(String str){
        //empty userid can be updated
        if(isBlank(str)) return true;
        //amp userId with phone number can be updated as it's not allowed in new amp project
        return str.contains("+");
    }

    public static Uri getImageUri(Context inContext, Bitmap inImage) {
        ByteArrayOutputStream bytes = new ByteArrayOutputStream();
        inImage.compress(Bitmap.CompressFormat.JPEG, 100, bytes);
        String path = MediaStore.Images.Media.insertImage(inContext.getContentResolver(), inImage, "IMG_" + Calendar.getInstance().getTime(), null);
        return Uri.parse(path);
    }

    public static boolean hasInternet() {
        NetworkInfo activeNetworkInfo = ((ConnectivityManager) Application.getAppContext().getSystemService(Context.CONNECTIVITY_SERVICE)).getActiveNetworkInfo();
        return activeNetworkInfo != null && activeNetworkInfo.isConnected();
    }

    public static void hideKeyboard(Activity activity) {
        try {
            ((InputMethodManager) activity.getSystemService(Activity.INPUT_METHOD_SERVICE))
                    .toggleSoftInput(InputMethodManager.SHOW_IMPLICIT, 0);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static int getMatchingLangCd(String selectedLangNm) {
        if (Utility.isEqual(selectedLangNm, Language.ENGLISH.getName())) {
            return Language.ENGLISH.getLangCd();
        } else if (Utility.isEqual(selectedLangNm, Language.INDONESIAN.getName())) {
            return Language.INDONESIAN.getLangCd();
        } else {
            return Language.INDONESIAN.getLangCd();
        }
    }

    public static void copyToClipboard(String text, Context context) {
        copyToClipboard(text, context, context.getString(R.string.text_copied));
    }

    public static void copyToClipboard(String text, Context context, String toastText) {
        final ClipboardManager clipboard = (ClipboardManager) context.getSystemService(Context.CLIPBOARD_SERVICE);
        final ClipData clip = ClipData.newPlainText("Data", text);
        clipboard.setPrimaryClip(clip);
        if(toastText != null && !toastText.isEmpty()) {
            Toast.makeText(context, toastText, Toast.LENGTH_SHORT).show();
        }
    }
    public static long getCurrentTime() {
        return new Date().getTime();
    }

    public static String getCurrentTimeStr() {
        try {
            long time = new Date().getTime();
            return String.valueOf(time);
        } catch (Exception e) {
            e.printStackTrace();
            FirebaseCrashlytics.getInstance().recordException(e);
        }
        return getCurrentTime() + "";
    }

    public static String uuid() {
        return AppIdGenerator.resourceUUID();
    }

    public static int getRandonIdx(int min, int max) {
        Random random = new Random();
        return random.nextInt(max - min) + min;
    }

    public static String getReportId() {
        try {
            String time = String.valueOf(System.currentTimeMillis()).substring(3);
            Random r = new Random();
            time += alphabet.charAt(r.nextInt(alphabet.length()));
            Random ran = new Random();
            int randIdx = ran.nextInt(time.length() - 1);
            char[] reportChars = time.toCharArray();
            reportChars[randIdx] = alphabet.charAt(r.nextInt(alphabet.length()));
            randIdx = ran.nextInt(time.length() - 1);
            reportChars[randIdx] = alphabet.charAt(r.nextInt(alphabet.length()));
            time = String.valueOf(reportChars);
            randIdx = ran.nextInt(time.length() - 1);
            reportChars[randIdx] = alphabet.charAt(r.nextInt(alphabet.length()));
            time = String.valueOf(reportChars);
            return time;
        } catch (Exception e) {
            e.printStackTrace();
            FirebaseCrashlytics.getInstance().recordException(e);
        }
        return System.currentTimeMillis() + "";

    }

    public static String getRefCodeRandomPrefix() {
        Random r = new Random();
        return alphabet.charAt(r.nextInt(alphabet.length())) + "" + alphabet.charAt(r.nextInt(alphabet.length()));
    }

    public static void setBackground(Context context, View view, int i) {
        Drawable mutate = ContextCompat.getDrawable(context, i).mutate();
        if (Build.VERSION.SDK_INT >= 16) {
            view.setBackground(mutate);
        } else {
            view.setBackgroundDrawable(mutate);
        }
    }

    public static File getExtBookImagesFolder() {
        return new File(Application.getAppContext().getExternalFilesDir(null), "images");
    }

    public static String formatInternationalCurrency(Double d) {
        double round =  Math.round(Math.abs(d) * 1000.0d);
        Double.isNaN(round);

        if (SessionManager.getInstance().getCountryCode().equals("+62")) {
            return new DecimalFormat("###,###.###", DecimalFormatSymbols.getInstance(new Locale("in", "ID"))).format(Double.valueOf(round / 1000.0d));
        } else {
            Locale currentLocale = Locale.getDefault();
            Locale.setDefault(Locale.US);
            String formattedStr = priceToString(Double.valueOf(round / 1000.0d), false);
            Locale.setDefault(currentLocale);
            return formattedStr;
        }
    }

    public static String formatNonAbsoluteInternationalCurrency(Double d) {
        double round =  Math.round(d * 1000.0d);
        Double.isNaN(round);

        if (SessionManager.getInstance().getCountryCode().equals("+62")) {
            return new DecimalFormat("###,###.###", DecimalFormatSymbols.getInstance(new Locale("in", "ID"))).format(Double.valueOf(round / 1000.0d));
        } else {
            Locale currentLocale = Locale.getDefault();
            Locale.setDefault(Locale.US);
            String formattedStr = priceToString(Double.valueOf(round / 1000.0d), false);
            Locale.setDefault(currentLocale);
            return formattedStr;
        }
    }

    public static String formatCurrency(Double d) {
//        if (SessionManager.getInstance().getCountryCode().equals("+62") || SessionManager.getInstance().getCountryCode().equals("+62")) {
//            return formatIndonesianCurrency(d);
//        }
        if(d == null) return "";
        return formatInternationalCurrency(d);
    }

    public static String formatNonAbsoluteCurrency(Double d) {
        if(d == null) return "";
        return formatNonAbsoluteInternationalCurrency(d);
    }

    public static String formatCurrencyForEditing(Double d) {
        return new DecimalFormat("#.###", DecimalFormatSymbols.getInstance(Locale.US)).format(Double.valueOf(Math.abs(d.doubleValue())));
    }

    public static String priceWithDecimal(Double price) {
        DecimalFormat formatter = new DecimalFormat("###,###,###.000", DecimalFormatSymbols.getInstance(new Locale("in", "ID")));
        if (!getCurrency().equals("Rp")) {
            formatter = new DecimalFormat("###,###,###.000", DecimalFormatSymbols.getInstance(Locale.US));
        }
        return formatter.format(price);
    }

    public static String priceWithoutDecimal(Double price) {
        DecimalFormat formatter  = new DecimalFormat("###,###,###.###", DecimalFormatSymbols.getInstance(new Locale("in", "ID")));
        if (!getCurrency().equals("Rp")) {
            formatter = new DecimalFormat("###,###,###.###", DecimalFormatSymbols.getInstance(Locale.US));
        }
        return formatter.format(price);
    }

    public static String priceToString(Double price, boolean fromBulk) {
        if (price % 1 != 0 && !fromBulk) {
            return priceWithDecimal(price);
        } else {
            return priceWithoutDecimal(price);
        }
    }

    public static File getCachedImagesFolder() {
        return new File(Application.getAppContext().getCacheDir(), "images");
    }

    public static boolean areEqual(String str1, String str2) {
        if (str1 == null && str2 == null) {
            return true;
        }
        if (str1 == null || str2 == null) {
            return false;
        }
        return str1.equals(str2);
    }

    public static int getTransactionTypeInt(Double bal) {
        return bal.doubleValue() > 0.0d ? 1 : -1;
    }

    public static String getTransactionTypeStr(Double bal) {
        if (bal.doubleValue() == 0.0d) {
            return "Zero";
        }
        return bal.doubleValue() > 0.0d ? "Credit" : "Debit";
    }

    public static String getStorableDateString(Date date) {
        return new SimpleDateFormat("yyyy-MM-dd", Locale.US).format(date);
    }

    public static String getDateString(Date date) {
        return new SimpleDateFormat("dd/MM/yyyy", Locale.US).format(date);
    }

    public static String getReadableTimeString(Long timestamp) {
        if (FeaturePrefManager.getInstance().useMilitaryTime()) {
            return new SimpleDateFormat("HH:mm").format(timestamp);
        } else {
            return new SimpleDateFormat("hh:mm a").format(timestamp);
        }
    }

    public static String getRegularDateString(Date date) {
        return new SimpleDateFormat("dd-MM-yyyy", Locale.US).format(date);
    }

    public static String getRegularDateStringYYYYMMDD(Date date) {
        return new SimpleDateFormat("yyyy-MM-dd", Locale.US).format(date);
    }

    public static String getRegularRangedDateStringForChip(Pair<Long, Long> selectedRange) {
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("dd MMM yy", Locale.getDefault());
            Date firsDate = new Date(selectedRange.first);
            Date secondDate = new Date(selectedRange.second);

            String[] firstDateStr = sdf.format(firsDate).split(" ");
            String firstDateFormatted = new StringBuilder(firstDateStr[0]) // day
                    .append(" ")
                    .append(firstDateStr[1]) // month
                    .append(" ")
                    .append("'")
                    .append(firstDateStr[2]) // year with single-quotation mark
                    .toString();

            String[] secondDateStr = sdf.format(secondDate).split(" ");
            String secondDateFormatted = new StringBuilder(secondDateStr[0]) // day
                    .append(" ")
                    .append(secondDateStr[1]) // month
                    .append(" ")
                    .append("'")
                    .append(secondDateStr[2]) // year with single-quotation mark
                    .toString();
            return firstDateFormatted + "-" + secondDateFormatted;
        } catch (Exception ex) {
            ex.printStackTrace();
            FirebaseCrashlytics.getInstance().recordException(ex);
            return "";
        }
    }

    public static String getRegularDateStringId(Date date) {
        return new SimpleDateFormat("dd MMM yyyy", new Locale("ID", "id")).format(date);
    }

    public static String getCurrency() {
        Country country;
        String str = "Rp";
        List countryList = new Countries().getCountryList();
        int i = 0;
        while (true) {
            if (i >= countryList.size()) {
                country = null;
                break;
            }
            country = (Country) countryList.get(i);
            if (country.getCode().equals(SessionManager.getInstance().getCountryCode())) {
                break;
            }
            i++;
        }
        return country != null ? country.getCurrency() : str;
    }

    public static String cleanPhonenumber(String str) {
        if (isBlank(str)) {
            return str;
        }
        str = str.replace("+65", "");
        str = str.replace("-", "");
        str = str.replace("+81", "");
        str = str.replace("+60", "");
        str = str.replace("+49", "");
        str = str.replace("+7", "");
        str = str.replace("+91", "");
        str = str.replace("+62", "");
        str = str.replace("+1", "");

        StringBuffer sb = new StringBuffer(str);
        while (sb.length() > 1 && sb.charAt(0) == '0')
            sb.deleteCharAt(0);
        if (sb.toString().startsWith("62")) {
            sb.delete(0,2);
        }
        str = sb.toString();
        return numberPattern.matcher(str).replaceAll("");
    }

    public static String beautifyPhoneNumber(String phone) {
        try {
            if (isBlank(phone))
                return "";

            if (phone.startsWith("0") && phone.length() > 8) {
                return phone;
            } else if (phone.startsWith("+62")) {
                phone = phone.replace("+62", "0");
            } else if (phone.length() > 8) {
                phone = "0" + phone;
            }

            return phone;
        } catch (Exception ex) {
            ex.printStackTrace();
            FirebaseCrashlytics.getInstance().log(ex.getMessage());
            return phone;
        }
    }

    public static String getNumberWithCC(String countryCode, String mobileNum) {
        return countryCode + " " + mobileNum;
    }

    public static String getReadableDateString(String str) {
        if (str == null) {
            return str;
        }
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd", Locale.US);
        try {
            return new SimpleDateFormat("dd MMM yyyy").format(simpleDateFormat.parse(str));
        } catch (ParseException e) {
            FirebaseCrashlytics.getInstance().recordException(e);
            return str;
        }
    }

    public static String getReadableDateStringWithDay(String str) {
        if (str == null) {
            return str;
        }
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd", Locale.US);
        try {
            return new SimpleDateFormat("EEEE, dd MMM yyyy").format(simpleDateFormat.parse(str));
        } catch (ParseException e) {
            FirebaseCrashlytics.getInstance().recordException(e);
            return str;
        }
    }

    public static String formatReceiptDate(String str) {
        if (str == null || str == "-") {
            return str;
        }
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd", Locale.getDefault());
        try {
            return new SimpleDateFormat("dd MMM yyyy", Locale.getDefault()).format(simpleDateFormat.parse(str));
        } catch (ParseException e) {
            FirebaseCrashlytics.getInstance().recordException(e);
            return str;
        }
    }

    public static String formatDateWithTime(String str) {
        if (str == null || str == "-") {
            return str;
        }
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss", Locale.getDefault());
        try {
            return new SimpleDateFormat("dd MMM yyyy, HH:mm", Locale.getDefault()).format(simpleDateFormat.parse(str));
        } catch (ParseException e) {
            FirebaseCrashlytics.getInstance().recordException(e);
            return str;
        }
    }

    public static String formatReceiptDateFromHistory(String str, boolean withTime) {
        if (str == null || str == "-") {
            return str;
        }
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("dd MMMM yyyy, HH:mm", Locale.getDefault());
        try {
            if (withTime) {
                return new SimpleDateFormat("dd MMM yyyy HH:mm", Locale.getDefault()).format(simpleDateFormat.parse(str));
            } else {
                return new SimpleDateFormat("dd MMM yyyy", Locale.getDefault()).format(simpleDateFormat.parse(str));
            }
        } catch (ParseException e) {
            FirebaseCrashlytics.getInstance().recordException(e);
            return str;
        }
    }

    public static String formatReceiptTimeromHistory(String str) {
        if (str == null || str == "-") {
            return str;
        }
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("dd MMMM yyyy, HH:mm", Locale.getDefault());
        try {
            return new SimpleDateFormat("HH:mm", Locale.getDefault()).format(simpleDateFormat.parse(str));
        } catch (ParseException e) {
            FirebaseCrashlytics.getInstance().recordException(e);
            return str;
        }
    }

    public static void sendSms(String receiver, Double amount, final int type, final Context context, String reportId, Integer cstLang) {
        sendSms(receiver, amount, type, context, reportId, cstLang, -1);
    }

    public static boolean isValidName(String word) {
        return word.matches("[A-Za-z0-9 ]*");
    }

    public static void sendSms(String receiver, Double amount, final int type, final Context context, String reportId, Integer cstLang, double balance) {
        String amnt = "";
        try {
            if (SessionManager.getInstance().isGuestUser()) return;
            amnt = String.valueOf(Math.abs(amount));
            BookEntity bookSync = BusinessRepository.getInstance(Application.getAppContext()).getBusinessByIdSync(User.getBusinessId());
            RequestQueue queue = Volley.newRequestQueue(context);
            String phoneCd = SessionManager.getInstance().getCountryCode();
            int lang = cstLang == null || cstLang.intValue() == -1 ? SessionManager.getInstance().getAppLanguage() : cstLang;
            String url = "";
            int transType = amount < 0 ? -1 : 1;
            if(type == 5){
                url = AppConfigManager.getInstance().getLunasApi() + "notifyCreditTrans?receiver=" + receiver + "&&sender=" + User
                        .getUserId() + "&&khataName=" + amnt + "&&shopName=" + bookSync.businessName + "&&phoneCd=" + phoneCd + "&&lang=" + lang + "&&reportId=" + reportId + "&&biztype=" + bookSync.bookType + "&&balance=" + balance+ "&&type=" + type;
            }else if(type == 7){
                url = AppConfigManager.getInstance().getTxnApi() + "notifyCreditTrans?receiver=" + receiver + "&&sender=" + User
                        .getUserId() + "&&khataName=" + amnt + "&&shopName=" + bookSync.businessName + "&&phoneCd=" + phoneCd + "&&lang=" + lang + "&&reportId=" + reportId + "&&biztype=" + bookSync.bookType + "&&balance=" + balance+ "&&type=" + type;
            }else if(type == 8){
                url = AppConfigManager.getInstance().getTxnApi() + "notifyCreditTrans?receiver=" + receiver + "&&sender=" + User
                        .getUserId() + "&&khataName=" + amnt + "&&shopName=" + bookSync.businessName + "&&phoneCd=" + phoneCd + "&&lang=" + lang + "&&reportId=" + reportId + "&&biztype=" + bookSync.bookType + "&&balance=" + balance+ "&&type=" + type;
            }else {
                if (type == 0) {
                    url = AppConfigManager.getInstance().getSmsApi() + "sendsms?receiver=" + receiver + "&&sender=" + User
                            .getUserId() + "&&khataName=" + amnt + "&&shopName=" + bookSync.businessName + "&&phoneCd=" + phoneCd + "&&lang=" + lang + "&&reportId=" + reportId + "&&type=" + transType + "&&biztype=" + bookSync.bookType + "&&balance=" + balance;
                } else if (type == 1) {
                    url = AppConfigManager.getInstance().getTxnApi() + "notifyCreditTrans?receiver=" + receiver + "&&sender=" + User
                            .getUserId() + "&&khataName=" + amnt + "&&shopName=" + bookSync.businessName + "&&phoneCd=" + phoneCd + "&&lang=" + lang + "&&reportId=" + reportId + "&&biztype=" + bookSync.bookType + "&&balance=" + balance;
                } else {
                    url = AppConfigManager.getInstance().getTxnApi() + "notifyDebitTrans?receiver=" + receiver + "&&sender=" + User
                            .getUserId() + "&&khataName=" + amnt + "&&shopName=" + bookSync.businessName + "&&phoneCd=" + phoneCd + "&&lang=" + lang + "&&reportId=" + reportId + "&&biztype=" + bookSync.bookType + "&&balance=" + balance;
                }
            }

            // Request a string response from the provided URL.
            StringRequest stringRequest = new StringRequest(Request.Method.GET, url,
                    new Response.Listener<String>() {
                        @Override
                        public void onResponse(String response) {
                            System.out.println(response);
                        }
                    }, new Response.ErrorListener() {
                @Override
                public void onErrorResponse(VolleyError error) {
                    System.out.println("That didn't work!" + error);
                }
            }){
                @Override
                public Map<String, String> getHeaders() throws AuthFailureError {
                    Map<String, String> params = new HashMap<String, String>();
                    params.put("Content-Type", "application/json; charset=UTF-8");
                    params.put("Authorization", "Bearer "+SessionManager.getInstance().getBukuwarungToken());
                    params.put("X-APP-VERSION-NAME", BuildConfig.VERSION_NAME);
                    params.put("X-APP-VERSION-CODE", BuildConfig.VERSION_CODE+"");
                    params.put("buku-origin", "bukuwarung-app");
                    return params;
                }
            };

            // Add the request to the RequestQueue.
            /*
            Customer added, SMS notification sent to the customer..
            Transaction update SMS sent to customer.
            Transaction update SMS sent to customer.
             */
            queue.add(stringRequest);
        } catch (Exception e) {
            e.printStackTrace();
            FirebaseCrashlytics.getInstance().recordException(e);
        }
        return;
    }

    public static long getHash(String str) {
        return (((long) str.hashCode()) << 32) + ((100) - -2147483648L);
    }

    public static String getReportFileName(String fileNm, String ext) {
        return String.format("%s-%s.%s", new Object[]{fileNm, new SimpleDateFormat("dd.MM-HH.mm", Locale.US).format(Long.valueOf(System.currentTimeMillis())), ext});
    }

    public static String formatAmount(@Nullable Double amount) {
        StringBuilder sb = new StringBuilder();
        sb.append(Utility.getCurrency());
        if(amount != null)
            sb.append(Utility.formatCurrency(Double.valueOf(amount)));
        else sb.append("0");
        return sb.toString();
    }

    public static String formatAmountWithoutRp(@Nullable Double amount) {
        StringBuilder sb = new StringBuilder();
        if(amount != null)
            sb.append(Utility.formatCurrency(Double.valueOf(amount)));
        else sb.append("0");
        return sb.toString();
    }

    public static String formatNonAbsoluteAmount(@Nullable Double amount) {
        StringBuilder sb = new StringBuilder();
        sb.append(Utility.getCurrency());
        if(amount != null)
            sb.append(Utility.formatNonAbsoluteCurrency(Double.valueOf(amount)));
        else sb.append("0");
        return sb.toString();
    }

    public static String getReportUrl() {
        int lang = SessionManager.getInstance().getAppLanguage();
        if (SessionManager.getInstance().getCountryCode().equals("+62")) {
            return lang == 1 ? "https://bukuwarung.com/z?ae=%1$s" : BuildConfig.REPORT_URL_ID + "%1$s";
        } else if (SessionManager.getInstance().getCountryCode().equals("+65")) {
            return lang == 1 ? "https://bukuwarung.com/w?ae=%1$s" : BuildConfig.REPORT_URL_ID + "%1$s";
        } else if (SessionManager.getInstance().getCountryCode().equals("+60")) {
            return lang == 1 ? "https://bukuwarung.com/d?ae=%1$s" : "https://bukuwarung.com/p?ae=%1$s";
        } else {
            return lang == 1 ? "https://bukuwarung.com/z?ae=%1$s" : BuildConfig.REPORT_URL_ID + "%1$s";
        }
    }

    public static boolean loginExceeded() {
        return SessionManager.getInstance().getTryCount() > 8;
    }

    public static boolean invalidOTPExceeded() {
        return SessionManager.getInstance().getInvalidOtpCount() > 10;
    }

    public static boolean hasBusinessName(String businessNm) {
        return !Utility.isBlank(businessNm) && !Utility.isBlank(businessNm.replaceAll("\\s", "")) && !businessNm.replaceAll("\\s", "").equalsIgnoreCase(DEFAULT_OWNER_NAME.replaceAll("\\s", "")) && !businessNm.replaceAll("\\s", "").equalsIgnoreCase(DEFAULT_BUSINESS_NAME.replaceAll("\\s", ""));
    }

    public static String getNameInitials(String name) {
        StringBuilder text = new StringBuilder();
        String[] letters = name.split(" ");
        for (String letter : letters) {
            try {
                text.append(letter.charAt(0));
            } catch (Exception ex) {
                ex.printStackTrace();
            }
        }
        String firstLetterText = text.toString();
        firstLetterText = firstLetterText.length() > 2 ? firstLetterText.substring(0, 2) : firstLetterText;
        return firstLetterText;
    }

    public static String getEncodedStr(String str) {
        try {
            byte[] data = str.getBytes(StandardCharsets.UTF_8);
            String base64 = Base64.encodeToString(data, Base64.DEFAULT);
            return base64;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public static String getCurrentYearStr() {
        return new SimpleDateFormat("yyyy", Locale.US).format(new Date());
    }

    public static String getYearStr(int year) {
        try {
            Calendar calendar = Calendar.getInstance();
            calendar.set(Calendar.YEAR, year);
            return new SimpleDateFormat("yyyy", Locale.US).format(calendar.getTime());
        } catch (Exception ex) {
            ex.printStackTrace();
            return "";
        }
    }

    public static int getCurrentMonthInt() {
        try {
            Calendar calendar = Calendar.getInstance();
            int year = calendar.get(Calendar.MONTH);
            return year;
        } catch (Exception ex) {
            ex.printStackTrace();
            return 1;
        }
    }

    public static int getCurrentYearInt() {
        try {
            Calendar calendar = Calendar.getInstance();
            int year = calendar.get(Calendar.YEAR);
            return year;
        } catch (Exception ex) {
            ex.printStackTrace();
            return 1;
        }
    }

    public static String getCurrentMonthYearStr() {
        return new SimpleDateFormat("MMM yyyy", Locale.US).format(new Date());
    }

    public static String getMonthYearStr(int month, int year) {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.MONTH, month);
        calendar.set(Calendar.YEAR, year);
        return new SimpleDateFormat("MMM yyyy", Locale.US).format(calendar.getTime());
    }

    public static String getMonthStr(int month) {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.MONTH, month);
        return new SimpleDateFormat("MMMM", new Locale("ID", "ID")).format(calendar.getTime());
    }

    /**
     * Example Input:
     * Rp 20.000,96
     * Example Output:
     * 20000.96
     *
     * @return double amount
     */
    public static double extractAmountFromText(String input) {
        try {
            // remove currency if any
            String currency = getCurrency();
            input = input.replace(currency, "").replace(" ", "");
            String cleansedString = Utility.isBlank(input) || input.equalsIgnoreCase("0")
                    ? "0"
                    : SessionManager.getInstance().getCountryCode().contains("62")
                    ? input.replace(".", "")
                    .replace(",", ".")
                    : input.replace(",", "");
            return Double.valueOf(cleansedString);
        } catch (Exception ex) {
            ex.printStackTrace();
            return 0;
        }
    }

    public static String getRoundedOffPrice(Double totalStock) {
        DecimalFormat df = new DecimalFormat("#####.###");
        if (totalStock == floor(totalStock) && !totalStock.isInfinite()) {
            int stock = totalStock.intValue();
            return String.valueOf(stock).replace(".", ",");
        }
        return df.format(totalStock).replace(".", ",");
    }

    public static String getConfigCode() {
        try {
            StringBuilder configCd = new StringBuilder();
            String lastDigit = User.getUserId().charAt(User.getUserId().length() - 1)+"";
            configCd.append(AppConfigManager.getInstance().getDefaultTabLogin() <= 0 ? 2 : 1);
            configCd.append(AppConfigManager.getInstance().getDefaultCash() < 0 ? 2 : 1);
            return configCd.toString()+lastDigit+(RemoteConfigUtils.TransactionTabConfig.INSTANCE.canShowOldTransactionForm() ? 1 : 0);
        } catch (Exception e) {
            e.printStackTrace();
            return "DEFAULT";
        }
    }

    public static boolean isSystemPackage(PackageInfo pkgInfo) {
        return ((pkgInfo.applicationInfo.flags & ApplicationInfo.FLAG_SYSTEM) != 0);
    }

    public static void callCustomer(Activity activity, String phoneNumber) {
        if (Build.VERSION.SDK_INT >= 23) {
            if (PermissonUtil.hasCallPermission()) {
                Intent intent = new Intent("android.intent.action.CALL");
                StringBuilder sb = new StringBuilder();
                sb.append("tel:");
                if (phoneNumber.length() <= 9) {
                    sb.append(phoneNumber);
                } else {
                    sb.append(phoneNumber);
                }
                intent.setData(Uri.parse(sb.toString()));
                activity.startActivity(intent);
                return;
            }
            activity.requestPermissions(new String[]{"android.permission.CALL_PHONE"}, 320);
            return;
        }
        Intent callIntent = new Intent("android.intent.action.CALL");
        StringBuilder sb2 = new StringBuilder();
        sb2.append("tel:");
        sb2.append(phoneNumber);
        callIntent.setData(Uri.parse(sb2.toString()));
        activity.startActivity(callIntent);
    }

    public static void makeCall(Activity activity, String phone) {
        Intent intent = new Intent(Intent.ACTION_DIAL);
        StringBuilder phoneSb = new StringBuilder("tel:");
        Utility.getCurrency();
        phoneSb.append("0");
        phoneSb.append(phone);
        intent.setData(Uri.parse(phoneSb.toString()));
        activity.startActivity(intent);
    }

    public static boolean isRooted(Context context) {
        try {
            return CommonUtils.isRooted() || checkSuBinary(context) || checkSuProcess(context);
        } catch (Exception ex) {
            return false;
        }
    }

    private static boolean checkSuBinary(Context context) {
        boolean isEmulator = CommonUtils.isEmulator();
        String[] paths = new String[]{
                "/system/app/Superuser.apk",
                "/sbin/su",
                "/system/bin/su",
                "/system/xbin/su",
                "/data/local/xbin/su",
                "/data/local/bin/su",
                "/system/sd/xbin/su",
                "/system/bin/failsafe/su",
                "/data/local/su",
                "/su/bin/su"
        };
        for (String path : paths) {
            // return true if file exists but the device is not an emulator
            if (new File(path).exists()) return !isEmulator;
        }
        return false;
    }

    private static boolean checkSuProcess(Context context) {
        boolean isEmulator = CommonUtils.isEmulator();
        Process process = null;
        try {
            process = Runtime.getRuntime().exec(new String[]{"/system/xbin/which", "su"});
            BufferedReader input = new BufferedReader(new InputStreamReader(process.getInputStream()));
            return input.readLine() != null && !isEmulator;
        } catch (Throwable ex) {
            return false;
        } finally {
            if (process != null) {
                process.destroy();
            }
        }
    }

    public static String getPathFromUri(Uri uri) {
        if (uri == null) {
            return null;
        }

        String[] projection = {MediaStore.Images.Media.DATA};
        Cursor cursor = Application.getAppContext().getContentResolver().query(uri, projection, null, null, null);
        if (cursor == null) return null;
        int column_index = cursor.getColumnIndexOrThrow(MediaStore.Images.Media.DATA);
        cursor.moveToFirst();
        String s = cursor.getString(column_index);
        cursor.close();
        return s;
    }

    public static Bitmap captureScreenshot(View view) {
        /*
         * Creating a Bitmap of view with ARGB_4444.
         * */
        int width = Math.max(view.getWidth(), 80);
        int height = Math.max(view.getHeight(), 80);
        Bitmap bitmap = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_4444);
        Canvas canvas = new Canvas(bitmap);
        Drawable backgroundDrawable = view.getBackground();
        if (backgroundDrawable != null) {
            backgroundDrawable.draw(canvas);
        } else {
            canvas.drawColor(Color.parseColor("#80000000"));
        }
        view.draw(canvas);
        return bitmap;
    }

    public static Bitmap blur(Context context, Bitmap image) {
        float BITMAP_SCALE = 0.4f;
        float BLUR_RADIUS = 7.5f;

        int width = Math.round(image.getWidth() * BITMAP_SCALE);
        int height = Math.round(image.getHeight() * BITMAP_SCALE);

        Bitmap inputBitmap = Bitmap.createScaledBitmap(image, width, height, false);
        Bitmap outputBitmap = Bitmap.createBitmap(inputBitmap);
        RenderScript rs = RenderScript.create(context);
        ScriptIntrinsicBlur theIntrinsic = ScriptIntrinsicBlur.create(rs, Element.U8_4(rs));
        Allocation tmpIn = Allocation.createFromBitmap(rs, inputBitmap);
        Allocation tmpOut = Allocation.createFromBitmap(rs, outputBitmap);
        theIntrinsic.setRadius(BLUR_RADIUS);
        theIntrinsic.setInput(tmpIn);
        theIntrinsic.forEach(tmpOut);
        tmpOut.copyTo(outputBitmap);
        return outputBitmap;
    }

    public static void trackTransactionCount() {
        try {
            FeaturePrefManager.getInstance().exitWithoutTransaction(0);
            FeaturePrefManager.getInstance().setEnableGamifyDialog(1);
            FeaturePrefManager.getInstance().setNewInstallTransactionCount(FeaturePrefManager.getInstance().getNewInstallTransactionCount() + 1);
            FeaturePrefManager.getInstance().setMonthlyPUTargetProgress(FeaturePrefManager.getInstance().getMonthlyPUTargetProgress() + 1);
            //use created target records in first month
            if(!FeaturePrefManager.getInstance().hasRecordedM1Record() && DateTimeUtils.isSecondMonthOfInstall() && FeaturePrefManager.getInstance().getNewInstallTransactionCount()>0){
                AppAnalytics.trackEvent("M1_transaction_created");
                FeaturePrefManager.getInstance().hasRecordedM1Record(true);
            }

            if(DateTimeUtils.getDuration(FeaturePrefManager.getInstance().getLastMonthlyTargetTime(),getCurrentTime())>ONE_MONTH_DURATION_IN_MILLIS){
                if(FeaturePrefManager.getInstance().getMonthlyPUTargetProgress()>10) {
                    AppAnalytics.trackEvent("monthly_power_user_goal_reached");
                }
                //reset monthly power user target since time difference is more than 1 month
                FeaturePrefManager.getInstance().setMonthlyPUTargetProgress(0);
                FeaturePrefManager.getInstance().setLastMonthlyTargetTime(System.currentTimeMillis());
            }

            if (FeaturePrefManager.getInstance().getInstallTimestamp() < 0 || FeaturePrefManager.getInstance().getNewInstallTransactionCount() > 10) {
                return;
            }
            if(FeaturePrefManager.getInstance().getNewInstallTransactionCount() == 10){
                AppAnalytics.trackEvent("new_power_user_goal_reached");
            }
            //user created few records within first day
            if (DateTimeUtils.isFirstDayOfInstall() && FeaturePrefManager.getInstance().getNewInstallTransactionCount() == 4) {
                AppAnalytics.trackEvent("D0_transaction_goal_reached");
            }else if(!FeaturePrefManager.getInstance().hasRecordedD1Record() && DateTimeUtils.isSecondDayOfInstall() && FeaturePrefManager.getInstance().getNewInstallTransactionCount()>0){
                AppAnalytics.trackEvent("D1_transaction_created");
                FeaturePrefManager.getInstance().hasRecordedD1Record(true);
            }
            //use created target records in first week
            if(!FeaturePrefManager.getInstance().hasRecordedW1Record() && DateTimeUtils.isSecondWeekOfInstall() && FeaturePrefManager.getInstance().getNewInstallTransactionCount()>0){
                FeaturePrefManager.getInstance().hasRecordedW1Record(true);
            }

        } catch (Exception e) {
            FirebaseCrashlytics.getInstance().log(e.getMessage());
        }
    }

    public static Bitmap fixImageRotation(Context context, Bitmap bitmap, File imageFile, Uri uri) {
        try {

            int imageRotation = 0;
            if (imageFile == null) {
                File tempFile = ImageUtils.createTempFile();
                InputStream inputStream = context.getContentResolver().openInputStream(uri);
                OutputStream outputStream = new FileOutputStream(tempFile);
                byte[] buffer = new byte[1024];
                int length;
                while ((length = inputStream.read(buffer)) > 0) {
                    outputStream.write(buffer, 0, length);
                }

                outputStream.close();
                inputStream.close();

                imageRotation = getImageRotation(tempFile);

            } else {
                imageRotation = getImageRotation(imageFile);
            }

            if (imageRotation != 0) {
                bitmap = getBitmapRotatedByDegree(bitmap, imageRotation);
            }
        } catch (Exception ex) {
            FirebaseCrashlytics.getInstance().recordException(ex);
        }

        return bitmap;
    }

    private static int getImageRotation(final File imageFile) {

        ExifInterface exif = null;
        int exifRotation = 0;

        try {
            exif = new ExifInterface(imageFile.getPath());
            exifRotation = exif.getAttributeInt(ExifInterface.TAG_ORIENTATION, ExifInterface.ORIENTATION_NORMAL);
        } catch (IOException e) {
            e.printStackTrace();
        }

        if (exif == null)
            return 0;
        else
            return exifToDegrees(exifRotation);
    }

    private static int exifToDegrees(int rotation) {
        if (rotation == ExifInterface.ORIENTATION_ROTATE_90)
            return 90;
        else if (rotation == ExifInterface.ORIENTATION_ROTATE_180)
            return 180;
        else if (rotation == ExifInterface.ORIENTATION_ROTATE_270)
            return 270;

        return 0;
    }

    private static Bitmap getBitmapRotatedByDegree(Bitmap bitmap, int rotationDegree) {
        Matrix matrix = new Matrix();
        matrix.preRotate(rotationDegree);

        return Bitmap.createBitmap(bitmap, 0, 0, bitmap.getWidth(), bitmap.getHeight(), matrix, true);
    }

    public static String generateProductCode(String name) {
        try {
            String number = String.format("%03d", FeaturePrefManager.getInstance().getProductCount());
            if (isBlank(name)) {
                return "P-" + number;
            }
            String initial = name.length() >= 3 ? name.substring(0, 3).toUpperCase() : name.toUpperCase();
            return initial + "-" + number;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "";
    }

    public static BigDecimal valueOf(String balanceAmount) {
        balanceAmount = balanceAmount.substring(3).replace(".","");
        return BigDecimal.valueOf(Long.parseLong(balanceAmount));
    }
    public static String dashDividedString(String str1, String str2) {
        return String.format("%s - %s", str1, str2);
    }

    public static String removePrecisionIfZero(double unitPrice)  {
        try {
            if (unitPrice == 0.0) return "0";
            if (ceil(unitPrice) == floor(unitPrice)) {
               return String.format("%.0f", unitPrice);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Double.toString(unitPrice);
    }

    public static String cleanBalance(String price) {
        if (SessionManager.getInstance().getCountryCode().equals("+62")) {
            return price.replace(".", "")
                    .replace(",", ".");
        }
        return price.replace(".", "")
                .replace(",", "");
    }

    private static String cleanBalanceAndSpace(String price) {
        return cleanBalance(price).replace(" ", "");
    }

    public static Double getAmountInDouble(String price) {
        String cleanBalance = cleanBalanceAndSpace(price);
        if (cleanBalance.isEmpty() || cleanBalance.length() <= 2) return 0.0;
        return Double.parseDouble(cleanBalance.substring(2));
    }

    public static String getQuantityTypeFromTotalStock(Double totalStock) {
        if (totalStock == floor(totalStock) && !totalStock.isInfinite()) {
            return AnalyticsConst.INTEGER;
        }
        return AnalyticsConst.DECIMAL;
    }

    public static void shareTransaction(AppFragment fragment, Context context, View view, CustomerEntity customerEntity, boolean withWhatsApp, String balanceAmount, String paymentLink) {
        try {

            TextView tvWarungName = view.findViewById(R.id.shopName);
            TextView tvWarungPhone = view.findViewById(R.id.shopPhone);
            TextView tvDueAmount = view.findViewById(R.id.dueAmount);
            TextView tvReminder = view.findViewById(R.id.reminderExplain);

            BookEntity bookEntity = fragment.getCurrentBook();

            String amount = Utility.getCurrency() + Utility.formatCurrency(Math.abs(customerEntity.balance));
            if (bookEntity != null) {
                if (Utility.isBlank(bookEntity.bookName)) {
                    tvWarungName.setText("-");
                } else {
                    tvWarungName.setText(bookEntity.bookName);
                }
                tvWarungPhone.setText(Utility.beautifyPhoneNumber(fragment.getCurrentBook().businessPhone));
                if (SessionManager.getInstance().isGuestUser()) {
                    tvWarungPhone.setText("-");
                }
                if (customerEntity.balance != null) {
                    tvDueAmount.setText(amount);

                    String message = context.getString(R.string.payment_reminder_string);
                    String date = DateTimeUtils.getReadableReminderDate(System.currentTimeMillis());
                    String formattedMessage = String.format(message, amount, date);
                    tvReminder.setText(formattedMessage);
                }
            }
            shareReceipt(context, view, customerEntity,withWhatsApp, paymentLink, amount);

        } catch (Exception ex) {
            Toast.makeText(context, "Terjadi kesalahan", Toast.LENGTH_SHORT).show();
            FirebaseCrashlytics.getInstance().recordException(ex);
        }
    }

    public static void shareReceipt(Context context, View view, CustomerEntity customerEntity,boolean withWhatsApp,String paymentLink, String amount) {
        Double balance;
        try {
            balance = Utility.extractAmountFromText(amount);
        } catch (Exception e) {
            balance = customerEntity.balance;
        }
        CardView layoutWhatsAppPreview = view.findViewById(R.id.layoutWhatsAppPreview);
        Task<ImageUtils.SaveToDiskTaskResult> saveViewSnapshot = ImageUtils.saveLayoutConvertedImage(layoutWhatsAppPreview, false);
        CustomerEntity customer = CustomerRepository.getInstance(context).getCustomerById(customerEntity.customerId);
        ShareLayoutImage shareLayoutImage = new ShareLayoutImage(ShareUtils.getReminderSharingText(context, AddTransactionActivity.getReportUrl(customerEntity),
                balance, customerEntity.name, customer.language, paymentLink), context, "com.whatsapp", customerEntity.phone,
                    withWhatsApp, false);
        saveViewSnapshot.continueWith(TaskExecutors.MAIN_THREAD, shareLayoutImage);
    }

    private static String getReportUrl(CustomerEntity customerEntity) {
        StringBuilder sb = new StringBuilder();
        if (!TextUtils.isEmpty(customerEntity.customerId)) {
            String format = String.format(Utility.getReportUrl(), customerEntity.altCustomerId);
            sb.append(format);
            sb.append("\n");
        } else {
            return "https://bukuwarung.com/app";
        }
        return sb.toString();
    }

    public static int calculateUserProfileCompletionPercentage(UserProfileEntity userProfileObject) {
        int filledPercentage = 0;
        if (userProfileObject.getUserName()!=null && !userProfileObject.getUserName().isEmpty()) {
            filledPercentage = filledPercentage + 20;
        }
        if (userProfileObject.getUserPhone()!=null && !userProfileObject.getUserPhone().isEmpty()) {
            filledPercentage = filledPercentage + 20;
        }
        if (userProfileObject.getUserEmail()!=null && !userProfileObject.getUserEmail().isEmpty()) {
            filledPercentage = filledPercentage + 20;
        }
        if (userProfileObject.getDateOfBirth()!=null && !userProfileObject.getDateOfBirth().isEmpty()) {
            filledPercentage = filledPercentage + 20;
        }
        if (userProfileObject.getGender()!=null && !userProfileObject.getGender().isEmpty()) {
            filledPercentage = filledPercentage + 20;
        }
        return filledPercentage;
    }


    public static int calculateCompletionPercentage(Object instance) {
        int filledClassFieldCount = 0;
        try {
            for (String field : profileFields) {
                Object object = instance.getClass().getDeclaredField(field).get(instance);
                boolean isNull = Objects.isNull(object);
                if(object instanceof Integer){
                    isNull = (Integer)object == 0;
                }else if(object instanceof String){
                    isNull = isBlank(object.toString());
                }
                if (!isNull) {
                    filledClassFieldCount++;
                }
            }
        }  catch (IllegalAccessException | NoSuchFieldException | NullPointerException e) {
            FirebaseCrashlytics.getInstance().recordException(e);
        }
        int filledPercentage =  filledClassFieldCount*100 / profileFields.size();
        return filledPercentage;
    }

    public static boolean isSectionPartiallyFilled(Object instance, List<String> sectionFields) {
        int matchCount = 0;
        try {
            for (String field : sectionFields) {
                Object object = instance.getClass().getDeclaredField(field).get(instance);
                boolean isNull = Objects.isNull(object);
                if(object instanceof Integer){
                    isNull = (Integer)object == 0;
                }else if(object instanceof String){
                    isNull = isBlank(object.toString());
                }
                if (!isNull) {
                    matchCount++;
                }
            }
        }   catch (IllegalAccessException | NoSuchFieldException e) {
            return false;
        }
        if(matchCount > 0 && matchCount < sectionFields.size())
            return true;
        return false;
    }

    public static String sectionCompletionStatus(Object instance, List<String> sectionFields) {
        if (instance!=null) {
            if (isSectionEmpty(instance, sectionFields))
                return EMPTY;
            else if (isSectionFilled(instance, sectionFields))
                return FULLY_FILLED;
            else
                return PARTIALLY_FILLED;
        } else {
            return PARTIALLY_FILLED;
        }
    }

    public static boolean isSectionEmpty(Object instance, List<String> sectionFields) {
        try {
            for (String field : sectionFields) {
                Object object = instance.getClass().getDeclaredField(field).get(instance);
                boolean isNull = Objects.isNull(object);
                if(object instanceof Integer){
                    isNull = (Integer)object == 0;
                }else if(object instanceof String){
                    isNull = isBlank(object.toString());
                }
                if (!isNull) {
                    return false;
                }
            }
        }   catch (IllegalAccessException | NoSuchFieldException e) {
            return false;
        }
        return true;
    }

    public static boolean isSectionFilled(Object instance, List<String> sectionFields) {
        try {
            for (String field : sectionFields) {
                Object object = instance.getClass().getDeclaredField(field).get(instance);
                boolean isNull = Objects.isNull(object);
                if(object instanceof Integer){
                    isNull = (Integer)object == 0;
                }else if(object instanceof String){
                    isNull = isBlank(object.toString());
                }
                if (isNull) {
                    return false;
                }
            }
        }   catch (IllegalAccessException | NoSuchFieldException e) {
            return false;
        }
        return true;
    }

    public static String getCompleteAddress(BookEntity entity) {

        StringBuilder sb = new StringBuilder();
        if(!isBlank(entity.businessAddress)) {
            sb.append(entity.businessAddress).append(", ");
        }
        if(!isBlank(entity.subdistrict)) {
            sb.append(entity.subdistrict).append(", ");
        }
        if(!isBlank(entity.district)) {
            sb.append(entity.district).append(", ");
        }
        if(!isBlank(entity.city)) {
            sb.append(entity.city).append(", ");
        }
        if(!isBlank(entity.province)) {
            sb.append(entity.province).append(", ");
        }
        if(!isBlank(entity.postalCode)) {
            sb.append(entity.postalCode).append(", ");
        }
        return sb.toString();
    }

    public static String getCompleteAddress(Address address) {

        StringBuilder sb = new StringBuilder();
        if(!isBlank(address.getFullAddress())) {
            sb.append(address.getFullAddress()).append(", ");
        }
        if(!isBlank(address.getSubDistrict())) {
            sb.append(address.getSubDistrict()).append(", ");
        }
        if(!isBlank(address.getDistrict())) {
            sb.append(address.getDistrict()).append(", ");
        }
        if(!isBlank(address.getCity())) {
            sb.append(address.getCity()).append(", ");
        }
        if(!isBlank(address.getProvince())) {
            sb.append(address.getProvince()).append(", ");
        }
        if(!isBlank(address.getPostalCode())) {
            sb.append(address.getPostalCode()).append(", ");
        }
        return sb.toString();
    }

    public static String getUserIdFromToken(){
        try {
            String token = SessionManager.getInstance().getBukuwarungToken();
            if (token == null) {
                return  token;
            }
            JWT parsedJWT = new JWT(token);
            Map<String, Claim> claim = parsedJWT.getClaims();
            if (claim.get("user_id") != null) {
                SessionManager.getInstance().setUserId(claim.get("user_id").asString());
                return claim.get("user_id").asString();
            }
            return null;
        } catch (Exception e){
            FirebaseCrashlytics.getInstance().recordException(e);
            return null;
        }
    }

    public static void updateBusinessCategory(String[] oldBusinessTypes, String[] newBusinessTypes) {
        try {
            BusinessRepository businessRepository = BusinessRepository.getInstance(Application.getAppContext());
            List<BookEntity> bookEntities = BusinessRepository.getInstance(Application.getAppContext()).getBusinessListRaw(User.getUserId());
            for (BookEntity book : bookEntities) {
                if (book.bookTypeName.contains(oldBusinessTypes[8])) {
                    businessRepository.updateExistingBookNames(book.bookId, 0, newBusinessTypes[1]);
                } else if (book.bookTypeName.contains(oldBusinessTypes[2])) {
                    businessRepository.updateExistingBookNames(book.bookId, 1, newBusinessTypes[2]);
                } else if (book.bookTypeName.contains(oldBusinessTypes[0])) {
                    businessRepository.updateExistingBookNames(book.bookId, 2, newBusinessTypes[3]);
                } else if (book.bookTypeName.contains(oldBusinessTypes[1]) || book.bookTypeName.contains(oldBusinessTypes[16])) {
                    businessRepository.updateExistingBookNames(book.bookId, 3, newBusinessTypes[0]);
                } else if (book.bookTypeName.contains(oldBusinessTypes[6])) {
                    businessRepository.updateExistingBookNames(book.bookId, 5, newBusinessTypes[9]);
                } else if (book.bookTypeName.contains(oldBusinessTypes[30]) || book.bookTypeName.contains(oldBusinessTypes[4])) {
                    businessRepository.updateExistingBookNames(book.bookId, 6, newBusinessTypes[4]);
                } else if (book.bookTypeName.contains(oldBusinessTypes[27])) {
                    businessRepository.updateExistingBookNames(book.bookId, 8, newBusinessTypes[7]);
                } else if (book.bookTypeName.contains(oldBusinessTypes[31]) || book.bookTypeName.contains(oldBusinessTypes[23])
                        || book.bookTypeName.contains(oldBusinessTypes[24]) || book.bookTypeName.contains(oldBusinessTypes[18])
                        || book.bookTypeName.contains(oldBusinessTypes[19]) || book.bookTypeName.contains(oldBusinessTypes[14])
                        || book.bookTypeName.contains(oldBusinessTypes[15]) || book.bookTypeName.contains(oldBusinessTypes[12])
                        || book.bookTypeName.contains(oldBusinessTypes[10]) || book.bookTypeName.contains(oldBusinessTypes[9])
                        || book.bookTypeName.contains(oldBusinessTypes[3]) || book.bookTypeName.contains(oldBusinessTypes[20])) {
                    businessRepository.updateExistingBookNames(book.bookId, 38, newBusinessTypes[6]);
                }
            }
            FeaturePrefManager.getInstance().setBookCategoriesUpdated(true);
        } catch (Exception e) {
            FirebaseCrashlytics.getInstance().recordException(e);
        }
    }
}
