package com.bukuwarung.data.transaction.di.module

import com.bukuwarung.data.transaction.api.TransactionRepository
import com.bukuwarung.data.transaction.implementation.DefaultTransactionRepository
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent

@Module
@InstallIn(SingletonComponent::class)
interface TransactionDataModule {
    @Binds
    fun bindTransactionRepository(repository: DefaultTransactionRepository): TransactionRepository
}
