package com.bukuwarung.data.transaction.implementation

import com.bukuwarung.base.coroutine.di.qualifier.IoDispatcher
import com.bukuwarung.base.data.api.Response
import com.bukuwarung.base.matrix.api.Matrix
import com.bukuwarung.base.pin.api.Pin
import com.bukuwarung.base.pin.di.qualifier.AppConfigPin
import com.bukuwarung.data.product.api.ProductRepository
import com.bukuwarung.data.restclient.ApiEmptyResponse
import com.bukuwarung.data.restclient.ApiErrorResponse
import com.bukuwarung.data.restclient.ApiSuccessResponse
import com.bukuwarung.data.transaction.api.TransactionRepository
import com.bukuwarung.data.transaction.api.model.CashCategory
import com.bukuwarung.data.transaction.api.model.CashTransaction
import com.bukuwarung.data.transaction.api.model.TransactionCategory
import com.bukuwarung.data.transaction.api.model.TransactionProduct
import com.bukuwarung.data.transaction.implementation.mapper.toCashCategory
import com.bukuwarung.data.transaction.implementation.mapper.toCashTransaction
import com.bukuwarung.data.transaction.implementation.mapper.toCashTransactionBody
import com.bukuwarung.data.transaction.implementation.mapper.toTransactionCategory
import com.bukuwarung.data.transaction.implementation.matrix.api.model.TransactionCategoryMatrix
import com.bukuwarung.data.transaction.implementation.remote.api.TransactionRemote
import com.bukuwarung.database.dao.CashDao
import com.bukuwarung.database.dao.TransactionDao
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.withContext
import javax.inject.Inject

class DefaultTransactionRepository @Inject constructor(
    private val matrix: Matrix,
    private val transactionRemote: TransactionRemote,
    private val cashDao: CashDao,
    private val productRepository: ProductRepository,
    private val transactionDao: TransactionDao,
    @AppConfigPin private val appConfigPin: Pin,
    @IoDispatcher private val ioDispatcher: CoroutineDispatcher,
) : TransactionRepository {
    override suspend fun postCashTransaction(cashTransaction: CashTransaction): Response<Unit> {
        return withContext(context = ioDispatcher) {
            try {
                val entity = transactionDao.getCashTransactionById(cashTransaction.id)
                val body = entity?.toCashTransactionBody() ?: return@withContext Response.Empty
                val response = transactionRemote.syncCashTransaction(body)

                when (response) {
                    is ApiErrorResponse -> {
                        Response.Error(
                            message = response.errorMessage,
                            meta = mapOf(
                                "statusCode" to response.statusCode,
                                "code" to response.code,
                            ),
                        )
                    }
                    is ApiEmptyResponse -> {
                        Response.Empty
                    }
                    is ApiSuccessResponse -> {
                        Response.Success(data = Unit)
                    }
                }
            } catch (e: Exception) {
                Response.Error(message = e.message.orEmpty())
            }
        }
    }

    override suspend fun fetchIncomeCategories(): Response<List<TransactionCategory>> {
        return withContext(context = ioDispatcher) {
            try {
                val config = matrix.fetchConfig(
                    key = "category_transaction_credit_new",
                    defaultValue = ""
                )
                val type = object : TypeToken<List<TransactionCategoryMatrix>>() {}.type
                val categories = Gson().fromJson<List<TransactionCategoryMatrix>>(config, type)

                val patchedCategories = mutableListOf(
                    TransactionCategory(
                        id = "penjualan",
                        name = "Penjualan",
                        image = "https://i.ibb.co/GJ32gQT/penjualan.webp",
                    )
                )
                patchedCategories.addAll(
                    categories.map(TransactionCategoryMatrix::toTransactionCategory)
                )

                Response.Success(data = patchedCategories.distinctBy { it.name })
            } catch (e: Exception) {
                Response.Error(message = e.message.orEmpty())
            }
        }
    }

    override suspend fun fetchExpenseCategories(): Response<List<TransactionCategory>> {
        return withContext(context = ioDispatcher) {
            try {
                val config = matrix.fetchConfig(
                    key = "category_transaction_debit_new",
                    defaultValue = ""
                )
                val type = object : TypeToken<List<TransactionCategoryMatrix>>() {}.type
                val categories = Gson().fromJson<List<TransactionCategoryMatrix>>(config, type)

                Response.Success(data = categories.map(TransactionCategoryMatrix::toTransactionCategory))
            } catch (e: Exception) {
                Response.Error(message = e.message.orEmpty())
            }
        }
    }

    override suspend fun retrieveCashTransactionById(id: String): CashTransaction? {
        return withContext(context = ioDispatcher) {
            try {
                val entity = transactionDao.getCashTransactionById(id)
                entity?.toCashTransaction()
            } catch (e: Exception) {
                null
            }
        }
    }

    override suspend fun retrieveTransactionProducts(cashTransactionId: String): List<TransactionProduct> {
        return withContext(context = ioDispatcher) {
            try {
                val items = transactionDao.getTransactionItems(cashTransactionId)
                val products = items?.mapNotNull { item ->
                    val product = productRepository.retrieveProductById(item.productId)
                        ?: return@mapNotNull null
                    TransactionProduct(product, quantity = (item.quantity ?: 0.0).toInt())
                }.orEmpty()

                products
            } catch (e: Exception) {
                listOf()
            }
        }
    }

    override suspend fun retrieveCashCategory(cashCategoryId: String): CashCategory? {
        return withContext(context = ioDispatcher) {
            try {
                val entity = cashDao.getCashCategoryById(cashCategoryId)
                entity?.toCashCategory()
            } catch (e: Exception) {
                null
            }
        }
    }

    override suspend fun readLastTransactionType(): Int {
        return withContext(context = ioDispatcher) {
            try {
                val flag = appConfigPin.read(key = "transaction_type", defaultValue = false)
                (-1).takeIf { flag } ?: 1
            } catch (e: Exception) {
                1
            }
        }
    }

    override suspend fun saveLastTransactionType(type: Int): Boolean {
        return withContext(context = ioDispatcher) {
            try {
                appConfigPin.save(key = "transaction_type", value = type == -1)
                true
            } catch (e: Exception) {
                false
            }
        }
    }

    override suspend fun fetchShowImageTransaction(): Boolean {
        return withContext(context = ioDispatcher) {
            try {
                matrix.fetchConfig(key = "show_transaksi_image", defaultValue = false)
            } catch (e: Exception) {
                false
            }
        }
    }

    override suspend fun fetchIsDefaultExpenseCategory(): Boolean {
        return withContext(context = ioDispatcher) {
            try {
                matrix.fetchConfig(key = "default_category_expenses", defaultValue = false)
            } catch (e: Exception) {
                false
            }
        }
    }

    override suspend fun fetchIsCategoryMandatory(): Boolean {
        return withContext(context = ioDispatcher) {
            try {
                matrix.fetchConfig(key = "show_mandatory_transaction_category", defaultValue = false)
            } catch (e: Exception) {
                false
            }
        }
    }

    override suspend fun fetchShowNewCategory(): Int {
        return withContext(context = ioDispatcher) {
            try {
                matrix.fetchConfig(key = "show_new_transaction_category", defaultValue = 0L)
                    .toInt()
            } catch (e: Exception) {
                0
            }
        }
    }
}
