package com.bukuwarung.data.transaction.api.model

import java.io.Serializable
import java.util.Date

data class CashTransaction(
    val id: String,
    val cashCategoryId: String?,
    val customerTransactionId: String?,
    val customerId: String?,
    val orderId: String,
    val balance: Double,
    val capitalBalance: Double,
    val date: Date,
    val paymentStatus: PaymentStatus,
    val image: String,
    val note: String,
) : Serializable

enum class PaymentStatus {
    Paid,
    NotPaid,
}
