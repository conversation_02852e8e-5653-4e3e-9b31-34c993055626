package com.bukuwarung.data.transaction.api

import com.bukuwarung.base.data.api.Response
import com.bukuwarung.data.transaction.api.model.CashCategory
import com.bukuwarung.data.transaction.api.model.CashTransaction
import com.bukuwarung.data.transaction.api.model.TransactionCategory
import com.bukuwarung.data.transaction.api.model.TransactionProduct

interface TransactionRepository {
    /** Post cash transaction in internal service. **/
    suspend fun postCashTransaction(cashTransaction: CashTransaction): Response<Unit>

    /** Fetch transaction income category from Firebase service. **/
    suspend fun fetchIncomeCategories(): Response<List<TransactionCategory>>

    /** Fetch transaction expense category from Firebase service. **/
    suspend fun fetchExpenseCategories(): Response<List<TransactionCategory>>

    /** Retrieve cash transaction with [id] from local database. **/
    suspend fun retrieveCashTransactionById(id: String): CashTransaction?

    /** Retrieve transaction products with [cashTransactionId] from local database. **/
    suspend fun retrieveTransactionProducts(cashTransactionId: String): List<TransactionProduct>

    /** Retrieve cash category with [cashCategoryId] from local database. **/
    suspend fun retrieveCashCategory(cashCategoryId: String): CashCategory?

    /** Read last created transaction type from memory-disk. **/
    suspend fun readLastTransactionType(): Int

    /** Save last created transaction type in memory-disk. **/
    suspend fun saveLastTransactionType(type: Int): Boolean

    /** Fetch should show image transaction flag from Firebase service. **/
    suspend fun fetchShowImageTransaction(): Boolean

    /** Fetch is default expense category from Firebase service. **/
    suspend fun fetchIsDefaultExpenseCategory(): Boolean

    /** Fetch is category mandatory from Firebase service. **/
    suspend fun fetchIsCategoryMandatory(): Boolean

    /** Fetch show new category from Firebase service. **/
    suspend fun fetchShowNewCategory(): Int
}
