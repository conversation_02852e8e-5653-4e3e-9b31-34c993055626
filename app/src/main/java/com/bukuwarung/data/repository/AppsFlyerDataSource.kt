package com.bukuwarung.data.repository

import com.bukuwarung.data.restclient.ApiResponse
import com.bukuwarung.data.restclient.ApiSuccessResponse
import com.bukuwarung.model.request.AppsFlyerIdRequest
import retrofit2.http.Body
import retrofit2.http.POST

interface AppsFlyerDataSource {

    @POST("/data-analytics/api/v1/account/appsflyer/save")
    suspend fun sendAppsFlyerId(@Body request: AppsFlyerIdRequest): ApiResponse<Boolean>
}