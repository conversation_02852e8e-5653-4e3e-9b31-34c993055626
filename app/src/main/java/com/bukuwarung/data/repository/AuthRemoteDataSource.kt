package com.bukuwarung.data.repository

import com.bukuwarung.data.restclient.ApiResponse
import com.bukuwarung.model.request.VerifyOtpLoginRequest
import com.bukuwarung.model.request.VerifyOtpRequest
import com.bukuwarung.model.response.OtpResponse
import com.bukuwarung.model.response.VerifyOtpLoginResponse
import retrofit2.http.Body
import retrofit2.http.Header
import retrofit2.http.POST
import retrofit2.http.Url

interface AuthRemoteDataSource {

    @POST("/api/v2/auth/login")
    suspend fun verifyOtp(
        @Header("x-session-id") sessionId: String = "",
        @Body request: VerifyOtpLoginRequest
    ): ApiResponse<VerifyOtpLoginResponse>

    @POST("/api/v2/auth/otp/verify")
    suspend fun verifyGenericOtp(
        @Header("x-ops-token") otpToken: String = "",
        @Body request: VerifyOtpRequest
    ): ApiResponse<OtpResponse>
}
