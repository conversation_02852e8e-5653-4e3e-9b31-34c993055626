package com.bukuwarung.data.repository

import android.util.Log
import androidx.lifecycle.LiveData
import com.bukuwarung.utils.await
import com.bukuwarung.utils.recordException
import com.bukuwarung.wrapper.FirestoreLiveData
import com.google.firebase.auth.AuthResult
import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.firestore.DocumentChange
import com.google.firebase.firestore.FirebaseFirestore

class FirestoreRepository {
    companion object {
        private const val TAG = "FirestoreRepository"
    }

    private var firestoreLiveData: FirestoreLiveData? = null

    suspend fun signInToFirebaseAuth(token: String): AuthResult? {
        return try {
            val data = FirebaseAuth.getInstance().signInWithCustomToken(token).await()
            Log.d(TAG, "signInWithCustomToken:success")
            data
        } catch (e: Exception) {
            Log.w(TAG, "signInWithCustomToken:failure", e)
            null
        }
    }

    suspend fun deleteFirestoreData(collectionPath: String,documentPath: String) {
        try {
            FirebaseFirestore.getInstance().collection(collectionPath)
                    .document(documentPath).delete().await()
        } catch (e: Exception) {
            e.recordException()
        }
    }
    suspend fun setFirestoreData(collectionPath: String,documentPath: String, objectToUpdate: Any) {
        try {
            FirebaseFirestore.getInstance().collection(collectionPath)
                    .document(documentPath).set(objectToUpdate).await()
        } catch (e: Exception) {
            e.recordException()
        }
    }

    suspend fun updateFirestoreData(collectionPath: String,documentPath: String, mapToUpdate: Map<String, Any>) {
        try {
            FirebaseFirestore.getInstance().collection(collectionPath)
                    .document(documentPath).update(mapToUpdate).await()
        } catch (e: Exception) {
            e.recordException()
        }
    }

    fun listenToFirestoreWaAuth(): LiveData<List<DocumentChange>> {
        firestoreLiveData = FirestoreLiveData(FirebaseFirestore.getInstance().collection("app_user_auth_wa"))
        return firestoreLiveData!!
    }

    fun removeFirestoreLiveDataListener() {
        firestoreLiveData?.removeEventListener()
    }

}
