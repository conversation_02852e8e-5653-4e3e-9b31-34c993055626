package com.bukuwarung.data.repository

import android.util.Log
import androidx.lifecycle.LiveData
import com.bukuwarung.data.restclient.ApiResponse
import com.bukuwarung.model.request.VerifyOtpLoginRequest
import com.bukuwarung.model.request.VerifyOtpRequest
import com.bukuwarung.model.response.OtpResponse
import com.bukuwarung.utils.await
import com.bukuwarung.utils.recordException
import com.bukuwarung.wrapper.FirestoreLiveData
import com.google.firebase.auth.AuthResult
import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.auth.GetTokenResult
import com.google.firebase.crashlytics.FirebaseCrashlytics
import com.google.firebase.firestore.DocumentChange
import com.google.firebase.firestore.FirebaseFirestore

class AuthRepository(private val remoteDataSource: AuthRemoteDataSource) {
    companion object {
        private const val TAG = "AuthRepository"
    }

    private var firestoreLiveData: FirestoreLiveData? = null
    suspend fun verifyOtp(sessionId: String, request: VerifyOtpLoginRequest) = remoteDataSource.verifyOtp(sessionId, request)

    suspend fun verifyGenericOtp(otpToken: String, request: VerifyOtpRequest): ApiResponse<OtpResponse> = remoteDataSource.verifyGenericOtp(otpToken, request)

    suspend fun signInToFirebaseAuth(token: String): AuthResult? {
        return try {
            val data = FirebaseAuth.getInstance().signInWithCustomToken(token).await()
            Log.d(TAG, "signInWithCustomToken:success")
            data
        } catch (e: Exception) {
            FirebaseCrashlytics.getInstance().log("signInWithCustomToken:failed")
            e.recordException()
            null
        }
    }

    fun deleteFirestoreWaAuthData(phone: String) {
        try {
            FirebaseFirestore.getInstance().collection("app_user_auth_wa")
                    .document(phone).delete()
        } catch (e: Exception) {
            e.recordException()
        }
    }
    suspend fun updateRefreshToken(autoVerify: Boolean, phone: String): GetTokenResult? {
        if (autoVerify) {
            deleteFirestoreWaAuthData(phone)
        }
        val mUser = FirebaseAuth.getInstance().currentUser
        return try {
            val data = mUser?.getIdToken(true)?.await()
            data
        } catch (e: Exception) {
            //request login after 1hr if user is not created
            //SessionManager.getInstance().logOutInstance();
            e.recordException()
            null
        }
    }

    fun listenToFirestoreWaAuth(): LiveData<List<DocumentChange>> {
        firestoreLiveData = FirestoreLiveData(FirebaseFirestore.getInstance().collection("app_user_auth_wa"))
        return firestoreLiveData!!
    }

    fun removeFirestoreLiveDataListener() {
        firestoreLiveData?.removeEventListener()
    }

}
