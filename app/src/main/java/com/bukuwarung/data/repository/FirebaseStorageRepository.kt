package com.bukuwarung.data.repository

import android.graphics.Bitmap
import com.bukuwarung.utils.await
import com.bukuwarung.utils.recordException
import com.google.firebase.ktx.Firebase
import com.google.firebase.storage.FirebaseStorage
import com.google.firebase.storage.ktx.storage
import kotlinx.coroutines.*
import java.io.ByteArrayOutputStream

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 20/11/20 at 18:57
 * for bukuwarung project
 *
 * github.com/muzafakar
 */

class FirebaseStorageRepository {

    suspend fun uploadProfilePicture(image: Bitmap, userId: String, businessId: String): String? {
        return try {

            val storage = Firebase.storage.reference.child("profile/$userId/$businessId.jpg")

            storage.putBytes(compressAsByteArray(image)).await()
            storage.downloadUrl.await().toString()
        } catch (ex: Exception) {
            ex.recordException()
            null
        }
    }

    suspend fun uploadBusinessLogo(image: Bitmap?, userId: String, businessId: String): String? {
        return try {
            image ?: return null
            val storage = Firebase.storage.reference.child("business_logo/$userId/$businessId.jpg")

            storage.putBytes(compressAsByteArray(image)).await()
            storage.downloadUrl.await().toString()
        } catch (ex: Exception) {
            ex.recordException()
            null
        }
    }

    suspend fun uploadCustomerPicture(image: Bitmap?, userId: String, businessId: String, customerId: String): String? {
        return try {
            image ?: return null
            val storage = Firebase.storage.reference.child("customer_profile/$userId/$businessId/$customerId.jpg")

            storage.putBytes(compressAsByteArray(image)).await()
            storage.downloadUrl.await().toString()
        } catch (ex: Exception) {
            ex.recordException()
            null
        }
    }


    /**
     * only use this method to upload image when coroutine can't be used
     * e.g. calling it from Java file
     * */
    fun uploadCustomerImageWithTask(image: Bitmap?, userId: String, businessId: String, customerId: String, callback: (String?) -> Unit) {
        CoroutineScope(Dispatchers.IO).launch {
            try {
                if (image == null) {
                    callback(null)
                    return@launch
                }

                val storage = FirebaseStorage.getInstance().reference.child("customer_profile/$userId/$businessId/$customerId.jpg")

                storage.putBytes(compressAsByteArray(image)).continueWithTask { task ->
                    if (!task.isSuccessful) {
                        task.exception?.let {
                            it.recordException()
                            callback(null)
                        }
                    }
                    storage.downloadUrl
                }.addOnCompleteListener { task ->
                    if (task.isSuccessful) {
                        callback(task.result.toString())
                    } else {
                        callback(null)
                    }
                }

            } catch (ex: Exception) {
                ex.recordException()
            }
        }
    }

    private suspend fun compressAsByteArray(image: Bitmap): ByteArray {
        return withContext(Dispatchers.IO) {
            val stream = ByteArrayOutputStream()
            image.compress(Bitmap.CompressFormat.JPEG, COMPRESS_RATE, stream)
            stream.toByteArray()
        }
    }

    companion object {
        private const val COMPRESS_RATE = 45
    }
}