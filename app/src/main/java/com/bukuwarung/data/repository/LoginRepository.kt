package com.bukuwarung.data.repository

import com.bukuwarung.BuildConfig
import com.bukuwarung.database.entity.AppConfig
import com.bukuwarung.database.repository.BusinessRepository
import com.bukuwarung.model.request.GuestSessionRequest
import com.bukuwarung.model.request.LoginRequest
import com.bukuwarung.preference.AppConfigManager
import com.bukuwarung.utils.recordException
import com.google.firebase.firestore.DocumentReference
import com.google.firebase.firestore.FirebaseFirestore

class LoginRepository(private val remoteDataSource: LoginRemoteDataSource) {

    suspend fun getOtp(request: LoginRequest, phone: String) =
        remoteDataSource.getOtp(request, phone)

    suspend fun checkPasswordExists(request: LoginRequest) = remoteDataSource.checkPasswordExists(request)

    suspend fun getGuestUserSession(request: GuestSessionRequest) = remoteDataSource.getGuestUserSession(request)

    fun loadAppConfig(): Boolean {
        val docRef: DocumentReference = FirebaseFirestore.getInstance().collection("app_config").document(BuildConfig.APP_CONFIG_VERSION)
        var enableGuestFeature = false
        docRef.get().addOnSuccessListener { documentSnapshot ->
            try {
                BusinessRepository.appConfig = documentSnapshot.toObject(AppConfig::class.java)
                AppConfigManager.getInstance().whatsappAuth = BusinessRepository.appConfig.whatsappAuth
                AppConfigManager.getInstance().enableGuestFeature = BusinessRepository.appConfig.enableGuestFeature
                AppConfigManager.getInstance().defaultTabLogin = BusinessRepository.appConfig.defaultTabLogin
                AppConfigManager.getInstance().defaultTabName = BusinessRepository.appConfig.defaultTabName
                AppConfigManager.getInstance().defaultTabTarget = BusinessRepository.appConfig.defaultTabTarget
                AppConfigManager.getInstance().profileSetupTarget = BusinessRepository.appConfig.profileSetupTarget
                AppConfigManager.getInstance().excelReportApi = BusinessRepository.appConfig.excelReportApi
                enableGuestFeature = AppConfigManager.getInstance().enableGuestFeature == 1
            }catch (e:Exception){
                e.recordException()
            }
        }

        return enableGuestFeature
    }

}
