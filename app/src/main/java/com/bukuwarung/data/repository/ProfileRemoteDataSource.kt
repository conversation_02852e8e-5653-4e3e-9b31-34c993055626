package com.bukuwarung.data.repository

import com.bukuwarung.data.restclient.ApiResponse
import com.bukuwarung.data.restclient.CompletionRequest
import com.bukuwarung.database.entity.UserProfileEntity
import com.bukuwarung.model.request.GuestSessionRequest
import com.bukuwarung.model.request.GuestSessionResponse
import com.bukuwarung.model.request.LoginRequest
import com.bukuwarung.model.response.LoginOtpResponse
import com.bukuwarung.payments.data.model.FinproOrderResponse
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.POST
import retrofit2.http.Path

interface ProfileRemoteDataSource {

    @POST("/api/v2/auth/users/profile")
    suspend fun saveProfile(@Body request: UserProfileEntity): ApiResponse<Object>

    @GET("/api/v2/auth/users/profile")
    suspend fun getProfile(): ApiResponse<UserProfileEntity>

}