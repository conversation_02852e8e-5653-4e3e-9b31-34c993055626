package com.bukuwarung.data.repository

import com.bukuwarung.data.restclient.ApiResponse
import com.bukuwarung.data.restclient.BureauRequest
import javax.inject.Inject

class BureauRepository (private val bureauDataSource: BureauDataSource) {

    suspend fun postBureauData(sessionId: BureauRequest?): ApiResponse<Boolean> = bureauDataSource.postBureauSuccessData(sessionId)

    suspend fun trackAuthEvent(): ApiResponse<Any> = bureauDataSource.trackAuthEvent()
}