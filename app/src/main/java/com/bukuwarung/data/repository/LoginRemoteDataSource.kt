package com.bukuwarung.data.repository

import com.bukuwarung.data.restclient.ApiResponse
import com.bukuwarung.model.request.GuestSessionRequest
import com.bukuwarung.model.request.GuestSessionResponse
import com.bukuwarung.model.request.LoginRequest
import com.bukuwarung.model.response.CheckPasswordExistResponse
import com.bukuwarung.model.response.LoginOtpResponse
import retrofit2.http.Body
import retrofit2.http.POST
import retrofit2.http.Query

interface LoginRemoteDataSource {

    @POST("/api/v2/auth/otp/send")
    suspend fun getOtp(
        @Body request: LoginRequest,
        @Query("phone_number") phone: String
    ): ApiResponse<LoginOtpResponse>

    @POST("/api/v2/verify")
    suspend fun checkPasswordExists(@Body request: LoginRequest): ApiResponse<CheckPasswordExistResponse>

    @POST("/api/v1/auth/guest")
    suspend fun getGuestUserSession(@Body request: GuestSessionRequest): ApiResponse<GuestSessionResponse>

}