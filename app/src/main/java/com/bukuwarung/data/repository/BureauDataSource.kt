package com.bukuwarung.data.repository

import com.bukuwarung.data.restclient.ApiResponse
import com.bukuwarung.data.restclient.BureauRequest
import retrofit2.http.Body
import retrofit2.http.POST

interface BureauDataSource {

    @POST("api/v1/auth/bureau/session/save")
    suspend fun postBureauSuccessData(@Body sessionId: BureauRequest?): ApiResponse<Boolean>

    @POST("api/v1/auth/event/track")
    suspend fun trackAuthEvent(): ApiResponse<Any>
}