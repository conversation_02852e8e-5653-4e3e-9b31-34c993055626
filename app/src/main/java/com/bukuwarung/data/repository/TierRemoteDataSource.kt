package com.bukuwarung.data.repository

import com.bukuwarung.activities.referral.leaderboard.models.LoyaltyResponse
import com.bukuwarung.data.restclient.ApiResponse
import com.bukuwarung.data.restclient.CompletionRequest
import com.bukuwarung.data.restclient.ResponseWrapper
import com.bukuwarung.payments.data.model.ReferralDataResponse
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.POST
import retrofit2.http.Query

interface TierRemoteDataSource {
    // https://apigateway-dev.bukuwarung.com/loyalty/api/v1/account?withAllTierBenefits=true
    @GET("/loyalty/api/v1/account")
    suspend fun getTierInfo(@Query("withAllTierBenefits") withAllTierBenefits: Boolean = true) : ApiResponse<ResponseWrapper<LoyaltyResponse>>

    @POST("/ac/api/v2/mx/event")
    suspend fun sendProfileCompletionEvent(@Body request: CompletionRequest): ApiResponse<Any>

    @GET("/loyalty/api/v1/referral/list/l1")
    suspend fun getReferralList(): ApiResponse<ReferralDataResponse>
}