package com.bukuwarung.data.restclient

import androidx.annotation.NonNull
import com.bukuwarung.constants.AppConst
import com.bukuwarung.session.SessionManager
import okhttp3.Interceptor
import okhttp3.Response
import java.io.IOException

class OtpInterceptor : Interceptor {

    @NonNull
    @Throws(IOException::class)
    override fun intercept(@NonNull chain: Interceptor.Chain): Response {
        val newRequest = chain.request().newBuilder()
                .addHeader(AppConst.CUSTOM_OP_HEADER, SessionManager.getInstance().opToken ?: "")
                .build()
        return chain.proceed(newRequest)
    }
}