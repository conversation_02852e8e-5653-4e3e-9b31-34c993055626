package com.bukuwarung.data.restclient

import android.util.Log
import com.bukuwarung.BuildConfig
import com.google.gson.Gson
import com.google.gson.GsonBuilder
import com.moczul.ok2curl.CurlInterceptor
import com.moczul.ok2curl.logger.Logger
import okhttp3.OkHttpClient
import okhttp3.logging.HttpLoggingInterceptor
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory
import java.util.concurrent.TimeUnit

object RestClient {
    fun retrofit(
        baseUrl: String = provideBaseUrl(),
        isWithToken: Boolean = true,
        retryOnConnectionFailure: Boolean = true
    ): Retrofit {
        val retrofit = Retrofit.Builder()
            .addConverterFactory(GsonConverterFactory.create(provideGson()))
            .addConverterFactory(NullOnEmptyConverterFactory())
            .addCallAdapterFactory(ApiResponseCallAdapterFactory())
            .baseUrl(baseUrl)

        val client = provideOkHttpClient(
            provideLoggingInterceptor(),
            provideHeadersInterceptor(),
            isWithToken,
            retryOnConnectionFailure
        )

        retrofit.client(client)

        return retrofit.build()
    }

    private fun provideGson(): Gson {
        return GsonBuilder()
                .setLenient()
                .create()
    }

    private fun provideSecuredInterceptor(): SecuredInterceptor {
        return SecuredInterceptor()
    }

    private fun provideLoggingInterceptor(): HttpLoggingInterceptor {
        return HttpLoggingInterceptor().apply {
//            level = if (BuildConfig.DEBUG) HttpLoggingInterceptor.Level.HEADERS else HttpLoggingInterceptor.Level.NONE
            level = if (BuildConfig.DEBUG) HttpLoggingInterceptor.Level.BODY else HttpLoggingInterceptor.Level.NONE
        }
    }

    private fun provideHeadersInterceptor(): HeadersInterceptor {
        return HeadersInterceptor()
    }

    private fun provideBaseUrl(): String {
        return BuildConfig.API_BASE_URL
    }

    private fun provideOkHttpClient(
        httpLoggingInterceptor: HttpLoggingInterceptor,
        headersInterceptor: HeadersInterceptor,
        isWithToken: Boolean,
        retryOnConnectionFailure: Boolean
    ): OkHttpClient {
        val builder = provideOkHttpClientBuilder(httpLoggingInterceptor, headersInterceptor, retryOnConnectionFailure)
        if (isWithToken) {
            builder.addInterceptor(provideSecuredInterceptor())
        }
        return builder.build()
    }

    private fun provideOkHttpClientBuilder(
        httpLoggingInterceptor: HttpLoggingInterceptor,
        headersInterceptor: HeadersInterceptor,
        retryOnConnectionFailure: Boolean
    ) = OkHttpClient.Builder()
        .addInterceptor(headersInterceptor)
        .addInterceptor(httpLoggingInterceptor)
        .addInterceptor(SecurityHeadersInterceptor())
        .authenticator(TokenAuthenticator())
        .callTimeout(1, TimeUnit.MINUTES)
        .connectTimeout(1, TimeUnit.MINUTES)
        .readTimeout(1, TimeUnit.MINUTES)
        .retryOnConnectionFailure(retryOnConnectionFailure)
        .addInterceptor(CurlInterceptor(object : Logger {
            override fun log(message: String) {
                Log.v("Ok2Curl", message)
            }
        }))
}