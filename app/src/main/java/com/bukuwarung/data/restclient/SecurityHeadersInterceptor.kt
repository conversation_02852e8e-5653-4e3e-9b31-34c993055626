package com.bukuwarung.data.restclient

import com.bukuwarung.BuildConfig
import okhttp3.Interceptor
import okhttp3.Response
import okio.Buffer


class SecurityHeadersInterceptor : Interceptor {

    private val headerGenerator = RequestIntegrityHeadersGenerator(hmacSecret = BuildConfig.HMAC_SECRET)

    override fun intercept(chain: Interceptor.Chain): Response {
        val originalRequest = chain.request()
        val method = originalRequest.method
        val path = originalRequest.url.encodedPath

        val shouldCheckMethod = method in listOf("POST", "PUT", "PATCH")
        val isTargetPath = path.matches(Regex("^/api/payments/[^/]+/disbursements/[^/]+/?(overview)?$"))

        if (shouldCheckMethod && isTargetPath) {
            val requestBody = originalRequest.body
            if (requestBody != null) {
                val buffer = Buffer()
                requestBody.writeTo(buffer)
                val bodyString = buffer.readUtf8()

                val securityHeaders = headerGenerator.generateSecurityHeaders(bodyString)

                val newRequestBuilder = originalRequest.newBuilder()
                securityHeaders.forEach { (name, value) ->
                    newRequestBuilder.header(name, value)
                }

                return chain.proceed(newRequestBuilder.build())
            }
        }

        return chain.proceed(originalRequest)
    }
}