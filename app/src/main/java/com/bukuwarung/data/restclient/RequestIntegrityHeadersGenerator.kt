package com.bukuwarung.data.restclient

import android.util.Base64
import java.nio.charset.StandardCharsets
import java.security.SecureRandom
import java.time.Instant
import javax.crypto.Mac
import javax.crypto.spec.SecretKeySpec

/**
 * Helper class for generating security headers required by the RequestBodyIntegrityFilter.
 */
class RequestIntegrityHeadersGenerator(
    private val hmacSecret: String,
    private val allowedTimeDiffSeconds: Long = 300
) {
    companion object {
        private const val HMAC_ALGORITHM = "HmacSHA256"
        private const val HEADER_SIGNATURE = "x-request-signature"
        private const val HEADER_TIMESTAMP = "x-request-timestamp"
        private const val HEADER_NONCE = "x-request-nonce"
        private const val NONCE_LENGTH = 16
    }

    /**
     * Generates the required security headers for a request with the given body.
     *
     * @param requestBody The request body as a String
     * @return A map of header names to values to be added to the request
     */
    fun generateSecurityHeaders(requestBody: String): Map<String, String> {
        val timestamp = Instant.now().epochSecond.toString()
        val nonce = generateNonce()
        val signature = calculateSignature(requestBody, timestamp, nonce)
        return mapOf(
            HEADER_SIGNATURE to signature,
            HEADER_TIMESTAMP to timestamp,
            HEADER_NONCE to nonce
        )
    }

    /**
     * Calculates the HMAC-SHA256 signature for the given parameters.
     *
     * @param requestBody The request body
     * @param timestamp The current timestamp in seconds
     * @param nonce A unique nonce for this request
     * @return The Base64-encoded HMAC signature
     */
    private fun calculateSignature(requestBody: String, timestamp: String, nonce: String): String {
        val stringToSign = "$requestBody|$timestamp|$nonce"
        val key = SecretKeySpec(hmacSecret.toByteArray(StandardCharsets.UTF_8), HMAC_ALGORITHM)
        val mac = Mac.getInstance(HMAC_ALGORITHM)
        mac.init(key)
        val hmacBytes = mac.doFinal(stringToSign.toByteArray(StandardCharsets.UTF_8))
        return Base64.encodeToString(hmacBytes, Base64.NO_WRAP)
    }

    /**
     * Generates a cryptographically secure random nonce.
     *
     * @return A Base64-encoded random string
     */
    private fun generateNonce(): String {
        val nonceBytes = ByteArray(NONCE_LENGTH)
        SecureRandom().nextBytes(nonceBytes)
        return Base64.encodeToString(nonceBytes, Base64.NO_WRAP).trim()
    }
}