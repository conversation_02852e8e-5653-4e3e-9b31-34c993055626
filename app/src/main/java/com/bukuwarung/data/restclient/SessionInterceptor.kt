package com.bukuwarung.data.restclient

import androidx.annotation.NonNull
import com.bukuwarung.session.SessionManager
import okhttp3.Interceptor
import okhttp3.Response
import java.io.IOException

class SessionInterceptor: Interceptor {
        @NonNull
        @Throws(IOException::class)
        override fun intercept(@NonNull chain: Interceptor.Chain): Response {
            val newRequest = chain.request().newBuilder()
                .addHeader("x-session-id", SessionManager.getInstance().bureauSessionId)
                .build()
            return chain.proceed(newRequest)
        }

}