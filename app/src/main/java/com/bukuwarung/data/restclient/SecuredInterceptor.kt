package com.bukuwarung.data.restclient

import androidx.annotation.NonNull
import com.bukuwarung.session.SessionManager
import okhttp3.Interceptor
import okhttp3.Response
import java.io.IOException

class SecuredInterceptor : Interceptor {

    @NonNull
    @Throws(IOException::class)
    override fun intercept(@NonNull chain: Interceptor.Chain): Response {
        val newRequest = chain.request().newBuilder()
                .addHeader("Authorization", getAuthToken())
                .build()
        return chain.proceed(newRequest)
    }

    private fun getAuthToken() = "Bearer ${SessionManager.getInstance().bukuwarungToken}"

    companion object {
        private const val KEY_TOKEN = "key_token"
    }
}