package com.bukuwarung.data.restclient

import androidx.annotation.NonNull
import com.bukuwarung.BuildConfig
import com.bukuwarung.session.SessionManager
import okhttp3.Interceptor
import okhttp3.Response
import java.io.IOException
import java.util.*

class HeadersInterceptor(private val additionalHeaders:Map<String,String> = emptyMap()) : Interceptor {

    @NonNull
    @Throws(IOException::class)
    override fun intercept(@NonNull chain: Interceptor.Chain): Response {
        val newRequest = chain.request().newBuilder()
            .addHeader("Content-Type", "application/json")
            .addHeader("Accept", "application/json")
            .addHeader("USER-PSEUDO-INSTANCE-ID", SessionManager.getInstance().appInstanceId)
            .addHeader("X-APP-VERSION-NAME", BuildConfig.VERSION_NAME)
            .addHeader("buku-origin", "bukuwarung-app")
            .addHeader("X-APP-VERSION-CODE", BuildConfig.VERSION_CODE.toString())
            .addHeader("X-TIMEZONE", TimeZone.getDefault().id)

        for ((key, value) in additionalHeaders) {
            newRequest.addHeader(key, value)
        }

        val requestBuilder = newRequest.build()
        return chain.proceed(requestBuilder)
    }
}
