package com.bukuwarung.data.restclient

import android.os.Build
import com.bukuwarung.BukuWarungKeys
import com.bukuwarung.constants.AppConst
import com.bukuwarung.data.session.NewSessionRequest
import com.bukuwarung.data.session.NewSessionResponse
import com.bukuwarung.data.session.SessionRemoteDataSource
import com.bukuwarung.data.session.SessionRepository
import com.bukuwarung.preference.AppConfigManager
import com.bukuwarung.session.AuthHelper
import com.bukuwarung.session.SessionManager
import com.bukuwarung.session.User
import com.bukuwarung.utils.RemoteConfigUtils
import com.bukuwarung.utils.Utilities
import com.bukuwarung.utils.Utilities.bwLog
import com.bukuwarung.utils.recordException
import com.google.firebase.crashlytics.FirebaseCrashlytics
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import okhttp3.Authenticator
import okhttp3.Request
import okhttp3.Response
import okhttp3.Route
import java.util.concurrent.CountDownLatch
import java.util.concurrent.atomic.AtomicBoolean

class TokenAuthenticator : Authenticator {

    private val isRefreshing = AtomicBoolean(false)
    private var lastRefreshAttempt = 0L

    @Synchronized
    override fun authenticate(route: Route?, response: Response): Request? {
        return if (RemoteConfigUtils.isAuthenticationRefactor()) {
            newAuthentication(route, response)
        } else {
            oldAuthentication(route, response)
        }
    }

    private fun oldAuthentication(route: Route?, response: Response): Request? {
        if (responseCount(response) > RemoteConfigUtils.refreshTokenAttempt()) return null

        val sessionManager = SessionManager.getInstance()
        val now = System.currentTimeMillis()

        if (isRefreshing.get() || (now - lastRefreshAttempt < 3000)) {
            logMessage("old-Skipping refresh to prevent spam")
            return null
        }

        synchronized(this) {
            isRefreshing.set(true)
            lastRefreshAttempt = now
        }

        val lock = CountDownLatch(1)

        val sessionRepository = SessionRepository(
            RestClient.retrofit(
                baseUrl = AppConfigManager.getInstance().otpApi
            ).create(SessionRemoteDataSource::class.java)
        )

        if (System.currentTimeMillis() - sessionManager.sessionStart < AuthHelper.SESSION_TIME &&
            !RemoteConfigUtils.getRefreshTokenNeeded()
        ) {
            if (sessionManager.bukuwarungToken == null) return null
            logMessage("old-Returned with updated request")
            return createNewRequest(
                response,
                sessionManager.bukuwarungToken
            )
        }


        var apiResponse: ApiResponse<NewSessionResponse>? = null

        CoroutineScope(Dispatchers.IO).launch {
            val token: String? = sessionManager.sessionToken
            val deviceId: String? = sessionManager.deviceGUID
            if (token.isNullOrEmpty() || deviceId.isNullOrEmpty()) {
                lock.countDown()
                return@launch
            }

            val newSessionRequest = NewSessionRequest(
                token = token,
                register = false,
                deviceId = deviceId,
                userId = User.getUserId(),
                deviceModel = Build.MODEL,
                deviceBrand = Build.MANUFACTURER,
                clientId = BukuWarungKeys.clientId.orEmpty(),
                clientSecret = BukuWarungKeys.clientSecret.orEmpty()
            )

            apiResponse = sessionRepository.createNewSession(newSessionRequest)

            if (apiResponse is ApiErrorResponse) {
                logMessage("old-fail to refresh token")
                sessionManager.setRefreshingTokenFails(true)
            }
            lock.countDown()
        }

        lock.await()
        isRefreshing.set(false)

        return if (apiResponse != null && apiResponse is ApiSuccessResponse) {
            val newSession = (apiResponse as ApiSuccessResponse<NewSessionResponse>).body
            sessionManager.bukuwarungToken = newSession.idToken
            sessionManager.sessionToken = newSession.refreshToken
            sessionManager.setSessionStart()
            sessionManager.setRefreshingTokenFails(false)

            logMessage("old-refresh token successfully: ${newSession.idToken}")
            createNewRequest(response, newSession.idToken)
        } else {
            if (responseCount(response) >= RemoteConfigUtils.refreshTokenAttempt()) {
                logMessage("old-Failed refresh token, logout user")
                try {
                    SessionManager.getInstance().forceLogout()
                } catch (ex: Exception) {
                    FirebaseCrashlytics.getInstance().recordException(ex)
                }
            }
            null
        }
    }

    private fun newAuthentication(route: Route?, response: Response): Request? {
        return try {
            if (responseCount(response) > RemoteConfigUtils.refreshTokenAttempt()) return null
            logMessage("new-Code ${response.code} received for Url: ${response.request.url}")

            val sessionManager = SessionManager.getInstance()
            val now = System.currentTimeMillis()

            if (isRefreshing.get() || (now - lastRefreshAttempt < 3000)) {
                logMessage("new-Skipping refresh to prevent spam")
                return null
            }

            synchronized(this) {
                isRefreshing.set(true)
                lastRefreshAttempt = now
            }

            val sessionRepository = SessionRepository(
                RestClient.retrofit(
                    baseUrl = AppConfigManager.getInstance().otpApi
                ).create(SessionRemoteDataSource::class.java)
            )

            val apiResponse = runBlocking(context = Dispatchers.IO) {
                val token: String? = sessionManager.sessionToken
                val deviceId: String? = sessionManager.deviceGUID
                if (token.isNullOrEmpty() || deviceId.isNullOrEmpty()) {
                    return@runBlocking null
                }

                val newSessionRequest = NewSessionRequest(
                    token = token,
                    register = false,
                    deviceId = deviceId,
                    userId = User.getUserId(),
                    deviceModel = Build.MODEL,
                    deviceBrand = Build.MANUFACTURER,
                    clientId = BukuWarungKeys.clientId.orEmpty(),
                    clientSecret = BukuWarungKeys.clientSecret.orEmpty()
                )
                sessionRepository.createNewSession(newSessionRequest)
            }

            isRefreshing.set(false)

            if (apiResponse != null && apiResponse is ApiSuccessResponse) {
                val newSession = apiResponse.body
                sessionManager.bukuwarungToken = newSession.idToken
                sessionManager.sessionToken = newSession.refreshToken
                sessionManager.setSessionStart()
                sessionManager.setRefreshingTokenFails(false)

                logMessage("new-refresh token successfully: ${newSession.idToken}")

                createNewRequest(response, newSession.idToken)
            } else if(apiResponse != null && apiResponse is ApiErrorResponse) {
                if (apiResponse.statusCode == AppConst.FORBIDDEN_STATUS_CODE || apiResponse.statusCode == AppConst.BAD_REQUEST_STATUS_CODE) {
                    Utilities.clearDataAndLogout()
                } else {
                    if (responseCount(response) >= RemoteConfigUtils.refreshTokenAttempt()) {
                        logMessage("new-Failed refresh token, logout user")
                        try {
                            SessionManager.getInstance().forceLogout()
                        } catch (ex: Exception) {
                            FirebaseCrashlytics.getInstance().recordException(ex)
                        }
                    }
                }
                null
            } else {
                if (responseCount(response) >= RemoteConfigUtils.refreshTokenAttempt()) {
                    logMessage("new-Failed refresh token, logout user")
                    try {
                        SessionManager.getInstance().forceLogout()
                    } catch (ex: Exception) {
                        FirebaseCrashlytics.getInstance().recordException(ex)
                    }
                }
                null
            }
        } catch (e: Exception) {
            e.recordException()
            null
        }
    }

    private fun responseCount(response: Response): Int {
        var result = 0
        var priorResponse = response.priorResponse

        while (priorResponse != null) {
            result++
            priorResponse = priorResponse.priorResponse

            if (result > RemoteConfigUtils.refreshTokenAttempt()) {
                result = Int.MAX_VALUE
                break
            }
        }

        return result
    }

    private fun createNewRequest(response: Response, token: String) =
        response.request.newBuilder()
            .header("Authorization", "Bearer $token")
            .header("x-session-id", SessionManager.getInstance().bureauSessionId)
            .build()

    private fun logMessage(message: String) {
        bwLog("OkHttpClient: ", "Retrofit Authenticator ->$message")
    }
}
