package com.bukuwarung.data.app.implementation

import android.content.Context
import android.content.Context.TELEPHONY_SERVICE
import android.media.MediaDrm
import android.os.Build
import android.provider.Settings
import android.telephony.TelephonyManager
import com.bukuwarung.BuildConfig
import com.bukuwarung.BukuWarungKeys
import com.bukuwarung.base.coroutine.di.qualifier.IoDispatcher
import com.bukuwarung.base.matrix.api.Matrix
import com.bukuwarung.base.pin.api.Pin
import com.bukuwarung.base.pin.di.qualifier.AppSessionSyncPin
import com.bukuwarung.base.pin.di.qualifier.AppSetupPin
import com.bukuwarung.data.app.api.AppRepository
import com.bukuwarung.utils.Utility
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.withContext
import java.lang.System.currentTimeMillis
import java.nio.charset.StandardCharsets
import java.util.UUID
import java.util.UUID.randomUUID
import javax.inject.Inject

class DefaultAppRepository @Inject constructor(
    @ApplicationContext private val context: Context,
    private val matrix: Matrix,
    @AppSessionSyncPin private val appSessionSyncPin: Pin,
    @AppSetupPin private val appSetupPin: Pin,
    @IoDispatcher private val ioDispatcher: CoroutineDispatcher,
) : AppRepository {
    override suspend fun readAppToken(): String {
        return withContext(context = ioDispatcher) {
            try {
                appSessionSyncPin.read(key = "BUKUWARUNG_TOKEN", defaultValue = "")
            } catch (e: Exception) {
                ""
            }
        }
    }

    override suspend fun saveAppToken(token: String): Boolean {
        return withContext(context = ioDispatcher) {
            try {
                appSessionSyncPin.save(key = "BUKUWARUNG_TOKEN", token)
                true
            } catch (e: Exception) {
                false
            }
        }
    }

    override suspend fun saveInitialSync(isInitial: Boolean): Boolean {
        return withContext(context = ioDispatcher) {
            try {
                appSetupPin.save(key = "isFirstSyncDone", isInitial)
                true
            } catch (e: Exception) {
                false
            }
        }
    }

    override suspend fun readImeiNumber(): String {
        return withContext(context = ioDispatcher) {
            try {
                val manager = context.getSystemService(TELEPHONY_SERVICE) as? TelephonyManager
                manager?.deviceId ?: readAndroidId()
            } catch (e: Exception) {
                ""
            }
        }
    }

    override suspend fun readWideVineId(): String {
        return withContext(context = ioDispatcher) {
            try {
                val deviceId = appSessionSyncPin.read(key = "DEVICE_ID", defaultValue = "")
                val uuid = UUID.fromString(deviceId)

                val mediaDrm = MediaDrm(uuid)
                val bytes = mediaDrm.getPropertyByteArray("deviceUniqueId")

                String(bytes, StandardCharsets.UTF_8)
            } catch (e: Exception) {
                UUID(currentTimeMillis(), randomUUID().leastSignificantBits).toString()
            }
        }
    }

    override suspend fun readAdvertisingId(): String {
        return withContext(context = ioDispatcher) {
            try {
                appSessionSyncPin.read(key = "ADVERTISING_ID", defaultValue = "")
            } catch (e: Exception) {
                ""
            }
        }
    }

    override suspend fun saveAdvertisingId(advertisingId: String): Boolean {
        return withContext(context = ioDispatcher) {
            try {
                appSessionSyncPin.save(key = "ADVERTISING_ID", advertisingId)
                true
            } catch (e: Exception) {
                false
            }
        }
    }

    override suspend fun readDeviceId(): String {
        return withContext(context = ioDispatcher) {
            try {
                appSessionSyncPin.read(key = "DEVICE_GUID", defaultValue = "")
            } catch (e: Exception) {
                ""
            }
        }
    }

    override suspend fun readDeviceModel(): String {
        return withContext(context = ioDispatcher) {
            try {
                "${Build.MANUFACTURER} ${Build.MODEL}"
            } catch (e: Exception) {
                ""
            }
        }
    }

    override suspend fun readDevicePlatform(): String {
        return BuildConfig.ORIGIN
    }

    override suspend fun readAndroidId(): String {
        return withContext(context = ioDispatcher) {
            try {
                Settings.Secure.getString(context.contentResolver, Settings.Secure.ANDROID_ID)
            } catch (e: Exception) {
                ""
            }
        }
    }

    override suspend fun isDeviceRooted(): Boolean {
        return withContext(context = ioDispatcher) {
            try {
                Utility.isRooted(context)
            } catch (e: Exception) {
                false
            }
        }
    }

    override suspend fun readClientId(): String {
        return BukuWarungKeys.clientId.orEmpty()
    }

    override suspend fun readClientSecret(): String {
        return BukuWarungKeys.clientSecret.orEmpty()
    }

    override suspend fun fetchTooLargeEnabled(): Boolean {
        return withContext(context = ioDispatcher) {
            try {
                matrix.fetchConfig(key = "toolarge_enabled", defaultValue = false)
            } catch (e: Exception) {
                false
            }
        }
    }

    override suspend fun fetchTooLargeThresholdMetric(): Double {
        return withContext(context = ioDispatcher) {
            try {
                matrix.fetchConfig(key = "toolarge_threshold", defaultValue = 0.0)
            } catch (e: Exception) {
                0.0
            }
        }
    }
}
