package com.bukuwarung.data.password.implementation

import com.bukuwarung.BuildConfig
import com.bukuwarung.base.coroutine.di.qualifier.IoDispatcher
import com.bukuwarung.base.data.api.Response
import com.bukuwarung.data.password.api.LoginRepository
import com.bukuwarung.data.password.api.model.*
import com.bukuwarung.data.password.implementation.remote.api.LoginRemote
import com.bukuwarung.data.password.implementation.remote.api.request.ValidatePasswordBody
import com.bukuwarung.data.restclient.ApiEmptyResponse
import com.bukuwarung.data.restclient.ApiErrorResponse
import com.bukuwarung.data.restclient.ApiSuccessResponse
import com.bukuwarung.enums.AuthAction
import com.bukuwarung.model.request.LoginRequest
import com.bukuwarung.session.SessionManager
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.withContext
import javax.inject.Inject

class DefaultLoginRepository @Inject constructor(
    private val loginRemote: LoginRemote,
    @IoDispatcher private val ioDispatcher: CoroutineDispatcher,
) : LoginRepository {

    override suspend fun validatePassword(request : ValidatePasswordRequest): Response<ValidatePasswordResponse> {
        return withContext(context = ioDispatcher) {
            try {
//                val type = object : TypeToken<ValidatePasswordResponse>() {}.type
                val response = loginRemote.validatePassword(request)
                when (response) {
                    is ApiErrorResponse -> {
                        return@withContext Response.Error(
                            message = response.errorMessage,
                            meta = mapOf(
                                "statusCode" to response.statusCode,
                                "code" to response.code,
                            ),
                        )
                    }
                    is ApiEmptyResponse -> {
                        return@withContext Response.Empty
                    }
                    is ApiSuccessResponse -> {
                        return@withContext Response.Success(response.body)
                    }
                }
            } catch (e: Exception) {
                return@withContext Response.Error(message = e.message.orEmpty())
            }
        }
    }


    override suspend fun createPassword(request : CreatePasswordRequest): Response<CreatePasswordResponse> {
        return withContext(context = ioDispatcher) {
            try {
//                val type = object : TypeToken<CreatePasswordResponse>() {}.type
                val response = loginRemote.createPassword(request)
                when (response) {
                    is ApiErrorResponse -> {
                        return@withContext Response.Error(
                            message = response.errorMessage,
                            meta = mapOf(
                                "statusCode" to response.statusCode,
                                "code" to response.code,
                            ),
                        )
                    }
                    is ApiEmptyResponse -> {
                        return@withContext Response.Empty
                    }
                    is ApiSuccessResponse -> {
                        return@withContext Response.Success(response.body)
                    }
                }
            } catch (e: Exception) {
                return@withContext Response.Error(message = e.message.orEmpty())
            }
        }
    }

    override suspend fun updatePassword(request : UpdatePasswordRequest): Response<UpdatePasswordResponse> {
        return withContext(context = ioDispatcher) {
            try {
//                val type = object : TypeToken<CreatePasswordResponse>() {}.type
                val response = loginRemote.updatePassword(request)
                when (response) {
                    is ApiErrorResponse -> {
                        return@withContext Response.Error(
                            message = response.errorMessage,
                            meta = mapOf(
                                "statusCode" to response.statusCode,
                                "code" to response.code,
                            ),
                        )
                    }
                    is ApiEmptyResponse -> {
                        return@withContext Response.Empty
                    }
                    is ApiSuccessResponse -> {
                        return@withContext Response.Success(response.body)
                    }
                }
            } catch (e: Exception) {
                return@withContext Response.Error(message = e.message.orEmpty())
            }
        }
    }


    override suspend fun changePassword(request : ChangePasswordRequest): Response<ChangePasswordResponse> {
        return withContext(context = ioDispatcher) {
            try {
//                val type = object : TypeToken<CreatePasswordResponse>() {}.type
                val response = loginRemote.changePassword(request)
                when (response) {
                    is ApiErrorResponse -> {
                        return@withContext Response.Error(
                            message = response.errorMessage,
                            meta = mapOf(
                                "statusCode" to response.statusCode,
                                "code" to response.code,
                            ),
                        )
                    }
                    is ApiEmptyResponse -> {
                        return@withContext Response.Empty
                    }
                    is ApiSuccessResponse -> {
                        return@withContext Response.Success(response.body)
                    }
                }
            } catch (e: Exception) {
                return@withContext Response.Error(message = e.message.orEmpty())
            }
        }
    }

}
