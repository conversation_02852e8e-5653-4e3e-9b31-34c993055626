package com.bukuwarung.data.password.api.model

import com.google.gson.annotations.SerializedName
import java.io.Serializable

data class ValidatePasswordRequest(
    @SerializedName("countryCode")
    var countryCode: String? = null,
    @SerializedName("userId")
    var userId: String? = null,
    @SerializedName("phone")
    var phone: String? = null,
    @SerializedName("password")
    var password: String? = null,
    @SerializedName("deviceId")
    var deviceId: String? = null,
    @SerializedName("client")
    val clientId: String,
    @SerializedName("clientSecret")
    val clientSecret: String,
    @SerializedName("platform")
    val platform: String?=null,
    @SerializedName("origin")
    val origin: String,
    @SerializedName("captchaResponse")
    val captchaResponse: String,
    @SerializedName("captchaType")
    val captchaType: String,
    @SerializedName("androidId")
    val androidId: String?,
    @SerializedName("imeiNumber")
    val imeiNumber: String?,
    @SerializedName("wideVineId")
    val wideVineId: String?,
    @SerializedName("advertisingId")
    val advertisingId: String?
) : Serializable

