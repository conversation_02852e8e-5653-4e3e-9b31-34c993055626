package com.bukuwarung.data.password.api.model

import com.google.gson.annotations.SerializedName
import java.io.Serializable

data class CreatePasswordRequest(
    @SerializedName("phoneNumber")
    var phone: String? = null,
    @SerializedName("password")
    var password: String? = null,
    @SerializedName("client")
    val clientId: String,
    @SerializedName("clientSecret")
    val clientSecret: String
) : Serializable

