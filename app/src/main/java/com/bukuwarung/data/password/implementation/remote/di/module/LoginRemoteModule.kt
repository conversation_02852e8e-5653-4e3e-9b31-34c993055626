package com.bukuwarung.data.password.implementation.remote.di.module

import com.bukuwarung.data.password.implementation.remote.api.LoginRemote
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import retrofit2.Retrofit
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
object LoginRemoteModule {
    @Provides
    @Singleton
    fun provideTransactionRemote(retrofit: Retrofit): LoginRemote {
        return retrofit.create(LoginRemote::class.java)
    }
}