package com.bukuwarung.data.password.implementation.remote.api

import com.bukuwarung.data.password.api.model.*
import com.bukuwarung.data.password.implementation.remote.api.request.ValidatePasswordBody
import com.bukuwarung.data.restclient.ApiResponse
import retrofit2.http.Body
import retrofit2.http.POST

interface LoginRemote {
    @POST("/api/v2/login")
    suspend fun validatePassword(@Body body: ValidatePasswordRequest): ApiResponse<ValidatePasswordResponse>

    @POST("/api/v2/register")
    suspend fun createPassword(@Body body: CreatePasswordRequest): ApiResponse<CreatePasswordResponse>

    @POST("/api/v2/forgot")
    suspend fun updatePassword(@Body body: UpdatePasswordRequest): ApiResponse<UpdatePasswordResponse>

    @POST("/api/v2/reset")
    suspend fun changePassword(@Body body: ChangePasswordRequest): ApiResponse<ChangePasswordResponse>
}