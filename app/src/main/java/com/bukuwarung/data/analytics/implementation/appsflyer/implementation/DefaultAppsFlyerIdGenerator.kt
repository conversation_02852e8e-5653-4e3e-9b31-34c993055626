package com.bukuwarung.data.analytics.implementation.appsflyer.implementation

import android.content.Context
import com.appsflyer.AppsFlyerLib
import com.bukuwarung.data.analytics.implementation.appsflyer.api.AppsFlyerIdGenerator
import dagger.hilt.android.qualifiers.ApplicationContext
import javax.inject.Inject

class DefaultAppsFlyerIdGenerator @Inject constructor(
    @ApplicationContext private val context: Context,
) : AppsFlyerIdGenerator {
    override fun generateAppsFlyerId(): String {
        return AppsFlyerLib.getInstance().getAppsFlyerUID(context).orEmpty()
    }
}
