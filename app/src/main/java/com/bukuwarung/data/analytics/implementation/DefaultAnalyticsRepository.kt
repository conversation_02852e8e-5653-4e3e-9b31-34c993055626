package com.bukuwarung.data.analytics.implementation

import com.bukuwarung.base.coroutine.di.qualifier.IoDispatcher
import com.bukuwarung.base.data.api.Response
import com.bukuwarung.data.analytics.api.AnalyticsRepository
import com.bukuwarung.data.analytics.implementation.appsflyer.api.AppsFlyerIdGenerator
import com.bukuwarung.data.analytics.implementation.remote.api.AnalyticsRemote
import com.bukuwarung.data.analytics.implementation.remote.api.request.AppsFlyerBody
import com.bukuwarung.data.onboarding.api.OnBoardingRepository
import com.bukuwarung.data.restclient.ApiEmptyResponse
import com.bukuwarung.data.restclient.ApiErrorResponse
import com.bukuwarung.data.restclient.ApiSuccessResponse
import com.bukuwarung.data.user.api.UserRepository
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.withContext
import javax.inject.Inject

class DefaultAnalyticsRepository @Inject constructor(
    private val analyticsRemote: AnalyticsRemote,
    private val appsFlyerIdGenerator: AppsFlyerIdGenerator,
    private val onBoardingRepository: OnBoardingRepository,
    private val userRepository: UserRepository,
    @IoDispatcher private val ioDispatcher: CoroutineDispatcher,
) : AnalyticsRepository {
    override suspend fun postAppsFlyerId(userId: String, advertisingId: String): Response<Unit> {
        return withContext(context = ioDispatcher) {
            try {
                val qualifiedId = "SEND_APPS_FLYER_ID$userId$advertisingId"
                if (!shouldPostAppsFlyerId(qualifiedId)) {
                    Response.Empty
                } else {
                    onBoardingRepository.saveOnBoardingCompleted(qualifiedId)

                    val appsFlyerId = appsFlyerIdGenerator.generateAppsFlyerId()
                    val body = AppsFlyerBody(userId, appsFlyerId, advertisingId)
                    val response = analyticsRemote.postAppsFlyerId(body)

                    when (response) {
                        is ApiErrorResponse -> {
                             Response.Error(
                                message = response.errorMessage,
                                meta = mapOf(
                                    "statusCode" to response.statusCode,
                                    "code" to response.code,
                                ),
                            )
                        }
                        is ApiEmptyResponse -> {
                            Response.Empty
                        }
                        is ApiSuccessResponse -> {
                            Response.Success(data = Unit)
                        }
                    }
                }
            } catch (e: Exception) {
                Response.Error(message = e.message.orEmpty())
            }
        }
    }

    private suspend fun shouldPostAppsFlyerId(qualifiedId: String): Boolean {
        val isGuestUser = userRepository.readIsGuestUser()
        val isOnBoardingCompleted = onBoardingRepository.readIsOnBoardingCompleted(qualifiedId)

        return !isGuestUser && !isOnBoardingCompleted
    }
}
