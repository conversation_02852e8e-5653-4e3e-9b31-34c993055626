package com.bukuwarung.data.analytics.implementation.remote.di.module

import com.bukuwarung.data.analytics.implementation.remote.api.AnalyticsRemote
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import retrofit2.Retrofit
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
object AnalyticsRemoteModule {
    @Provides
    @Singleton
    fun provideAnalyticsRemote(retrofit: Retrofit): AnalyticsRemote {
        return retrofit.create(AnalyticsRemote::class.java)
    }
}
