package com.bukuwarung.data.analytics.di.module

import com.bukuwarung.data.analytics.api.AnalyticsRepository
import com.bukuwarung.data.analytics.implementation.DefaultAnalyticsRepository
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent

@Module
@InstallIn(SingletonComponent::class)
interface AnalyticsDataModule {
    @Binds
    fun bindAnalyticsRepository(repository: DefaultAnalyticsRepository): AnalyticsRepository
}
