package com.bukuwarung.data.analytics.implementation.appsflyer.di.module

import com.bukuwarung.data.analytics.implementation.appsflyer.api.AppsFlyerIdGenerator
import com.bukuwarung.data.analytics.implementation.appsflyer.implementation.DefaultAppsFlyerIdGenerator
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent

@Module
@InstallIn(SingletonComponent::class)
interface AppsFlyerModule {
    @Binds
    fun bindAppsFlyerIdGenerator(generator: DefaultAppsFlyerIdGenerator): AppsFlyerIdGenerator
}
