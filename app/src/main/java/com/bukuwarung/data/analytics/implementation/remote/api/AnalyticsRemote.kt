package com.bukuwarung.data.analytics.implementation.remote.api

import com.bukuwarung.data.analytics.implementation.remote.api.request.AppsFlyerBody
import com.bukuwarung.data.restclient.ApiResponse
import retrofit2.http.Body
import retrofit2.http.POST

interface AnalyticsRemote {
    @POST("/data-analytics/api/v1/account/appsflyer/save")
    suspend fun postAppsFlyerId(@Body body: AppsFlyerBody): ApiResponse<Unit>
}
