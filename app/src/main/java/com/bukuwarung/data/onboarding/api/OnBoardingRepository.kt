package com.bukuwarung.data.onboarding.api

import com.bukuwarung.base.data.api.Response
import com.bukuwarung.data.onboarding.api.model.OnBoardingCategory
import com.bukuwarung.data.onboarding.api.model.OnBoardingForm
import com.bukuwarung.data.onboarding.api.model.OnBoardingRedirectionPolicy

interface OnBoardingRepository {
    /** Fetch on-boarding form from Firebase service. **/
    suspend fun fetchOnBoardingForms(): Response<List<OnBoardingForm>>

    /** Fetch on-boarding business category from Firebase service. **/
    suspend fun fetchOnBoardingBusinessCategories(): Response<List<OnBoardingCategory>>

    /** Fetch on-boarding business goal from Firebase service. **/
    suspend fun fetchOnBoardingBusinessGoals(): Response<List<OnBoardingCategory>>

    /** Fetch on-boarding business past usage from Firebase service. **/
    suspend fun fetchOnBoardingBusinessPastUsages(): Response<List<OnBoardingCategory>>

    /** Fetch on-boarding redirection policy from Firebase service. **/
    suspend fun fetchOnBoardingRedirectionPolicy(): Response<OnBoardingRedirectionPolicy>

    /** Fetch "streak" feature flag from Firebase service. **/
    suspend fun fetchStreakFeatureFlag(): Boolean

    /** Save [showTutorial] flag to show on-boarding tutorial in memory-disk. **/
    suspend fun saveOnBoardingTutorial(showTutorial: Boolean): Boolean

    /** Read if on-boarding completes for [userId] from memory-disk. **/
    suspend fun readIsOnBoardingCompleted(userId: String): Boolean

    /** Save [userId] for on-boarding completion in memory-disk. **/
    suspend fun saveOnBoardingCompleted(userId: String): Boolean

    /** Read default tab name for home redirection from memory-disk. **/
    suspend fun readTabRedirection(): String
}
