package com.bukuwarung.data.onboarding.implementation

import com.bukuwarung.base.coroutine.di.qualifier.IoDispatcher
import com.bukuwarung.base.data.api.Response
import com.bukuwarung.base.matrix.api.Matrix
import com.bukuwarung.base.pin.api.Pin
import com.bukuwarung.base.pin.di.qualifier.AppConfigPin
import com.bukuwarung.base.pin.di.qualifier.AppFeaturePin
import com.bukuwarung.base.pin.di.qualifier.AppOnBoardingPin
import com.bukuwarung.data.onboarding.api.OnBoardingRepository
import com.bukuwarung.data.onboarding.api.model.OnBoardingCategory
import com.bukuwarung.data.onboarding.api.model.OnBoardingForm
import com.bukuwarung.data.onboarding.api.model.OnBoardingRedirectionPolicy
import com.bukuwarung.data.user.api.UserRepository
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.withContext
import javax.inject.Inject

class DefaultOnBoardingRepository @Inject constructor(
    private val matrix: Matrix,
    @AppConfigPin private val appConfigPin: Pin,
    @AppFeaturePin private val appFeaturePin: Pin,
    @AppOnBoardingPin private val appOnBoardingPin: Pin,
    private val userRepository: UserRepository,
    @IoDispatcher private val ioDispatcher: CoroutineDispatcher,
) : OnBoardingRepository {
    override suspend fun fetchOnBoardingForms(): Response<List<OnBoardingForm>> {
        return withContext(context = ioDispatcher) {
            try {
                val config = matrix.fetchConfig(key = "user_onboarding_forms", defaultValue = "")
                val forms = config.split(",")
                    .map { id ->
                        when (id.trim()) {
                            OnBoardingForm.BusinessName.id -> OnBoardingForm.BusinessName
                            OnBoardingForm.BusinessCategory.id -> OnBoardingForm.BusinessCategory
                            OnBoardingForm.BusinessGoal.id -> OnBoardingForm.BusinessGoal
                            OnBoardingForm.BusinessPastUsage.id -> OnBoardingForm.BusinessPastUsage
                            else -> OnBoardingForm.Undefined
                        }
                    }.filter { form ->
                        form != OnBoardingForm.Undefined
                    }
                Response.Success(data = forms)
            } catch (e: Exception) {
                Response.Error(message = e.message.orEmpty())
            }
        }
    }

    override suspend fun fetchOnBoardingBusinessCategories(): Response<List<OnBoardingCategory>> {
        return withContext(context = ioDispatcher) {
            try {
                val config = matrix.fetchConfig(key = "on_boarding_categories", defaultValue = "")
                val type = object : TypeToken<List<OnBoardingCategory>>() {}.type

                Response.Success(data = Gson().fromJson(config, type))
            } catch (e: Exception) {
                Response.Error(message = e.message.orEmpty())
            }
        }
    }

    override suspend fun fetchOnBoardingBusinessGoals(): Response<List<OnBoardingCategory>> {
        return withContext(context = ioDispatcher) {
            try {
                val config = matrix.fetchConfig(
                    key = "on_boarding_usage_goal_options",
                    defaultValue = "",
                )
                val type = object : TypeToken<List<OnBoardingCategory>>() {}.type

                Response.Success(data = Gson().fromJson(config, type))
            } catch (e: Exception) {
                Response.Error(message = e.message.orEmpty())
            }
        }
    }

    override suspend fun fetchOnBoardingBusinessPastUsages(): Response<List<OnBoardingCategory>> {
        return withContext(context = ioDispatcher) {
            try {
                val config = matrix.fetchConfig(
                    key = "on_boarding_usage_past_options",
                    defaultValue = "",
                )
                val type = object : TypeToken<List<OnBoardingCategory>>() {}.type

                Response.Success(data = Gson().fromJson(config, type))
            } catch (e: Exception) {
                Response.Error(message = e.message.orEmpty())
            }
        }
    }

    override suspend fun fetchOnBoardingRedirectionPolicy(): Response<OnBoardingRedirectionPolicy> {
        return withContext(context = ioDispatcher) {
            try {
                val redirection = matrix.fetchConfig(
                    key = "on_boarding_redirection",
                    defaultValue = "",
                ).trim()
                if (redirection == OnBoardingRedirectionPolicy.UsageGoal.id) {
                    Response.Success(data = OnBoardingRedirectionPolicy.UsageGoal)
                } else {
                    Response.Success(data = OnBoardingRedirectionPolicy.Default)
                }
            } catch (e: Exception) {
                Response.Success(data = OnBoardingRedirectionPolicy.Default)
            }
        }
    }

    override suspend fun fetchStreakFeatureFlag(): Boolean {
        return withContext(context = ioDispatcher) {
            try {
                matrix.fetchConfig(key = "streaks_feature_enabled", defaultValue = false)
                true
            } catch (e: Exception) {
                false
            }
        }
    }

    override suspend fun saveOnBoardingTutorial(showTutorial: Boolean): Boolean {
        return withContext(context = ioDispatcher) {
            try {
                appFeaturePin.save(key = "disable_onBoarding_tutorial", !showTutorial)
                true
            } catch (e: Exception) {
                false
            }
        }
    }

    override suspend fun readIsOnBoardingCompleted(userId: String): Boolean {
        return withContext(context = ioDispatcher) {
            try {
                appOnBoardingPin.read(key = userId, defaultValue = false)
            } catch (e: Exception) {
                false
            }
        }
    }

    override suspend fun saveOnBoardingCompleted(userId: String): Boolean {
        return withContext(context = ioDispatcher) {
            try {
                appOnBoardingPin.save(key = userId, true)
                true
            } catch (e: Exception) {
                false
            }
        }
    }

    override suspend fun readTabRedirection(): String {
        return withContext(context = ioDispatcher) {
            try {
                val isLogin = userRepository.readIsLogin()
                val isGuestUser = userRepository.readIsGuestUser()
                val userId = userRepository.readUserId()

                val tabPercentage = appConfigPin.read(
                    key = "DEFAULT_TAB_PERCENTAGE",
                    defaultValue = DEFAULT_TAB_PERCENTAGE,
                )
                val defaultTabName = appConfigPin.read(
                    key = "default_tab_name",
                    defaultValue = DEFAULT_TAB_NAME,
                )

                if (!isLogin || isGuestUser || tabPercentage == DEFAULT_TAB_PERCENTAGE) {
                    defaultTabName
                } else {
                    val lastDigit = userId.last().digitToInt()
                    defaultTabName.takeIf { lastDigit < tabPercentage } ?: DEFAULT_TAB_NAME
                }
            } catch (e: Exception) {
                DEFAULT_TAB_NAME
            }
        }
    }

    private companion object {
        const val DEFAULT_TAB_NAME: String = "CUSTOMER"

        const val DEFAULT_TAB_PERCENTAGE: Int = 10
    }
}
