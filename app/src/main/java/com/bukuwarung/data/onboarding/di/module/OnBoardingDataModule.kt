package com.bukuwarung.data.onboarding.di.module

import com.bukuwarung.data.onboarding.api.OnBoardingRepository
import com.bukuwarung.data.onboarding.implementation.DefaultOnBoardingRepository
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent

@Module
@InstallIn(SingletonComponent::class)
interface OnBoardingDataModule {
    @Binds
    fun bindOnBoardingRepository(repository: DefaultOnBoardingRepository): OnBoardingRepository
}
