package com.bukuwarung.payments.ppob.base.view

import android.app.Activity
import android.content.Intent
import android.graphics.Color
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.activity.result.contract.ActivityResultContracts
import androidx.lifecycle.observe
import com.bukuwarung.R
import com.bukuwarung.activities.superclasses.BaseFragment
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.constants.PaymentConst
import com.bukuwarung.databinding.FragmentMultifinanceBinding
import com.bukuwarung.payments.CustomerListActivity
import com.bukuwarung.payments.PaymentDownBottomSheet
import com.bukuwarung.payments.bottomsheet.PpobBillDetailsBottomSheet
import com.bukuwarung.payments.constants.PpobConst
import com.bukuwarung.payments.data.model.Biller
import com.bukuwarung.payments.data.model.FinproAddCartRequest
import com.bukuwarung.payments.data.model.FinproBeneficiary
import com.bukuwarung.payments.data.model.FinproOrderResponse
import com.bukuwarung.payments.data.model.PpobProduct
import com.bukuwarung.payments.data.model.ppob.ProfilesItem
import com.bukuwarung.payments.ppob.base.model.PpobEvent
import com.bukuwarung.payments.ppob.base.viewmodel.MultifinanceViewModel
import com.bukuwarung.ui_component.utils.isNotNullOrEmpty
import com.bukuwarung.utils.InputUtils
import com.bukuwarung.utils.setSingleClickListener
import com.bukuwarung.utils.showView
import com.github.razir.progressbutton.hideProgress
import com.github.razir.progressbutton.showProgress
import javax.inject.Inject
import androidx.fragment.app.viewModels
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class MultifinanceFragment : BaseFragment(), PpobBillersDialog.ICommunicator,
    RecentAndFavouriteFragment.IRecentAndFavCommunicator,
    PpobBillDetailsBottomSheet.PpobBillDetailsBsListener {

    private var _binding: FragmentMultifinanceBinding? = null
    private val binding get() = _binding!!
    private var selectedProduct: PpobProduct? = null
    private var selectedBiller: Biller? = null
    private var addToCartResponse: FinproOrderResponse? = null
    private val category = PpobConst.CATEGORY_MULTIFINANCE
    private var recentBiller = ""
    private var recentAndFavouriteFragment: RecentAndFavouriteFragment? = null

    private val viewModel: MultifinanceViewModel by viewModels()

    private val from by lazy { arguments?.getString(FROM).orEmpty() }
    private val accountNumber by lazy { arguments?.getString(ACCOUNT_NUMBER).orEmpty() }
    private val phoneNumber by lazy { arguments?.getString(PHONE_NUMBER).orEmpty() }
    private val code by lazy { arguments?.getString(CODE).orEmpty() }

    companion object {
        private const val FROM = "FROM"
        private const val ACCOUNT_NUMBER = "ACCOUNT_NUMBER"
        private const val PHONE_NUMBER = "PHONE_NUMBER"
        private const val CODE = "CODE"
        fun createIntent(
            from: String,
            accountNumber: String,
            code: String,
            phoneNumber: String
        ): MultifinanceFragment {
            val bundle = Bundle().apply {
                putString(FROM, from)
                putString(ACCOUNT_NUMBER, accountNumber)
                putString(CODE, code)
                putString(PHONE_NUMBER, phoneNumber)
            }
            return MultifinanceFragment().apply {
                arguments = bundle
            }
        }
    }

    override fun onCreateView(
        layoutInflater: LayoutInflater,
        viewGroup: ViewGroup?,
        bundle: Bundle?
    ): View {
        _binding = FragmentMultifinanceBinding.inflate(layoutInflater, viewGroup, false)
        return binding.root
    }

    override fun setupView(view: View) {
        with(binding) {
            recentAndFavouriteFragment = RecentAndFavouriteFragment.createIntent(category).also {
                childFragmentManager.beginTransaction().add(
                    flRecentAndFav.id,
                    it
                ).commit()
            }
            bivBiller.setDropDownDrawable {
                showMultifinanceProductsDialog()
            }
            bivCustomerNumber.onTextChanged {
                bivCustomerNumber.setClearDrawable()
                viewModel.checkCustomerNumberValidation(bivCustomerNumber.getText(), selectedBiller)
            }
            bivNumber.onTextChanged { bivNumber.setClearDrawable() }
            ivContact.setSingleClickListener {
                resultLauncher.launch(
                    CustomerListActivity.createIntent(
                        requireContext(),
                        PaymentConst.TYPE_PAYMENT_OUT,
                        AnalyticsConst.PPOB_VEHICLE_TAX,
                        true
                    )
                )
            }
            btnCek.setSingleClickListener {
                InputUtils.hideKeyBoardWithCheck(requireActivity())
                addToCart()
            }
            if (accountNumber.isNotNullOrEmpty()) {
                showViewAndSetData(code, accountNumber, phoneNumber)
            }
        }
    }

    private fun showViewAndSetData(biller: String, accountNumber: String, phoneNumber: String) =
        with(binding) {
            bivCustomerNumber.showView()
            bivNumber.showView()
            ivContact.showView()
            btnCek.showView()
            recentBiller = biller
            bivCustomerNumber.setText(accountNumber)
            bivBiller.setText(biller)
            bivNumber.setText(phoneNumber)
            btnCek.callOnClick()
        }

    private fun addToCart() {
        viewModel.addToCart(
            FinproAddCartRequest(
                sku = selectedProduct?.sku.orEmpty(),
                beneficiary = FinproBeneficiary(
                    category = category,
                    accountNumber = binding.bivCustomerNumber.getText(),
                    code = recentBiller,
                    phoneNumber = binding.bivNumber.getText()
                ),
                userType = PpobConst.USERTYPE_TREATMENT
            )
        )
    }

    private fun showMultifinanceProductsDialog() {
        InputUtils.hideKeyboard(requireActivity())
        PpobBillersDialog.getInstance(selectedProduct, category).show(
            childFragmentManager,
            PpobBillersDialog.TAG
        )
    }

    override fun subscribeState() {
        viewModel.observeEvent.observe(viewLifecycleOwner) {
            when (it) {
                is PpobEvent.ToDetail -> {
                    addToCartResponse = it.orderDetail
                    trackFetchBillEvent()
                    binding.bivCustomerNumber.setSuccessState("")
                    showBillDetailsBottomSheet()
                }
                is PpobEvent.ServerError -> {
                    trackFetchBillEvent(it.message.orEmpty())
                    binding.bivCustomerNumber.setSuccessState("")
                    showPaymentDownBottomSheet(true, it.message)
                }
                is PpobEvent.InternetError -> {
                    trackFetchBillEvent(it.message.orEmpty())
                    binding.bivCustomerNumber.setSuccessState("")
                    showPaymentDownBottomSheet(false, it.message)
                }
                is PpobEvent.OtherError -> {
                    trackFetchBillEvent(it.message.orEmpty())
                    binding.bivCustomerNumber.setErrorMessage(it.message.orEmpty())
                }
                else -> {
                    binding.bivCustomerNumber.setSuccessState("")
                }
            }
        }
        viewModel.viewState.observe(this) {
            if (it.numberLengthInvalid) {
                binding.bivCustomerNumber.setErrorMessage(it.errorMessage)
                binding.btnCek.isEnabled = false
            } else if (binding.bivCustomerNumber.getText().isNotBlank()) {
                binding.bivCustomerNumber.setSuccessState("")
                binding.btnCek.isEnabled = true
            }
            if (it.showLoading) {
                binding.btnCek.showProgress {
                    buttonTextRes = null
                    progressColor = Color.BLACK
                }
            } else {
                binding.btnCek.hideProgress(R.string.bt_cek)
            }
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding?.bivCustomerNumber?.removeTextChanged()
        _binding = null
    }

    override fun setSelectedProduct(selectedProduct: PpobProduct, biller: Biller) {
        this.selectedProduct = selectedProduct
        this.selectedBiller = biller
        with(binding) {
            bivCustomerNumber.showView()
            bivNumber.showView()
            ivContact.showView()
            btnCek.showView()
            bivBiller.setText(selectedProduct.name.orEmpty())
            bivCustomerNumber.setFocus()
            InputUtils.showKeyboard(requireActivity())
            if (bivCustomerNumber.getText().isNotBlank() && viewModel.checkCustomerNumberValidation(
                    bivCustomerNumber.getText(),
                    selectedBiller
                )
            ) addToCart()
        }

    }

    private fun showPaymentDownBottomSheet(isServiceDown: Boolean, message: String? = null) {
        val paymentDownBottomSheet = PaymentDownBottomSheet.createInstance(isServiceDown, message)
        paymentDownBottomSheet.show(childFragmentManager, PaymentDownBottomSheet.TAG)
    }

    private fun trackFetchBillEvent(errorMessage: String = "") {
        AppAnalytics.trackEvent(
            AnalyticsConst.EVENT_PPOB_FETCH_BILL,
            AppAnalytics.PropBuilder()
                .put(AnalyticsConst.ID, binding.bivCustomerNumber.getText())
                .put(AnalyticsConst.PROVIDER, selectedProduct?.productInfo?.billerName.orEmpty())
                .put(AnalyticsConst.TYPE, AnalyticsConst.PPOB_MULTIFINANCE)
                .put(
                    AnalyticsConst.STATUS,
                    if (errorMessage.isBlank()) AnalyticsConst.STATUS_SUCCESS else AnalyticsConst.STATUS_FAILED
                )
                .put(AnalyticsConst.REASON, errorMessage)
                .put(AnalyticsConst.PHONE_FILLED, binding.bivNumber.getText())
        )
    }

    private fun showBillDetailsBottomSheet() {
        val ppobBillDetailsBottomSheet =
            PpobBillDetailsBottomSheet.createInstance(addToCartResponse, category)
        ppobBillDetailsBottomSheet.show(childFragmentManager, PpobBillDetailsBottomSheet.TAG)
    }

    override fun setData(profilesItem: ProfilesItem) {
        showViewAndSetData(
            profilesItem.biller?.code.orEmpty(),
            profilesItem.details?.accountNumber.orEmpty(),
            profilesItem.details?.phoneNumber.orEmpty()
        )
    }

    override fun refreshFavouritesTab() {
        recentAndFavouriteFragment?.refreshFavAndRecentTab()
    }

    private val resultLauncher =
        registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result ->
            if (result.resultCode == Activity.RESULT_OK) {
                val data: Intent? = result.data
                var phoneNumber =
                    data?.getStringExtra(CustomerListActivity.PHONE_NUMBER).orEmpty()
                if (phoneNumber.startsWith("8")) phoneNumber = "0$phoneNumber"
                binding.bivNumber.setText(phoneNumber)
                binding.bivNumber.setSelection()
            }
        }
}
