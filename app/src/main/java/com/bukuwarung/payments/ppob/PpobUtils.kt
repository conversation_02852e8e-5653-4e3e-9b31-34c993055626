package com.bukuwarung.payments.ppob

import android.content.Context
import android.content.Intent
import android.net.Uri
import androidx.fragment.app.FragmentManager
import com.bukuwarung.Application
import com.bukuwarung.BuildConfig
import com.bukuwarung.R
import com.bukuwarung.activities.HelpCenterActivity
import com.bukuwarung.activities.WebviewActivity
import com.bukuwarung.activities.homepage.data.BodyBlock
import com.bukuwarung.activities.homepage.view.NoInternetAvailableDialog
import com.bukuwarung.activities.payment.model.PpobErrorPopupContent
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.constants.AppConst
import com.bukuwarung.constants.PaymentConst
import com.bukuwarung.database.entity.BookEntity
import com.bukuwarung.database.entity.CustomerEntity
import com.bukuwarung.database.entity.UserProfileEntity
import com.bukuwarung.database.repository.BusinessRepository
import com.bukuwarung.dialogs.GenericConfirmationDialog
import com.bukuwarung.dialogs.transactions_pop_up_dialog.TransactionsPopUpDialog
import com.bukuwarung.neuro.api.Navigator
import com.bukuwarung.neuro.api.Neuro
import com.bukuwarung.neuro.api.SourceLink
import com.bukuwarung.payments.constants.PpobConst
import com.bukuwarung.payments.data.model.*
import com.bukuwarung.payments.ppob.base.view.PpobActivity
import com.bukuwarung.payments.ppob.base.view.PpobDialog
import com.bukuwarung.payments.ppob.catalog.view.CatalogActivity
import com.bukuwarung.payments.ppob.catalog.view.PromotionActivity
import com.bukuwarung.payments.ppob.reminders.view.ReminderActivity
import com.bukuwarung.payments.ppob.train.view.TrainTicketDetailsActivity
import com.bukuwarung.payments.ppob.train.view.TrainTicketWebviewActivity
import com.bukuwarung.payments.utils.PaymentUtils
import com.bukuwarung.preference.AppConfigManager
import com.bukuwarung.session.SessionManager
import com.bukuwarung.session.User
import com.bukuwarung.utils.*
import com.google.gson.reflect.TypeToken

object PpobUtils {
    fun getHelpButtonIntent(context: Context, category: String): Intent {
        val helpUrl = PpobConst.CATEGORY_HELP_URL[category].orEmpty()
        return if (AppConfigManager.getInstance().useWebView()) {
            val intent = Intent(context, HelpCenterActivity::class.java)
            intent.putExtra(AppConst.URL, helpUrl)
            intent.putExtra(AppConst.TITLE, context.getString(R.string.help))
            intent
        } else {
            Intent(Intent.ACTION_VIEW, Uri.parse(helpUrl))
        }
    }

    fun showPpobUnAvailable(context: Context, category: String) {
        val ppobErrorPopupContent: PpobErrorPopupContent?
        var bodyText: String
        val ppobErrorPopupJson: String = RemoteConfigUtils.getPpobErrorPopupContent()
        val type = object : TypeToken<List<PpobErrorPopupContent>>() {}.type
        val ppobErrorPopupContentList: List<PpobErrorPopupContent>? =
            type.returnObject(ppobErrorPopupJson)
        ppobErrorPopupContent = ppobErrorPopupContentList?.firstOrNull {
            it.ppobCategory?.contains(category).isTrue
        }
        val startTimeInMillis = ppobErrorPopupContent?.startTimeMillis.orNil
        val waitTimeInMillis =
            DateTimeUtils.convertHoursToMillis(ppobErrorPopupContent?.errorWaitTimeHrs.orNil)
        val currentTimeInMillis = DateTimeUtils.getCurrentUTCTime()
        val diffWithCurrentTime = startTimeInMillis + waitTimeInMillis - currentTimeInMillis
        if (diffWithCurrentTime > 0) {
            bodyText = ppobErrorPopupContent?.tvPpobErrorBody?.replace(
                "{category}",
                context.getString(PpobConst.CATEGORY_NAME_MAP[category] ?: R.string.ppob)
            ) ?: context.getString(R.string.ppob_unavailable, category)
            val diffWithCurrentTimeInhours =
                DateTimeUtils.convertMillisToHours(diffWithCurrentTime) + 1
            val timerText = diffWithCurrentTimeInhours.toString()
            bodyText = bodyText.replace("{timer}", timerText)
        } else {
            bodyText = ppobErrorPopupContent?.textAfterTimerComplete
                ?: context.getString(R.string.ppob_unavailable, category)
        }
        val transactionsPopUpDialog = TransactionsPopUpDialog(
            context = context,
            action = {},
            onDismissAction = {},
            image = R.drawable.ic_server_busy_big,
            headText = ppobErrorPopupContent?.tvPpobErrorHeading
                ?: context.getString(R.string.sorry_disturbance),
            bodyText = bodyText,
            buttonText = ppobErrorPopupContent?.tvPpobErrorButton
                ?: context.getString(R.string.back),
            imageUrl = ppobErrorPopupContent?.icPpobError.orEmpty()
        )
        transactionsPopUpDialog.window?.setBackgroundDrawableResource(R.drawable.round_corner_white_picture_picker)
        val minWidth =
            (context.resources.displayMetrics.widthPixels * 0.90).toInt()
        transactionsPopUpDialog.setMinWidth(minWidth)
        transactionsPopUpDialog.show()
    }

    fun showPpobComingSoonDialog(context: Context) {
        PpobDialog(
            context = context,
            action = {},
            onDismissAction = {},
            title = context.getString(R.string.feature_under_development),
            body = context.getString(R.string.feature_under_development_body),
            buttonText = context.getString(R.string.understand),
            image = R.drawable.ic_coming_soon
        ).show()
    }

    fun showRemoveFavouriteDialog(context: Context, action: () -> Unit) {
        context.let {
            GenericConfirmationDialog.create(it) {
                titleRes = R.string.remove_favourite
                bodyRes = R.string.remove_favourite_subtitle
                btnLeftRes = R.string.batal
                btnRightRes = R.string.delete
                rightBtnCallback = { action() }
                leftBtnCallback = {}
            }.show()
        }
    }

    fun showRemoveBankDialog(context: Context, action: () -> Unit) {
        context.let {
            GenericConfirmationDialog.create(it) {
                titleRes = R.string.remove_bank
                bodyRes = R.string.remove_bank_subtitle
                btnLeftRes = R.string.batal
                btnRightRes = R.string.delete
                rightBtnCallback = { action() }
                leftBtnCallback = {
                }
            }.show()
        }
    }

    fun isBnpl(order: FinproOrderResponse) =
        order.payments?.firstOrNull()?.paymentMethod?.code == PaymentHistory.SALDO_BNPL

    fun isSaldo(order: FinproOrderResponse) =
        order.payments?.firstOrNull()?.paymentMethod?.code == PaymentHistory.SALDO

    fun handlePpobCategorySelection(
        context: Context, category: BodyBlock,
        neuro: Neuro, navigator: Navigator
    ) {
        var appRedirectionLink = category.deeplinkAppNeuro.orEmpty()
        if (appRedirectionLink.isBlank()) appRedirectionLink = category.deeplink_app.orEmpty()
        val deeplink = category.deeplink_web.orEmpty()
        val legacyLink = "${AppConst.DEEPLINK_INTERNAL_URL}/ppob/product?category=$category"
        when {
            appRedirectionLink.isNotBlank() -> {
                redirectWithNeuro(
                    neuro,
                    context,
                    SourceLink(context = context, link = appRedirectionLink),
                    navigator,
                    legacyLink
                )
            }
            deeplink.isNotBlank() -> {
                if (Utility.hasInternet()) {
                    context.startActivity(
                        WebviewActivity.createIntent(
                            context, deeplink,
                            category.display_name
                        )
                    )
                } else {
                    context.getFragmentManager()?.let {
                        NoInternetAvailableDialog.show(it)
                    }
                }
            }
        }
    }

    private fun redirectWithNeuro(
        neuro: Neuro, context: Context, sourceLink: SourceLink,
        navigator: Navigator, legacyLink: String
    ) {
        neuro.route(
            sourceLink,
            navigator = navigator,
            onSuccess = {},
            onFailure = {
                redirectWithLegacyLink(
                    neuro,
                    context,
                    navigator,
                    legacyLink
                )
            },
        )
    }

    private fun redirectWithLegacyLink(
        neuro: Neuro, context: Context, navigator: Navigator, legacyLink: String
    ) {
        val sourceLink = SourceLink(context = context, legacyLink)
        neuro.route(
            sourceLink,
            navigator = navigator,
            onSuccess = {},
            onFailure = {},
        )
        return
    }

    fun getPpobCategoryActivityIntent(
        fragmentManager: FragmentManager,
        context: Context,
        category: String?,
        from: String = "",
        accountNumber: String = "",
        phoneNumber: String = "",
        code: String = "",
        layoutType: String = "",
        machineNumber: String = "",
        frameNumber: String = ""
    ): Intent? {
        val state =
            RemoteConfigUtils.getPpobConfigs().ppobList?.firstOrNull { it.category == category }?.state
        when {
            state == State.COMING_SOON -> {
                showPpobComingSoonDialog(context)
                return null
            }
            state == State.NOT_AVAILABLE -> {
                showPpobUnAvailable(
                    context,
                    context.getString(PpobConst.CATEGORY_NAME_MAP[category] ?: R.string.ppob)
                )
                return null
            }
            PaymentUtils.shouldBeBlockedAsPerKycTier(PaymentConst.KYC_PPOB) -> {
                PaymentUtils.showKycKybStatusBottomSheet(fragmentManager, AnalyticsConst.PAYMENTS)
                return null
            }
            else -> {
                when (category) {
                    PpobConst.CATEGORY_REMINDER -> {
                        return ReminderActivity.createIntent(context)
                    }
                    PpobConst.CATEGORY_SET_SELLING_PRICE -> {
                        return CatalogActivity.createIntent(context)
                    }
                    PpobConst.CATEGORY_PROMOTIONS -> {
                        return PromotionActivity.createIntent(context)
                    }
                    PpobConst.CATEGORY_TRAIN_TICKET -> {
                        return TrainTicketWebviewActivity.createIntent(context)
                    }
                    PpobConst.CATEGORY_VOUCHER_GAME -> {
                        return WebviewActivity.createIntent(
                            context,
                            BuildConfig.VOUCHER_GAME_URL + SessionManager.getInstance().businessId,
                            context.getString(R.string.voucher_game1)
                        )
                    }
                }
                return PpobActivity.createIntent(
                    context = context,
                    from = from,
                    category = category ?: PpobConst.CATEGORY_PULSA,
                    accountNumber = accountNumber,
                    phoneNumber = phoneNumber,
                    code = code,
                    layoutType = layoutType,
                    machineNumber = machineNumber,
                    frameNumber = frameNumber
                )
            }
        }
    }

    fun getPpobDetailActivityIntent(
        context: Context,
        category: String = "",
        accountNumber: String = ""
    ): Intent? {
        return when (category) {
            PpobConst.CATEGORY_TRAIN_TICKET -> {
                TrainTicketDetailsActivity.createIntent(context, accountNumber)
            }
            else -> null
        }
    }

    /**
     * Returns invoice items data for the passed PPOB and payment category
     */
    fun getInvoiceItemsForCategory(
        context: Context,
        order: FinproOrderResponse,
        paymentType: String?,
        customerEntity: CustomerEntity?,
        bookEntity: BookEntity?,
        userProfileEntity: UserProfileEntity?
    ): ArrayList<InvoiceItem> {
        val item = order.items?.firstOrNull()
        val customerName = item?.details?.customerName
        val customerPhone = item?.details?.customerNumber ?: customerEntity?.phone
        val invoiceItems = arrayListOf<InvoiceItem>()
        var customer = customerName
        Utilities.safeLet(customerName, customerPhone) { name, phone ->
            customer = "${name.ifEmpty { "-" }}\n$phone"
        }
        val customerLabel: String
        val customerValue: String
        when (paymentType) {
            PaymentHistory.TYPE_PAYMENT_IN -> {
                customerLabel = context.getString(R.string.name_of_recepient)
                customerValue = "${bookEntity?.businessName}\n${Utility.beautifyPhoneNumber(bookEntity?.businessPhone)}"
            }
            PaymentHistory.TYPE_PAYMENT_OUT -> {
                customerLabel = context.getString(R.string.name_of_sender)
                customerValue = "${bookEntity?.businessName}\n${Utility.beautifyPhoneNumber(bookEntity?.businessPhone)}"
            }
            else -> {
                customerLabel = context.getString(R.string.label_customer)
                customerValue = customer.orDash
            }
        }
        // Adding common fields for all invoices
        invoiceItems.add(
            InvoiceItem(
                label = context.getString(R.string.date),
                value = Utility.formatReceiptDateFromHistory(
                    order.getCompletedStatusDate(),
                    true
                )
            )
        )
        invoiceItems.add(
            InvoiceItem(
                label = context.getString(R.string.payment_code),
                value = order.transactionId
            )
        )
        invoiceItems.add(
            InvoiceItem(
                label = customerLabel, value = customerValue
            )
        )
        invoiceItems.add(InvoiceItem(type = InvoiceItemType.DIVIDER))

        when (item?.beneficiary?.category.orEmpty()) {
            PpobConst.CATEGORY_LISTRIK -> {
                if (item?.beneficiary?.code == PpobConst.CATEGORY_PLN_POSTPAID) {
                    // Listrik Postpaid
                    invoiceItems.add(
                        InvoiceItem(
                            label = context.getString(R.string.customer_id_message),
                            value = item.beneficiary.accountNumber.orDash,
                            margins = Margin(top = 8)
                        )
                    )
                    invoiceItems.add(
                        InvoiceItem(
                            label = context.getString(R.string.period),
                            value = item.details.periode.orDash
                        )
                    )
                    invoiceItems.add(
                        InvoiceItem(
                            label = context.getString(R.string.total_billing),
                            value = item.details.totalLembarTagihan.orDash
                        )
                    )
                    invoiceItems.add(
                        InvoiceItem(
                            label = context.getString(R.string.tarif),
                            value = item.details.tarif.orDash
                        )
                    )
                    invoiceItems.add(
                        InvoiceItem(
                            label = context.getString(R.string.total_bill),
                            value = Utility.formatAmount(order.amount)
                        )
                    )
                } else {
                    // Token Listrik
                    invoiceItems.add(
                        InvoiceItem(
                            label = context.getString(R.string.token_listrik),
                            value = item?.name.orDash,
                            margins = Margin(top = 8)
                        )
                    )
                    invoiceItems.add(
                        InvoiceItem(
                            label = context.getString(R.string.customer_id_message),
                            value = item?.beneficiary?.accountNumber.orDash
                        )
                    )
                    invoiceItems.add(
                        InvoiceItem(
                            label = context.getString(R.string.nama_pelanggan),
                            value = item?.details?.customerName.orDash
                        )
                    )
                    invoiceItems.add(
                        InvoiceItem(
                            label = context.getString(R.string.tarif),
                            value = item?.details?.tarif.orDash
                        )
                    )
                    invoiceItems.add(
                        InvoiceItem(
                            label = context.getString(R.string.total_kwh),
                            value = item?.details?.totalKwh.orDash
                        )
                    )
                    invoiceItems.add(
                        InvoiceItem(
                            type = InvoiceItemType.CODE_INFO,
                            label = context.getString(R.string.token_code),
                            value = item?.details?.token.orDash
                        )
                    )
                }
            }
            PpobConst.CATEGORY_PULSA -> {
                invoiceItems.add(
                    InvoiceItem(
                        label = context.getString(R.string.nomor_hp),
                        value = item?.beneficiary?.phoneNumber.orDash,
                        margins = Margin(top = 8)
                    )
                )
                invoiceItems.add(
                    InvoiceItem(
                        label = context.getString(R.string.pulsa),
                        value = item?.name.orDash
                    )
                )
                invoiceItems.add(
                    InvoiceItem(
                        label = context.getString(R.string.serial_number),
                        value = item?.details?.serialNumber.orDash
                    )
                )
            }
            PpobConst.CATEGORY_EWALLET -> {
                invoiceItems.add(
                    InvoiceItem(
                        label = context.getString(R.string.nomor_hp),
                        value = item?.beneficiary?.accountNumber.orDash,
                        margins = Margin(top = 8)
                    )
                )
                invoiceItems.add(
                    InvoiceItem(
                        label = context.getString(R.string.nama_pelanggan),
                        value = item?.details?.customerName.orDash
                    )
                )
                invoiceItems.add(
                    InvoiceItem(
                        label = context.getString(R.string.provider),
                        value = item?.details?.billerName.orDash
                    )
                )
                invoiceItems.add(
                    InvoiceItem(
                        label = context.getString(R.string.nominal_topup),
                        value = item?.name.orDash
                    )
                )
                invoiceItems.add(
                    InvoiceItem(
                        label = context.getString(R.string.serial_number),
                        value = item?.details?.serialNumber.orDash
                    )
                )
            }
            PpobConst.CATEGORY_PULSA_POSTPAID -> {
                invoiceItems.add(
                    InvoiceItem(
                        label = context.getString(R.string.operator),
                        value = item?.details?.productName.orDash,
                        margins = Margin(top = 8)
                    )
                )
                invoiceItems.add(
                    InvoiceItem(
                        label = context.getString(R.string.nomor_hp),
                        value = item?.beneficiary?.phoneNumber.orDash
                    )
                )
                invoiceItems.add(
                    InvoiceItem(
                        label = context.getString(R.string.nama_pelanggan),
                        value = item?.details?.customerName.orDash
                    )
                )
            }
            PpobConst.CATEGORY_PAKET_DATA -> {
                invoiceItems.add(
                    InvoiceItem(
                        label = context.getString(R.string.nomor_hp),
                        value = item?.beneficiary?.phoneNumber.orDash,
                        margins = Margin(top = 8)
                    )
                )
                invoiceItems.add(
                    InvoiceItem(
                        label = context.getString(R.string.packet_data),
                        value = item?.name.orDash
                    )
                )
                invoiceItems.add(
                    InvoiceItem(
                        label = context.getString(R.string.serial_number),
                        value = item?.details?.serialNumber.orDash
                    )
                )
            }
            PpobConst.CATEGORY_PDAM -> {
                invoiceItems.add(
                    InvoiceItem(
                        label = context.getString(R.string.title_pdam),
                        value = order.metadata?.billerName.orDash,
                        margins = Margin(top = 8)
                    )
                )
                invoiceItems.add(
                    InvoiceItem(
                        label = context.getString(R.string.nomor_hp),
                        value = item?.details?.customerNumber.orDash
                    )
                )
                invoiceItems.add(
                    InvoiceItem(
                        label = context.getString(R.string.nama_pelanggan),
                        value = item?.details?.customerName.orDash
                    )
                )
                invoiceItems.add(
                    InvoiceItem(
                        label = context.getString(R.string.period),
                        value = item?.details?.period.orDash
                    )
                )
                invoiceItems.add(
                    InvoiceItem(
                        label = context.getString(R.string.total_bill),
                        value = Utility.formatAmount(order.amount)
                    )
                )
            }
            PpobConst.CATEGORY_BPJS -> {
                invoiceItems.add(
                    InvoiceItem(
                        label = context.getString(R.string.card_number),
                        value = item?.details?.customerNumber.orDash,
                        margins = Margin(top = 8)
                    )
                )
                invoiceItems.add(
                    InvoiceItem(
                        label = context.getString(R.string.nama_pelanggan),
                        value = item?.details?.customerName.orDash
                    )
                )
                invoiceItems.add(
                    InvoiceItem(
                        label = context.getString(R.string.no_of_family),
                        value = item?.details?.memberCount.orDash
                    )
                )
                invoiceItems.add(
                    InvoiceItem(
                        label = context.getString(R.string.period),
                        value = item?.details?.period.orDash
                    )
                )
            }
            PpobConst.CATEGORY_VEHICLE_TAX -> {
                invoiceItems.add(
                    InvoiceItem(
                        label = context.getString(R.string.nama_pelanggan),
                        value = item?.details?.customerName.orDash,
                        margins = Margin(top = 8)
                    )
                )
                invoiceItems.add(
                    InvoiceItem(
                        label = context.getString(R.string.nomor_hp),
                        value = item?.details?.phoneNumber.orDash
                    )
                )
                invoiceItems.add(
                    InvoiceItem(
                        label = context.getString(R.string.policy_number),
                        value = item?.details?.policyNumber.orDash
                    )
                )
                invoiceItems.add(
                    InvoiceItem(
                        label = context.getString(R.string.vehicle_brand),
                        value = item?.details?.vehicleBrand.orDash
                    )
                )
                invoiceItems.add(
                    InvoiceItem(
                        label = context.getString(R.string.period),
                        value = item?.details?.period.orDash
                    )
                )
                invoiceItems.add(
                    InvoiceItem(
                        label = context.getString(R.string.transportation_type),
                        value = item?.details?.vehicleName.orDash
                    )
                )
                invoiceItems.add(
                    InvoiceItem(
                        label = context.getString(R.string.vehicle_type),
                        value = item?.details?.vehicleType.orDash
                    )
                )
                invoiceItems.add(
                    InvoiceItem(
                        label = context.getString(R.string.vehicle_colour),
                        value = item?.details?.vehicleColor.orDash
                    )
                )
                invoiceItems.add(
                    InvoiceItem(
                        label = context.getString(R.string.vehicle_build_year),
                        value = item?.details?.buildYear.orDash
                    )
                )
                invoiceItems.add(
                    InvoiceItem(
                        label = context.getString(R.string.machine_number),
                        value = item?.details?.machineNumber.orDash
                    )
                )
                invoiceItems.add(
                    InvoiceItem(
                        label = context.getString(R.string.frame_number),
                        value = item?.details?.frameNumber.orDash
                    )
                )
                invoiceItems.add(
                    InvoiceItem(
                        label = context.getString(R.string.pkb),
                        value = item?.details?.pkb.orDash
                    )
                )
                invoiceItems.add(
                    InvoiceItem(
                        label = context.getString(R.string.total_bill),
                        value = Utility.formatAmount(item?.amount.orNil)
                    )
                )
            }
            PpobConst.CATEGORY_MULTIFINANCE -> {
                invoiceItems.add(
                    InvoiceItem(
                        label = context.getString(R.string.contact_number),
                        value = item?.details?.customerNumber.orDash,
                        margins = Margin(top = 8)
                    )
                )
                invoiceItems.add(
                    InvoiceItem(
                        label = context.getString(R.string.nama_pelanggan),
                        value = item?.details?.customerName.orDash
                    )
                )
                invoiceItems.add(
                    InvoiceItem(
                        label = context.getString(R.string.installment_product),
                        value = order.metadata?.billerName.orDash
                    )
                )
                invoiceItems.add(
                    InvoiceItem(
                        label = context.getString(R.string.period),
                        value = item?.details?.period.orDash
                    )
                )
                invoiceItems.add(
                    InvoiceItem(
                        label = context.getString(R.string.fine),
                        value = item?.details?.fine.orDash
                    )
                )
            }
            PpobConst.CATEGORY_INTERNET_DAN_TV_CABLE -> {
                invoiceItems.add(
                    InvoiceItem(
                        label = context.getString(R.string.nama_pelanggan),
                        value = item?.details?.customerName.orDash,
                        margins = Margin(top = 8)
                    )
                )
                invoiceItems.add(
                    InvoiceItem(
                        label = context.getString(R.string.provider),
                        value = order.metadata?.billerName.orDash
                    )
                )
                invoiceItems.add(
                    InvoiceItem(
                        label = context.getString(R.string.period),
                        value = item?.details?.period.orDash
                    )
                )
                invoiceItems.add(
                    InvoiceItem(
                        label = context.getString(R.string.total_bill),
                        value = Utility.formatAmount(order.amount)
                    )
                )
            }
            PpobConst.CATEGORY_TRAIN_TICKET -> {
                val numberOfAdult =
                    item?.details?.trainPassenger?.filter { passenger -> passenger.type == PpobConst.ADULT_TYPE }?.size.orNil
                val numberOfChild =
                    item?.details?.trainPassenger?.filter { passenger -> passenger.type == PpobConst.INFANT_TYPE }?.size.orNil

                invoiceItems.add(
                    InvoiceItem(
                        label = context.getString(R.string.route_label),
                        value = "${item?.details?.trainOriginStationCode} - ${item?.details?.trainDestinationStationCode}",
                        margins = Margin(top = 8)
                    )
                )
                invoiceItems.add(
                    InvoiceItem(
                        label = context.getString(R.string.nama_pelanggan),
                        value = item?.details?.customerName.orDash
                    )
                )
                invoiceItems.add(
                    InvoiceItem(
                        label = context.getString(R.string.nomor_hp),
                        value = item?.details?.phoneNumber.orDash
                    )
                )
                invoiceItems.add(
                    InvoiceItem(
                        label = context.getString(R.string.e_mail_label),
                        value = item?.details?.customerEmail.orDash
                    )
                )
                invoiceItems.add(
                    InvoiceItem(
                        label = context.getString(R.string.passenger),
                        value = if (numberOfChild > 0) {
                            "$numberOfAdult ${context.getString(R.string.adult)}, $numberOfChild ${
                                context.getString(
                                    R.string.baby
                                )
                            }"
                        } else {
                            "$numberOfAdult ${context.getString(R.string.adult)}"
                        }
                    )
                )
                invoiceItems.add(InvoiceItem(type = InvoiceItemType.DIVIDER))
                invoiceItems.add(
                    InvoiceItem(
                        type = InvoiceItemType.NOTES,
                        label = context.getString(R.string.notes),
                        value = context.getString(R.string.code_booking_info)
                    )
                )
                invoiceItems.add(
                    InvoiceItem(
                        type = InvoiceItemType.CODE_INFO,
                        label = context.getString(R.string.code_booking),
                        value = item?.details?.token.orDash
                    )
                )
            }
            PpobConst.CATEGORY_VOUCHER_GAME -> {
                if (order.metadata?.billerType.isNotNullOrBlank() && order.metadata?.billerType == PpobConst.VOUCHER_TYPE_TOPUP) {
                    invoiceItems.add(
                        InvoiceItem(
                            label = context.getString(R.string.voucher_type),
                            value = order.metadata.billerName.orDash,
                            margins = Margin(top = 8)
                        )
                    )
                    invoiceItems.add(
                        InvoiceItem(
                            label = context.getString(R.string.produk),
                            value = item?.name.orDash
                        )
                    )
                    invoiceItems.add(
                        InvoiceItem(
                            label = order.metadata.idFieldName.orDash,
                            value = order.metadata.idFieldValue.orDash
                        )
                    )
                    invoiceItems.add(InvoiceItem(type = InvoiceItemType.DIVIDER))
                    invoiceItems.add(
                        InvoiceItem(
                            type = InvoiceItemType.NOTES,
                            label = context.getString(R.string.notes),
                            value = context.getString(R.string.voucher_code_info)
                        )
                    )
                    invoiceItems.add(
                        InvoiceItem(
                            type = InvoiceItemType.NOTES,
                            label = context.getString(R.string.notes),
                            value = order.metadata.infoboxText.orDash,
                            margins = Margin(top = 0),
                            hideIfMissing = true
                        )
                    )
                    invoiceItems.add(
                        InvoiceItem(
                            type = InvoiceItemType.CODE_INFO,
                            label = context.getString(R.string.code_voucher),
                            value = item?.details?.voucherCode.orDash
                        )
                    )
                } else {
                    invoiceItems.add(
                        InvoiceItem(
                            label = context.getString(R.string.voucher_type),
                            value = order.metadata?.billerName.orDash,
                            margins = Margin(top = 8)
                        )
                    )
                    invoiceItems.add(
                        InvoiceItem(
                            label = context.getString(R.string.produk),
                            value = item?.name.orDash
                        )
                    )
                    invoiceItems.add(
                        InvoiceItem(
                            label = context.getString(R.string.nomor_hp),
                            value = item?.beneficiary?.phoneNumber.orDash
                        )
                    )
                    invoiceItems.add(InvoiceItem(type = InvoiceItemType.DIVIDER))
                    invoiceItems.add(
                        InvoiceItem(
                            type = InvoiceItemType.NOTES,
                            label = context.getString(R.string.notes),
                            value = context.getString(R.string.voucher_code_info)
                        )
                    )
                    invoiceItems.add(
                        InvoiceItem(
                            type = InvoiceItemType.CODE_INFO,
                            label = context.getString(R.string.code_voucher),
                            value = item?.details?.voucherCode.orDash
                        )
                    )
                }
            }
            // payment in and out case
            else -> {
                if (paymentType == PaymentHistory.TYPE_PAYMENT_IN) {
                    invoiceItems.add(
                        InvoiceItem(
                            label = context.getString(R.string.receipient_detail),
                            value = "${item?.beneficiary?.code} - ${item?.beneficiary?.accountNumber}\n${item?.beneficiary?.name}"
                        )
                    )
                    val value = if (customerEntity?.name.isNotNullOrBlank()) {
                        "${order.payments?.getOrNull(0)?.paymentMethod?.code} - ${customerEntity?.name}"
                    } else {
                        "${order.payments?.getOrNull(0)?.paymentMethod?.code}"
                    }
                    invoiceItems.add(
                        InvoiceItem(
                            type = InvoiceItemType.PAYMENT_CODE_INFO,
                            label = customerEntity?.name,
                            value = value,
                            heading = context.getString(R.string.sender_account)
                        )
                    )
                } else if (paymentType == PaymentHistory.TYPE_PAYMENT_OUT) {
                    val businessOwnerName = when {
                        userProfileEntity?.userName.isNotNullOrBlank() -> {
                            userProfileEntity?.userName
                        }
                        bookEntity?.businessOwnerName.isNotNullOrBlank() -> {
                            bookEntity?.let {
                                if (it.hasCompletedProfileWithOwnerName()) it.businessOwnerName
                                else context.getString(R.string.defaulBusinessName)
                            } ?: run { context.getString(R.string.defaulBusinessName) }
                        }
                        else -> context.getString(R.string.defaulBusinessName)
                    }
                    invoiceItems.add(
                        InvoiceItem(
                            label = context.getString(R.string.sender_detail),
                            value = "${order.payments?.getOrNull(0)?.paymentMethod?.code} - ${ bookEntity?.businessName}\n${businessOwnerName}"
                        )
                    )
                    invoiceItems.add(
                        InvoiceItem(
                            type = InvoiceItemType.PAYMENT_CODE_INFO,
                            label = item?.beneficiary?.name,
                            value = "${item?.beneficiary?.code} - ${item?.beneficiary?.accountNumber}",
                            heading = context.getString(R.string.destination_account)
                        )
                    )
                }
            }
        }
        val sellingPrice = when {
            order.paymentCollectionInfo?.paymentCollectionCashTransactionInfo?.amount.orNil > 0.0 -> order.paymentCollectionInfo?.paymentCollectionCashTransactionInfo?.amount
            item?.sellingPrice.orNil > 0.0 -> item?.sellingPrice
            (paymentType == PaymentHistory.TYPE_PAYMENT_OUT) -> order.amount?.minus(item?.discountedFee.orNil)?.plus(order.loyalty?.tierDiscount.orNil)?.plus(order.loyalty?.subscriptionDiscount.orNil)
            else -> order.amount
        }
        var totalAmount = sellingPrice
        if (isPaymentInOrOut(paymentType) && order.transactionType == PaymentConst.RECORD_IN_CASH) {
            val visibility = (order.agentFeeInfo?.amount.orNil != 0.0).asVisibility()
            invoiceItems.add(
                InvoiceItem(
                    type = InvoiceItemType.DIVIDER,
                    viewTag = PaymentConst.SERVICE_FEE_DIVIDER_VIEW_TAG,
                    viewVisibility = visibility
                )
            )
            if (paymentType == PaymentHistory.TYPE_PAYMENT_OUT) {
                // Jumlah Pembayaran
                invoiceItems.add(
                    InvoiceItem(
                        label = context.getString(R.string.payment_amount),
                        value = Utility.formatAmount(sellingPrice),
                        viewTag = PaymentConst.PAYMENT_AMOUNT_VIEW_TAG,
                        viewVisibility = visibility
                    )
                )
                totalAmount = getTotalAmountForPaymentOut(order)
            } else {
                invoiceItems.add(
                    InvoiceItem(
                        label = context.getString(R.string.bill_amount),
                        value = Utility.formatAmount(getBillAmountForPaymentIn(order)),
                        viewTag = PaymentConst.BILL_AMOUNT_VIEW_TAG,
                        viewVisibility = visibility
                    )
                )
            }
            // Biaya Layanan
            invoiceItems.add(
                InvoiceItem(
                    label = context.getString(R.string.service_fee),
                    value = Utility.formatAmount(order.agentFeeInfo?.amount),
                    viewTag = PaymentConst.SERVICE_FEE_VIEW_TAG,
                    viewVisibility = visibility
                )
            )
        }
        invoiceItems.add(InvoiceItem(type = InvoiceItemType.DIVIDER))
        invoiceItems.add(
            InvoiceItem(
                label = context.getString(R.string.label_total_payment),
                value = Utility.formatAmount(totalAmount),
                margins = Margin(top = 8),
                labelTextDecor = TextDecor(
                    textColor = context.getColorCompat(R.color.black),
                    textStyle = TextStyle.BOLD,
                    fontSize = 16
                ),
                valueTextDecor = TextDecor(
                    textColor = context.getColorCompat(R.color.black),
                    textStyle = TextStyle.BOLD,
                    fontSize = 16
                ),
                viewTag = PaymentConst.TOTAL_PAYMENT_VIEW_TAG
            )
        )

        if (isPaymentInOrOut(paymentType)) {
            invoiceItems.add(
                InvoiceItem(
                    type = InvoiceItemType.NOTES,
                    label = context.getString(R.string.notes),
                    value = order.description?.trim().orEmpty(),
                    hideIfMissing = true,
                    heading = context.getString(R.string.notes),
                    margins = Margin(top = 24)
                )
            )
        }

        return invoiceItems
    }

    private fun getTotalAmountForPaymentOut(
        order: FinproOrderResponse?,
    ): Double? {
        return order?.amount?.minus(order.fee.orNil)?.plus(order.agentFeeInfo?.amount.orNil)
    }

    private fun getBillAmountForPaymentIn(
        order: FinproOrderResponse?,
    ): Double? {
        return order?.amount?.minus(order.agentFeeInfo?.amount.orNil)
    }

    private fun isPaymentInOrOut(paymentType: String?): Boolean {
        return paymentType == PaymentHistory.TYPE_PAYMENT_IN || paymentType == PaymentHistory.TYPE_PAYMENT_OUT
    }

    fun getPpobSuccessMessage(context: Context, order: FinproOrderResponse?): String {
        val categoryCode = order?.items?.firstOrNull()?.beneficiary?.code.orEmpty()
        val ppobCategory =
            if (categoryCode == PpobConst.CATEGORY_PLN_POSTPAID) categoryCode else order?.items?.firstOrNull()?.beneficiary?.category.orEmpty()
        val bookEntity =
            BusinessRepository.getInstance(Application.getAppContext())
                .getBusinessByIdSync(User.getBusinessId())
        val merchantName = bookEntity.bookName
        val tokenCode = order?.items?.firstOrNull()?.details?.token.orDash
        val meterNumber = order?.items?.firstOrNull()?.beneficiary?.accountNumber.orDash
        val productPrice = Utility.formatAmount(order?.items?.firstOrNull()?.sellingPrice)
        val customerEmail = order?.items?.firstOrNull()?.details?.customerEmail.orDash
        val productName = order?.items?.firstOrNull()?.name.orEmpty()
        when (ppobCategory) {
            PpobConst.CATEGORY_LISTRIK -> {
                return context.getString(
                    R.string.prepaid_listrik_message,
                    productName,
                    meterNumber,
                    tokenCode,
                    merchantName
                )
            }
            PpobConst.CATEGORY_VOUCHER_GAME -> {
                if (tokenCode.isNotNullOrBlank()) return context.getString(
                    R.string.voucher_game_message_token,
                    productName,
                    productPrice,
                    tokenCode
                )
                else return context.getString(
                    R.string.voucher_game_message,
                    productName,
                    productPrice
                )
            }
            PpobConst.CATEGORY_TRAIN_TICKET -> {
                return context.getString(R.string.train_ticket_message, productPrice, customerEmail)
            }
            PpobConst.CATEGORY_PLN_POSTPAID, PpobConst.CATEGORY_PULSA_POSTPAID, PpobConst.CATEGORY_BPJS, PpobConst.CATEGORY_PDAM, PpobConst.CATEGORY_INTERNET_DAN_TV_CABLE, PpobConst.CATEGORY_MULTIFINANCE, PpobConst.CATEGORY_VEHICLE_TAX -> {
                //common message is used in case of all postpaid ppob use cases.
                //In future, if we add more postpaid ppob usec ases, please add them above.
                return context.getString(
                    R.string.postpaid_message,
                    context.getString(PpobConst.CATEGORY_NAME_MAP[ppobCategory] ?: R.string.ppob),
                    productPrice,
                    merchantName
                )
            }
            else -> {
                return context.getString(
                    R.string.prepaid_message,
                    context.getString(PpobConst.CATEGORY_NAME_MAP[ppobCategory] ?: R.string.ppob),
                    productPrice,
                    merchantName
                )
            }
        }
    }
}