package com.bukuwarung.payments.ppob.base.view

import android.app.Activity
import android.content.Intent
import android.graphics.Color
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.activity.result.contract.ActivityResultContracts
import com.bukuwarung.R
import com.bukuwarung.activities.superclasses.BaseFragment
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.constants.PaymentConst
import com.bukuwarung.databinding.FragmentPostpaidListrikNewBinding
import com.bukuwarung.payments.CustomerListActivity
import com.bukuwarung.payments.PaymentDownBottomSheet
import com.bukuwarung.payments.bottomsheet.PpobBillDetailsBottomSheet
import com.bukuwarung.payments.constants.PpobConst
import com.bukuwarung.payments.data.model.FinproAddCartRequest
import com.bukuwarung.payments.data.model.FinproBeneficiary
import com.bukuwarung.payments.data.model.FinproOrderResponse
import com.bukuwarung.payments.data.model.ppob.ProfilesItem
import com.bukuwarung.payments.ppob.base.model.PpobEvent
import com.bukuwarung.payments.ppob.base.viewmodel.PostpaidListrikNewViewModel
import com.bukuwarung.utils.InputUtils
import com.bukuwarung.utils.isNotNullOrBlank
import com.bukuwarung.utils.isNotNullOrEmpty
import com.bukuwarung.utils.setSingleClickListener
import com.github.razir.progressbutton.hideProgress
import com.github.razir.progressbutton.showProgress
import javax.inject.Inject
import androidx.fragment.app.viewModels
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class PostpaidListrikNewFragment : BaseFragment(),
    RecentAndFavouriteFragment.IRecentAndFavCommunicator,
    PpobBillDetailsBottomSheet.PpobBillDetailsBsListener {

    private var _binding: FragmentPostpaidListrikNewBinding? = null
    private val binding get() = _binding!!
    private val category = PpobConst.CATEGORY_PLN_POSTPAID
    private var addToCartResponse: FinproOrderResponse? = null
    private var recentAndFavouriteFragment : RecentAndFavouriteFragment? = null

    private val viewModel: PostpaidListrikNewViewModel by viewModels()

    private val from by lazy { arguments?.getString(FROM) }
    private val accountNumber by lazy { arguments?.getString(ACCOUNT_NUMBER) }
    private val phoneNumber by lazy { arguments?.getString(PHONE_NUMBER) }
    companion object {
        private const val FROM = "FROM"
        private const val ACCOUNT_NUMBER = "ACCOUNT_NUMBER"
        private const val PHONE_NUMBER = "PHONE_NUMBER"
        fun createIntent(from: String, accountNumber: String, phoneNumber: String): PostpaidListrikNewFragment {
            val bundle = Bundle().apply {
                putString(FROM, from)
                putString(ACCOUNT_NUMBER, accountNumber)
                putString(PHONE_NUMBER, phoneNumber)
            }
            return PostpaidListrikNewFragment().apply {
                arguments = bundle
            }
        }
    }

    override fun onCreateView(
        layoutInflater: LayoutInflater,
        viewGroup: ViewGroup?,
        bundle: Bundle?
    ): View {
        _binding = FragmentPostpaidListrikNewBinding.inflate(layoutInflater, viewGroup, false)
        return binding.root
    }

    override fun setupView(view: View) {
        addRecentsAndFavourites()
        with(binding) {
            bivCustomerNumber.setFocus()
            bivNumber.onTextChanged { bivNumber.setClearDrawable() }
            ivContact.setSingleClickListener {
                resultLauncher.launch(
                    CustomerListActivity.createIntent(
                        requireContext(),
                        PaymentConst.TYPE_PAYMENT_OUT,
                        AnalyticsConst.PPOB_VEHICLE_TAX,
                        true
                    )
                )
            }
            InputUtils.showKeyboard(requireActivity())
            bivCustomerNumber.onTextChanged {
                bivCustomerNumber.setClearDrawable()
                viewModel.checkCustomerNumberValidation(bivCustomerNumber.getText(), requireContext())
            }
            if(phoneNumber.isNotNullOrBlank()) bivNumber.setText(phoneNumber)
            if (accountNumber.isNotNullOrEmpty()) {
                bivCustomerNumber.setText(accountNumber)
                btnCek.callOnClick()
            }

            btnCek.setSingleClickListener {
                InputUtils.hideKeyBoardWithCheck(requireActivity())
                addToCart()
            }
            btnCek.isEnabled = false
        }
    }

    private fun addToCart() {
        viewModel.addToCart(
            FinproAddCartRequest(
                sku = "",
                beneficiary = FinproBeneficiary(
                    category = PpobConst.CATEGORY_LISTRIK,
                    accountNumber = binding.bivCustomerNumber.getText(),
                    code = PpobConst.CATEGORY_PLN_POSTPAID,
                    phoneNumber = binding.bivNumber.getText()
                ),
                userType = PpobConst.USERTYPE_TREATMENT
            )
        )
    }

    private fun addRecentsAndFavourites() {
        recentAndFavouriteFragment = RecentAndFavouriteFragment.createIntent(category).also {
            childFragmentManager.beginTransaction().add(
                binding.flRecentAndFav.id,
                it
            ).commit()
        }
    }

    override fun subscribeState() {
        viewModel.observeEvent.observe(this) {
            when (it) {
                is PpobEvent.ToDetail -> {
                    addToCartResponse = it.orderDetail
                    trackFetchBillEvent()
                    binding.bivCustomerNumber.setSuccessState("")
                    showBillDetailsBottomSheet()
                }
                is PpobEvent.ServerError -> {
                    trackFetchBillEvent(it.message.orEmpty())
                    binding.bivCustomerNumber.setSuccessState("")
                    showPaymentDownBottomSheet(true, it.message)
                }
                is PpobEvent.InternetError -> {
                    trackFetchBillEvent(it.message.orEmpty())
                    showPaymentDownBottomSheet(false, it.message)
                    binding.bivCustomerNumber.setSuccessState("")
                }
                is PpobEvent.OtherError -> {
                    trackFetchBillEvent(it.message.orEmpty())
                    binding.bivCustomerNumber.setErrorMessage(it.message.orEmpty())
                }
                else -> {
                    binding.bivCustomerNumber.setSuccessState("")
                }
            }
        }
        viewModel.viewState.observe(this) {
            if (it.numberLengthInvalid) {
                binding.bivCustomerNumber.setErrorMessage(getString(R.string.phone_number_length_invalid))
                binding.btnCek.isEnabled = false
            } else if(binding.bivCustomerNumber.getText().isNotBlank()) {
                binding.bivCustomerNumber.setSuccessState("")
                binding.btnCek.isEnabled = true
            }
            if (it.showLoading) {
                binding.btnCek.showProgress {
                    buttonTextRes = null
                    progressColor = Color.BLACK
                }
            } else {
                binding.btnCek.hideProgress(R.string.bt_cek)
            }
        }
    }

    private fun showBillDetailsBottomSheet() {
        val ppobBillDetailsBottomSheet =
            PpobBillDetailsBottomSheet.createInstance(addToCartResponse, category)
        ppobBillDetailsBottomSheet.show(childFragmentManager, PpobBillDetailsBottomSheet.TAG)
    }

    private fun trackFetchBillEvent(reason: String = "") {
        AppAnalytics.trackEvent(
            AnalyticsConst.EVENT_PPOB_FETCH_BILL,
            AppAnalytics.PropBuilder()
                .put(AnalyticsConst.PARAM_PLN, binding.bivCustomerNumber.getText())
                .put(AnalyticsConst.TYPE, AnalyticsConst.LISTRIK_POSTPAID)
                .put(
                    AnalyticsConst.STATUS,
                    if (reason.isBlank()) AnalyticsConst.STATUS_SUCCESS else AnalyticsConst.STATUS_FAILED
                )
                .put(AnalyticsConst.PARAM_REASON, reason)
                .put(AnalyticsConst.PHONE_FILLED, binding.bivNumber.getText()),
            true,
            false,
            false
        )
    }

    private fun showPaymentDownBottomSheet(isServiceDown: Boolean, message: String? = null) {
        val paymentDownBottomSheet = PaymentDownBottomSheet.createInstance(isServiceDown, message)
        paymentDownBottomSheet.show(childFragmentManager, PaymentDownBottomSheet.TAG)
    }

    override fun setData(profilesItem: ProfilesItem) {
        binding.bivCustomerNumber.setText(profilesItem.details?.accountNumber)
        binding.btnCek.callOnClick()
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding?.bivCustomerNumber?.removeTextChanged()
        _binding = null
    }

    override fun refreshFavouritesTab() {
        recentAndFavouriteFragment?.refreshFavAndRecentTab()
    }

    private val resultLauncher =
        registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result ->
            if (result.resultCode == Activity.RESULT_OK) {
                val data: Intent? = result.data
                var phoneNumber =
                    data?.getStringExtra(CustomerListActivity.PHONE_NUMBER).orEmpty()
                if (phoneNumber.startsWith("8")) phoneNumber = "0$phoneNumber"
                binding.bivNumber.setText(phoneNumber)
                binding.bivNumber.setSelection()
            }
        }
}