package com.bukuwarung.payments.ppob.base.view

import android.content.Context
import android.content.res.ColorStateList
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.LinearLayoutManager
import com.bukuwarung.R
import com.bukuwarung.databinding.DialogBillersBinding
import com.bukuwarung.dialogs.base.BaseDialogFragment
import com.bukuwarung.payments.constants.PpobConst
import com.bukuwarung.payments.data.model.Biller
import com.bukuwarung.payments.data.model.PpobProduct
import com.bukuwarung.payments.ppob.PpobUtils
import com.bukuwarung.payments.ppob.base.adapter.PpobBillersAdapter
import com.bukuwarung.payments.ppob.base.model.PpobEvent
import com.bukuwarung.payments.ppob.base.model.PpobViewState
import com.bukuwarung.payments.ppob.base.viewmodel.PpobBillersViewModel
import com.bukuwarung.ui_component.base.BaseErrorView
import com.bukuwarung.utils.afterTextChanged
import com.bukuwarung.utils.getColorCompat
import com.bukuwarung.utils.getDrawableCompat
import com.bukuwarung.utils.hideView
import com.bukuwarung.utils.showView
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject
import androidx.fragment.app.viewModels


@AndroidEntryPoint
class PpobBillersDialog() : BaseDialogFragment() {

    private var _binding: DialogBillersBinding? = null
    private val binding get() = _binding!!
    private var iCommunicator: ICommunicator? = null
    private var ppobProductList: List<PpobProduct>? = null
    private lateinit var adapter: PpobBillersAdapter
    private val selectedProduct by lazy { arguments?.getParcelable(SELECTED_PRODUCT) as? PpobProduct }
    private val category by lazy { arguments?.getString(CATEGORY).orEmpty() }

    private val viewModel: PpobBillersViewModel by viewModels()

    companion object {
        const val TAG = "PpobBillersDialog"
        private const val SELECTED_PRODUCT = "selected_product"
        private const val CATEGORY = "category"
        fun getInstance(selectedProduct: PpobProduct? = null, category: String) =
            PpobBillersDialog().apply {
                arguments = Bundle().apply {
                    putParcelable(SELECTED_PRODUCT, selectedProduct)
                    putString(CATEGORY, category)
                }
            }
    }

    override fun onAttach(context: Context) {
        super.onAttach(context)
        parentFragment?.let { iCommunicator = it as? ICommunicator }
        if (context is ICommunicator) iCommunicator = context
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding =
            DialogBillersBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        with(binding) {
            viewModel.getPpobProductsWithBillerDetails(
                category,
                emptyMap()
            )
            bukuErrorView.addCallback(errorViewCallBack)
            etSearch.apply {
                backgroundTintList =
                    context?.getColorCompat(R.color.black_10)?.let { ColorStateList.valueOf(it) }
                afterTextChanged { searchText ->
                    if (!ppobProductList.isNullOrEmpty()) {
                        viewModel.onSearchTextChanged(searchText, ppobProductList!!)
                    }
                }
            }
            includeToolBar.apply {
                ivHelp.setOnClickListener {
                    startActivity(
                        PpobUtils.getHelpButtonIntent(
                            requireContext(),
                            category
                        )
                    )
                }
                tbPpob.apply {
                    navigationIcon = context.getDrawableCompat(R.drawable.ic_arrow_back)
                    setNavigationOnClickListener {
                        dismiss()
                    }
                }
                toolBarLabel.text = PpobConst.CATEGORY_NAME[category]?.let { getString(it) } ?: getString(R.string.ppob)
                if(category == PpobConst.CATEGORY_VEHICLE_TAX) tvChoosePolicy.showView()
            }
        }
        subscribeState()
    }

    private fun subscribeState() {
        viewModel.observeEvent.observe(this) {
            with(binding) {
                when (it) {
                    is PpobEvent.ShowProductsList -> {
                        rvItem.showView()
                        tvEmpty.hideView()
                        grpNoProducts.hideView()
                        it.list.forEach { product -> product.isSelected = (product.sku == selectedProduct?.sku) }
                        if (!it.isFilteredCall) {
                            ppobProductList = it.list
                            adapter = PpobBillersAdapter(
                                it.list,
                                it.billerDetails ?: emptyMap(),
                                ::clickAction,
                                category
                            )
                            rvItem.layoutManager = LinearLayoutManager(context)
                            rvItem.adapter = adapter
                        } else {
                            if (it.list.isNotEmpty()) {
                                adapter.setData(it.list)
                            } else {
                                rvItem.hideView()
                                if (category == PpobConst.CATEGORY_VEHICLE_TAX) grpNoProducts.showView()
                                else tvEmpty.showView()
                            }
                        }
                    }
                    is PpobEvent.SearchInternetError -> {
                        includeShimmer.sflLayout.hideShimmer()
                        includeShimmer.sflLayout.hideView()
                        bukuErrorView.apply {
                            showView()
                            setErrorType(
                                BaseErrorView.Companion.ErrorType.CUSTOM,
                                getString(R.string.no_connection_title),
                                it.message,
                                getString(R.string.reload), R.drawable.ic_no_inet
                            )
                        }
                    }
                    is PpobEvent.SearchServerError -> {
                        includeShimmer.sflLayout.hideShimmer()
                        includeShimmer.sflLayout.hideView()
                        bukuErrorView.apply {
                            showView()
                            setErrorType(
                                BaseErrorView.Companion.ErrorType.CUSTOM,
                                getString(R.string.server_error_title),
                                it.message,
                                getString(R.string.reload), R.drawable.ic_server_down
                            )
                        }
                    }
                    else -> {}
                }
            }
        }
        viewModel.viewState.observe(this) {
            setViewState(it)
        }
    }

    private fun setViewState(ppobViewState: PpobViewState) {
        with(binding) {
            when {
                ppobViewState.showLoading -> {
                    includeShimmer.sflLayout.showShimmer(true)
                    includeShimmer.sflLayout.showView()
                    bukuErrorView.hideView()
                }
                !ppobViewState.showLoading -> {
                    includeShimmer.sflLayout.hideShimmer()
                    includeShimmer.sflLayout.hideView()
                    bukuErrorView.hideView()
                }
                else -> {}
            }
        }
    }

    private var errorViewCallBack = object : BaseErrorView.Callback {
        override fun ctaClicked() {
            viewModel.getPpobProductsWithBillerDetails(category, emptyMap())
        }

        override fun messageClicked() {}

    }

    private fun clickAction(selectedProduct: PpobProduct, biller: Biller) {
        iCommunicator?.setSelectedProduct(selectedProduct, biller)
        dismiss()
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }

    interface ICommunicator {
        fun setSelectedProduct(selectedProduct: PpobProduct, biller: Biller)
    }

}
