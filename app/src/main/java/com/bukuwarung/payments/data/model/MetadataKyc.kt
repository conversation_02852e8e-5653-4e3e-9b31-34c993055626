package com.bukuwarung.payments.data.model

import android.os.Parcelable
import com.bukuwarung.payments.constants.KycRequirement
import com.bukuwarung.payments.constants.KycStatusMetadata
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class MetadataKyc(
        @SerializedName("requirement")
        val requirement: KycRequirement?,
        @SerializedName("status")
        val status: KycStatusMetadata?
) : Parcelable
