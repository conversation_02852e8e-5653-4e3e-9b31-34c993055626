package com.bukuwarung.payments.data.model

import android.os.Parcelable
import com.bukuwarung.utils.DateTimeUtils
import com.bukuwarung.utils.DateTimeUtils.DD_MMM_YYYY_HH_MM
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class PaymentHistory(
        @SerializedName("order_id")
        val orderId: String? = null,

        @SerializedName("customer_id")
        val customerId: String? = null,

        @SerializedName("display_name")
        val displayName: String? = null,

        @SerializedName("amount")
        val amount: Double? = null,

        @SerializedName("timestamp")
        val timestamp: String? = null,

        @SerializedName("status")
        val status: String? = "",

        @SerializedName("type")
        val type: String? = "",

        val viewType: Int = 0,

        @SerializedName("customer_profile")
        val customerProfile: CustomerProfile? = null,

        @SerializedName("tags")
        val tags: Map<String, Tag>? = null,

        @SerializedName("ledger_account_id")
        val ledgerAccountId: String? = null,

        @SerializedName("linked_order_count")
        var linkedOrderCount: Int? = null,

        @SerializedName("linked_order_id")
        val linkedOrderId: String? = null,

        @SerializedName("serial_number")
        val serialNumber: String? = null,

        @SerializedName("detail_service")
        val detailService: String? = null,

        @SerializedName("original_transaction_type")
        val originalTransactionType: String? = null,

        var isLinkedOrder: Boolean = false
) : Parcelable {


    fun formattedDate(): String? {
        return DateTimeUtils.getFormattedLocalDateTime(timestamp, DD_MMM_YYYY_HH_MM)
    }

    fun isPaymentIn() = type == TYPE_PAYMENT_IN
    fun isPaymentOut() = type == TYPE_PAYMENT_OUT
    // saldo out not included, right now its because saldo out means paying ppob with saldo
    fun isPaymentPpob() = !isPaymentIn() && !isPaymentOut() && !isPaymentSaldoIn()
//    fun isPurchaseFail() = status == STATUS_REFUNDING_FAILED || status == STATUS_REFUNDING || status == STATUS_REFUNDED
    fun isPpobFail() = status == STATUS_FAILED

    fun isPaymentSaldoIn() = type.equals(TYPE_SALDO_IN, ignoreCase = true)
            || type.equals(TYPE_SALDO_REFUND, ignoreCase = true)

    fun isPaymentSaldoOut() = type.equals(TYPE_SALDO_OUT, ignoreCase = true)

    fun isPaymentSaldoRedemption() = type.equals(TYPE_SALDO_REDEMPTION, ignoreCase = true)

    fun isPaymentSaldoCashback() = type.equals(TYPE_SALDO_CASHBACK, ignoreCase = true)

    companion object {
        const val TYPE_PAYMENT_IN = "IN"
        const val TYPE_PAYMENT_OUT = "OUT"
        const val TYPE_CASHBACK_SETTLEMENT = "CASHBACK_SETTLEMENT"
        const val TYPE_SALDO_IN = "SALDO"
        const val TYPE_SALDO_REFUND = "SALDO_IN"
        const val TYPE_SALDO_OUT = "SALDO_OUT"
        const val TYPE_SALDO_REDEMPTION = "SALDO_REDEMPTION"
        const val TYPE_SALDO_CASHBACK = "SALDO_CASHBACK"
        const val TYPE_SALDO_BNPL = "SALDO_BNPL"
        const val SALDO_BNPL = "BNPL"
        const val SALDO = "SALDO"
        const val STATUS_PENDING = "PENDING"
        const val STATUS_CREATED = "CREATED"
        const val STATUS_COMPLETED = "COMPLETED"
        const val STATUS_PAID = "PAID"
        const val STATUS_FAILED = "FAILED"
        const val STATUS_CANCELLED = "CANCELLED"
        const val STATUS_REFUNDING = "REFUNDING"
        const val STATUS_REFUNDED = "REFUNDED"
        const val STATUS_REFUNDING_FAILED = "REFUNDING_FAILED"
        const val STATUS_EXPIRED = "EXPIRED"
        const val STATUS_REJECTED = "REJECTED"
        const val STATUS_RETRY = "RETRY"
        const val STATUS_HOLD = "HOLD"
        const val STATUS_UNHOLD = "UNHOLD"
        const val STATUS_RETRY_1 = "RETRY-1"
        const val STATUS_RETRY_2 = "RETRY-2"
        const val STATUS_RETRY_3 = "RETRY-3"
        const val STATUS_RETRY_4 = "RETRY-4"
        const val STATUS_RETRY_5 = "RETRY-5"
        const val STATUS_FAILED_RETRY_1 = "FAILED_RETRY-1"
        const val STATUS_FAILED_RETRY_2 = "FAILED_RETRY-2"
        const val STATUS_FAILED_RETRY_3 = "FAILED_RETRY-3"
        const val STATUS_FAILED_RETRY_4 = "FAILED_RETRY-4"
        const val STATUS_FAILED_RETRY_5 = "FAILED_RETRY-5"
        const val STATUS_MAXIMUM_FAILED_RETRY = "MAXIMUM_FAILED_RETRY"
        const val TAG_PRICE_LABEL = "TAG_PRICE_LABEL"
        val STATUS_RED_LIST = arrayListOf(
            STATUS_FAILED.lowercase(),
            STATUS_REFUNDING_FAILED.lowercase(),
            STATUS_EXPIRED.lowercase(),
            STATUS_REJECTED.lowercase(),
            STATUS_RETRY_1.lowercase(),
            STATUS_RETRY_2.lowercase(),
            STATUS_RETRY_3.lowercase(),
            STATUS_RETRY_4.lowercase(),
            STATUS_RETRY_5.lowercase(),
            STATUS_FAILED_RETRY_1.lowercase(),
            STATUS_FAILED_RETRY_2.lowercase(),
            STATUS_FAILED_RETRY_3.lowercase(),
            STATUS_FAILED_RETRY_4.lowercase(),
            STATUS_FAILED_RETRY_5.lowercase(),
            STATUS_MAXIMUM_FAILED_RETRY.lowercase(),
            STATUS_CANCELLED.lowercase()
        )
    }
}

@Parcelize
data class Tag(
        @SerializedName("id")
        val id: String? = "",
        @SerializedName("display_text")
        val displayText: String? = "",
        @SerializedName("text_color")
        val textColor: String? = "",
        @SerializedName("background_color")
        val backgroundColor: String? = "",
):Parcelable


data class LinkedOrdersData(
    var linkedOrdersVisibility: Boolean = false,
    var linkedOrdersLoading: Boolean = false,
    var linkedOrders: ArrayList<OrderHistoryData>? = null
)