package com.bukuwarung.payments.data.model

import com.google.gson.annotations.SerializedName

data class EdcConfig(
    @SerializedName("edc_purchase_pending_details")
    val edcPurchasePendingDetails: EdcPurchasePendingDetails? = null
)

data class EdcPurchasePendingDetails(
    @SerializedName("show_snackbar")
    val showSnackbar: Boolean? = null,
    @SerializedName("snackbar_title")
    val snackbarTitle: String? = null,
    @SerializedName("snackbar_desc")
    val snackbarDesc: String? = null,
    @SerializedName("snackbar_desc_highlight")
    val snackbarDescHighlight: String? = null
)
