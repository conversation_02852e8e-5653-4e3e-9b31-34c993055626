package com.bukuwarung.payments.data.model

import android.os.Parcelable
import com.bukuwarung.payments.constants.FeeTier
import com.bukuwarung.payments.constants.KycRequirement
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class PaymentMetadata(
        @SerializedName("fee")
        val fee: MetadataFee?,
        @SerializedName("kyc")
        val kyc: MetadataKyc?,
        @SerializedName("qris")
        val qris: MetadataQris?,
        @SerializedName("remaining_free_quota")
        var remainingFreeQuota: Int?,
        @SerializedName("remaining_full_cashback_quota")
        var remainingFullCbQuota: Int?,
        @SerializedName("cashback_amount")
        var cashbackAmount: Double?
) : Parcelable {
        fun isPaidUsers(): Boolean = FeeTier.PAID == fee?.tier
        fun isKycUsers(): Boolean = KycRequirement.REQUIRED == kyc?.requirement
}