package com.bukuwarung.payments.data.repository

import com.bukuwarung.data.restclient.ApiResponse
import com.bukuwarung.data.session.SessionRemoteRepository
import com.bukuwarung.payments.data.model.FinproOrderResponse
import com.bukuwarung.payments.data.model.ppob.*
import com.bukuwarung.session.SessionManager
import javax.inject.Inject


class ReminderRepository @Inject constructor(
    val remoteDataSource: ReminderRemoteDataSource,
    paymentRemoteDataSource: PaymentsRemoteDataSource,
    sessionRemoteRepository: SessionRemoteRepository
) : ReminderRemoteRepository, PaymentsBaseRepository(paymentRemoteDataSource, sessionRemoteRepository) {

    override suspend fun getList(accountId: String, queryUrl:String): ApiResponse<ReminderResponse> = callWithEnablePaymentCheck { remoteDataSource.getList("finpro/api/reminder/${accountId}/list/${queryUrl}") }

    override suspend fun checkBill(accountId: String, reminderId: String): ApiResponse<CheckBillResponse> = callWithEnablePaymentCheck { remoteDataSource.checkBill(accountId, reminderId) }

    override suspend fun markAsReminded(accountId: String, reminderId: String, request: MarkRemindedRequest): ApiResponse<RemindersItem> = callWithEnablePaymentCheck { remoteDataSource.markAsReminded(accountId, reminderId, request) }

    override suspend fun checkout(accountId: String, reminderId: String, request: MarkRemindedRequest): ApiResponse<FinproOrderResponse> = callWithEnablePaymentCheck { remoteDataSource.checkout(accountId, reminderId, request) }

    override suspend fun checkEligibility(accountId: String): ApiResponse<ReminderEligibilityResponse> = callWithEnablePaymentCheck { remoteDataSource.checkEligibility(accountId) }

    override suspend fun getFilters(tab: String): ApiResponse<ReminderFilterResponse> = callWithEnablePaymentCheck { remoteDataSource.getFilters(SessionManager.getInstance().businessId, tab) }
}

interface ReminderRemoteRepository {
    suspend fun getList(accountId: String, queryUrl: String): ApiResponse<ReminderResponse>

    suspend fun checkBill(accountId: String, reminderId: String): ApiResponse<CheckBillResponse>

    suspend fun markAsReminded(accountId: String, reminderId: String, request: MarkRemindedRequest): ApiResponse<RemindersItem>

    suspend fun checkout(accountId: String, reminderId: String, request: MarkRemindedRequest): ApiResponse<FinproOrderResponse>

    suspend fun checkEligibility(accountId: String): ApiResponse<ReminderEligibilityResponse>

    suspend fun getFilters(tab: String): ApiResponse<ReminderFilterResponse>
}