package com.bukuwarung.payments.data.model

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class FinproGetPaymentMethodsResponse(
        @SerializedName("name")
        val name: String? = null,
        @SerializedName("code")
        val code: String? = null,
        @SerializedName("logo")
        val logo: String? = null,
        @SerializedName("title")
        val title: String? = null,
        @SerializedName("subtext")
        val subtext: String? = null,
        @SerializedName("channels")
        val channels: List<FinproPaymentMethod> = emptyList()
) : Parcelable
