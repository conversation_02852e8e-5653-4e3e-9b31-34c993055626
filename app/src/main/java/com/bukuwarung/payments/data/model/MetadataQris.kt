package com.bukuwarung.payments.data.model

import android.os.Parcelable
import com.bukuwarung.payments.constants.QrisAndKycStatus
import com.bukuwarung.payments.constants.KycStatusMetadata
import com.bukuwarung.payments.constants.VerificationStatus
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class MetadataQris(
    @SerializedName("book_id")
    val bookId: String?,
    @SerializedName("status")
    val status: VerificationStatus?,
    @SerializedName("kyc_status")
    val kycStatus: KycStatusMetadata?,
    @SerializedName("final_status")
    val finalStatus: QrisAndKycStatus?
) : Parcelable
