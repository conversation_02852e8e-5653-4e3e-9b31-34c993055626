package com.bukuwarung.payments.data.repository

import android.os.Build
import com.bukuwarung.Application
import com.bukuwarung.BuildConfig
import com.bukuwarung.BukuWarungKeys
import com.bukuwarung.data.restclient.ApiErrorResponse
import com.bukuwarung.data.restclient.ApiResponse
import com.bukuwarung.data.restclient.ApiSuccessResponse
import com.bukuwarung.data.session.NewSessionRequest
import com.bukuwarung.data.session.SessionRemoteRepository
import com.bukuwarung.database.repository.BusinessRepository
import com.bukuwarung.payments.data.model.EnableCustomerResponse
import com.bukuwarung.session.SessionManager
import com.bukuwarung.session.User
import com.bukuwarung.utils.Utilities


abstract class PaymentsBaseRepository(
    private val paymentRemoteDataSource: PaymentsRemoteDataSource,
    private val sessionRemoteRepository: SessionRemoteRepository
) {
    /**
     * This method can be used to refresh idToken.
     * Returns the new idToken if success, returns null on failure
     */
    suspend fun refreshToken(): String? {
        val sessionManager = SessionManager.getInstance()
        SessionManager.getInstance().isRefreshingToken = true
        sessionManager.setRefreshingTokenFails(false)
        val token: String? = sessionManager.sessionToken
        val deviceId: String? = sessionManager.deviceGUID
        if (token.isNullOrEmpty() || deviceId.isNullOrEmpty()) {
            return null
        }
        val newSessionRequest = NewSessionRequest(
            token = token,
            register = false,
            deviceId = deviceId,
            userId = User.getUserId(),
            deviceModel = Build.MODEL,
            deviceBrand = Build.MANUFACTURER,
            clientId = BukuWarungKeys.clientId.orEmpty(),
            clientSecret = BukuWarungKeys.clientSecret.orEmpty()
        )
        val refreshTokenResponse = sessionRemoteRepository.createNewSession(newSessionRequest)
        sessionManager.isRefreshingToken = false
        if (refreshTokenResponse is ApiSuccessResponse) {
            val newSession = refreshTokenResponse.body
            if (newSession.idToken.isEmpty() || newSession.refreshToken.isEmpty()) {
                //clear the shared preferences and redirect the user to login screen
                Utilities.clearDataAndLogout()
            }
            sessionManager.bukuwarungToken = newSession.idToken
            sessionManager.sessionToken = newSession.refreshToken
            sessionManager.setSessionStart()
            return newSession.idToken
        } else if (refreshTokenResponse is ApiErrorResponse) {
            if (refreshTokenResponse.statusCode == 403) {
                Utilities.clearDataAndLogout()
            }
        }
        return null
    }

    /**
     * This method handles Unauthorized and Unauthenticated errors for any API method.
     * After resolving the errors, it calls the request API method again.
     */
    suspend fun <T : Any> callWithEnablePaymentCheck(
        requestFunc: suspend () -> ApiResponse<T>
    ): ApiResponse<T> {
        val result = requestFunc.invoke()
        if (result is ApiErrorResponse && result.statusCode == 403) {
            val bookEntity = BusinessRepository.getInstance(Application.getAppContext())
                .getBusinessByIdSync(SessionManager.getInstance().businessId)
            val enableResult = enableMerchantPayments(
                SessionManager.getInstance().businessId,
                bookEntity.businessName
            )
            if (enableResult is ApiSuccessResponse)
                return requestFunc.invoke()
        }
        return result
    }

    suspend fun enableMerchantPayments(
        accountId: String, bookName: String
    ): ApiResponse<EnableCustomerResponse> {
        return paymentRemoteDataSource.enabledMerchantPayments(
            accountId, mapOf(Pair("business_name", bookName))
        )
    }
}
