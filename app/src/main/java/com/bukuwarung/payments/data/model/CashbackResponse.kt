package com.bukuwarung.payments.data.model

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class CashbackResponse(
    @SerializedName("id")
    val id: String? = null,
    @SerializedName("amount")
    val amount: Double? = 0.0,
    @SerializedName("externalId")
    val externalId: String? = null,
    @SerializedName("status")
    val status: CashbackStatus? = null,
    @SerializedName("lastModifiedDate")
    val lastModifiedDate: String? = null,
    @SerializedName("created_date")
    val createdDate: String? = null
) : Parcelable

enum class CashbackStatus {
    PENDING, COMPLETED
}