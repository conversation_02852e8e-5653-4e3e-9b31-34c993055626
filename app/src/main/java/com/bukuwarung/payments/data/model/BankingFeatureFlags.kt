package com.bukuwarung.payments.data.model

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize


@Parcelize
data class BankingFeatureFlags(
    @SerializedName("saldo")
    val saldo: Boolean? = false,
    @SerializedName("qris")
    val qris: Boolean? = false,
    @SerializedName("cashback")
    val cashback: Boolean? = false
) : Parcelable