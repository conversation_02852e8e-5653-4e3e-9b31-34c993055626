package com.bukuwarung.payments.data.repository

import com.bukuwarung.database.entity.BankAccount
import javax.inject.Inject

class BankAccountLocalRepositoryImpl @Inject constructor(private val dataStore: BankAccountLocalDataStore) : BankAccountLocalRepository {

    override suspend fun getBankAccounts(bookId: String?): List<BankAccount>? {
        return dataStore.getBankAccounts(bookId)
    }

    override suspend fun insertBankAccounts(list: List<BankAccount>) {
        dataStore.insertBankAccounts(list)
    }

    override suspend fun deleteMerchantBankAccounts(bookId: String?) {
        dataStore.deleteMerchantBankAccounts(bookId)
    }

    override fun getCustomerBankAccounts(
        bookId: String, customerId: String
    ) = dataStore.getCustomerBankAccounts(bookId, customerId)
}

interface BankAccountLocalRepository {
    suspend fun insertBankAccounts(list: List<BankAccount>)
    suspend fun deleteMerchantBankAccounts(bookId: String?)
    suspend fun getBankAccounts(bookId: String?): List<BankAccount>?
    fun getCustomerBankAccounts(bookId: String, customerId: String): List<BankAccount>?
}
