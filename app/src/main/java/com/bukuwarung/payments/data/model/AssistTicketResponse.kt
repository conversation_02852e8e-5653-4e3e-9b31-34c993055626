package com.bukuwarung.payments.data.model

import android.os.Parcelable
import androidx.annotation.StringDef
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class AssistTicketResponse(

    @SerializedName("data")
    val data: List<TicketDataItem?>? = null,

    @SerializedName("message")
    val message: String? = null,

    @SerializedName("status")
    val status: String? = null
) : Parcelable

@Parcelize
data class TicketDataItem(

    @SerializedName("transactionIdExists")
    val transactionIdExists: Boolean? = null,

    @SerializedName("ticketId")
    val ticketId: String? = null,

    @SerializedName("ticketStatus")
    val ticketStatus: Status? = null
) : Parcelable

enum class Status {
    @SerializedName("new")
    NEW,
    @SerializedName("open")
    OPEN,
    @SerializedName("pending")
    PENDING,
    @SerializedName("solved")
    SOLVED
}
