package com.bukuwarung.payments.data.model

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize
import java.io.Serializable

@Parcelize
data class PpobDetailParam(
        @SerializedName("serial_number")
        val serialNumber: String?,
        @SerializedName("paid_at")
        val paidAt: String?,
        @SerializedName("tarif")
        val tarif: String?,
        @SerializedName("amount")
        val amount: Double?,
        @SerializedName("admin_fee")
        val adminFee: Double?,
        @SerializedName("no_meter")
        val noMeter: String?,
        @SerializedName("customer_name")
        val customerName: String?,
        @SerializedName("total_kwh")
        val totalKwh: String?,
        @SerializedName("reference_number")
        val referenceNumber: String?,
        @SerializedName("token")
        val token: String?,
        @SerializedName("total_lembar_tagihan")
        val totalLembarTagihan: String?,
        @SerializedName("periode")
        val periode: String?,
        @SerializedName("product_name")
        val productName: String?,
        @SerializedName("response_code")
        val responseCode: String?,
        @SerializedName("customer_number")
        val customerNumber: String?,
        @SerializedName("installment_number")
        val installmentNumber: String?,
        @SerializedName("fine")
        val fine: String?,
        @SerializedName("e_ticket")
        val eTicket: String?,
        @SerializedName("voucher_code")
        val voucherCode: String? = null,
        @SerializedName("member_count")
        val memberCount: String? = null,
        @SerializedName("period")
        val period: String? = null,
        @SerializedName("policy_number")
        val policyNumber: String? = null,
        @SerializedName("vehicle_name")
        val vehicleName: String? = null,
        @SerializedName("vehicle_type")
        val vehicleType: String? = null,
        @SerializedName("vehicle_brand")
        val vehicleBrand: String? = null,
        @SerializedName("vehicle_color")
        val vehicleColor: String? = null,
        @SerializedName("build_year")
        val buildYear: String? = null,
        @SerializedName("expiration_date")
        val expirationDate: String? = null,
        @SerializedName("machine_number")
        val machineNumber: String? = null,
        @SerializedName("frame_number")
        val frameNumber: String? = null,
        @SerializedName("address")
        val address: String? = null,
        @SerializedName("product_price")
        val productPrice: String? = null,
        @SerializedName("pkb")
        val pkb: String? = null,
        @SerializedName("late_fee")
        val lateFee: String? = null ,
        @SerializedName("customer_email")
        val customerEmail: String? = null,
        @SerializedName("train_origin_station_code")
        val trainOriginStationCode: String? = null,
        @SerializedName("train_destination_station_code")
        val trainDestinationStationCode: String? = null,
        @SerializedName("train_origin_station_name")
        val trainOriginStationName: String? = null,
        @SerializedName("train_destination_station_name")
        val trainDestinationStationName: String? = null,
        @SerializedName("train_arrival_time")
        val trainArrivalTime: String? = null,
        @SerializedName("train_departure_time")
        val trainDepartureTime: String? = null,
        @SerializedName("train_name")
        val trainName: String? = null,
        @SerializedName("train_wagon_name")
        val trainWagonName: String? = null,
        @SerializedName("train_passengers")
        val trainPassenger: List<TrainPassenger>? = null,
        @SerializedName("bnpl_admin_fee")
        val bnplAdminFee: Double?= null,
        @SerializedName("payment_expiration_timestamp")
        val paymentExpirationTimestamp: String? = null,
        @SerializedName("nik")
        val nik: String? = null,
        @SerializedName("phone_number")
        val phoneNumber: String? = null,
        @SerializedName("biller_name")
        val billerName: String? = null
) : Parcelable, Serializable
