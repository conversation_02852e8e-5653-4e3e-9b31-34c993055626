package com.bukuwarung.payments.data.model

import android.os.Parcelable
import com.bukuwarung.payments.constants.KycStatus
import com.bukuwarung.payments.constants.KycTier
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class BasicKyc(
        @SerializedName("status")
        val status: KycStatus? = null,
        @SerializedName("checks")
        val checks: List<KycChecks>?=null,
        @SerializedName("kyc_tier")
        val kycTier: KycTier? = null,
        @SerializedName("name")
        val name: String? = null,
) : Parcelable
