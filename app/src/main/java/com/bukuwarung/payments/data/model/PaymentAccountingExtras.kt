package com.bukuwarung.payments.data.model

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize
import java.io.Serializable

@Parcelize
data class PaymentAccountingExtras(
        @SerializedName("category_id")
        val categoryId: String? = null,
        @SerializedName("transaction_id")
        val transactionId: String? = null
): Parcelable
