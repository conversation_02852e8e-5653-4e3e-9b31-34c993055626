package com.bukuwarung.payments.data.model.ppob

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class ValidationInfo(
        @SerializedName("validation_regex")
        val validationRegex: String?,
        @SerializedName("validation_error_message")
        val validationErrorMessage: String?,
        @SerializedName("value_type")
        val valueType: String?
) : Parcelable