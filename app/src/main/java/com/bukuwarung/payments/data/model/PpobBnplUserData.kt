package com.bukuwarung.payments.data.model

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class PpobBnplUserData(
    @SerializedName("isUserWhitelisted")
    val isUserWhitelisted: Boolean? = null,
    @SerializedName("isRegisteredUser")
    val isRegisteredUser: Boolean? = null,
    @SerializedName("currentLimit")
    val currentLimit: Double? = null,
    @SerializedName("totalLimit")
    val totalLimit: Double? = null,
    @SerializedName("transactionFee")
    val transactionFee: Double? = null,
    @SerializedName("adminFee")
    val adminFee: Double? = null,
    @SerializedName("repaymentAmount")
    val repaymentAmount: Double? = null,
    @SerializedName("userStatus")
    val userStatus: String? = null,
    @SerializedName("minTrx")
    val minTrx: Double? = null,
    @SerializedName("maxTrx")
    val maxTrx: Double? = null,
    @SerializedName("dueDate")
    val dueDate: String? = null,
    @SerializedName("startDate")
    val startDate: String? = null,
    @SerializedName("endDate")
    val endDate: String? = null,
    @SerializedName("introMessage")
    val introMessage: String? = null,
    @SerializedName("tncMessage")
    val tncMessage: String? = null
): Parcelable