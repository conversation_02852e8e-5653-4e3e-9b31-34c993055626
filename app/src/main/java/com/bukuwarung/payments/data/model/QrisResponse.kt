package com.bukuwarung.payments.data.model

import android.os.Parcelable
import com.bukuwarung.payments.constants.VerificationStatus
import com.bukuwarung.utils.isNotNullOrEmpty
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class QrisResponse(
    @SerializedName("virtual_account_id")
    val virtualAccountId: String? = null,
    @SerializedName("kyc")
    val kyc: Boolean? = false,
    @SerializedName("qris_code")
    val qrisCode: String? = null,
    @SerializedName("kyc_status")
    val kycStatus: String? = null,
    @SerializedName("kyb_status")
    val kybStatus: String? = null,
    @SerializedName("qris_status")
    val qrisStatus: String? = null,
    @SerializedName("matching_status")
    val matchingStatus: String? = null,
    @SerializedName("final_status")
    val finalStatus: String? = null,
    @SerializedName("metadata")
    val metadata: QrisMetadataDAO? = null
): Parcelable {
    fun isApprovedQrisUser(): Boolean {
        return kycStatus == VerificationStatus.VERIFIED.name
                && qrisStatus == VerificationStatus.VERIFIED.name
                && qrisCode.isNotNullOrEmpty()
    }
}

@Parcelize
data class QrisMetadataDAO(
    @SerializedName("qris")
    val qris: QrisMetadata? = null
): Parcelable

@Parcelize
data class QrisMetadata(
    @SerializedName("code")
    val code: String? = null,
    @SerializedName("business_name")
    val businessName: String? = null,
    @SerializedName("merchant_id")
    val merchantId: String? = null,
    @SerializedName("merchant_name")
    val merchantName: String? = null,
    @SerializedName("qr_string")
    val qrString: String? = null,
    @SerializedName("nmid")
    val nmid: String? = null,
    @SerializedName("payment_book_id")
    val paymentBookId: String? = null,
    @SerializedName("unique_business_name")
    val uniqueBusinessName: String? = null,
    @SerializedName("proposed_bank_account_id")
    val proposedBankAccountId: String? = null,
    @SerializedName("matching_rejected_reason")
    val matchingRejectionReason: String? = null
): Parcelable


/**
 * This we are using to store parts of QRIS response in the shared preferences.
 */
@Parcelize
data class QrisInfoSubset(
    val virtualAccountId: String? = null,
    val kycStatus: String? = null,
    val kybStatus: String? = null,
    val qrisStatus: String? = null,
    val qrisCode: String? = null,
    val matchingStatus: String? = null,
    var finalStatus: String? = null,
    val qrisBookId: String? = null,
    val qrisBookName: String? = null,
    var qrisBankId: String? = null
): Parcelable {
    fun isApprovedQrisUser(): Boolean {
        return kycStatus == VerificationStatus.VERIFIED.name
                && qrisStatus == VerificationStatus.VERIFIED.name
                && qrisCode.isNotNullOrEmpty()
    }
}