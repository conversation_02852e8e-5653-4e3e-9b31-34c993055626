package com.bukuwarung.payments.data.model

import android.os.Parcelable
import com.bukuwarung.database.entity.Bank
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class ReceiverBank(
        @SerializedName("bank_code")
        val bankCode: String? = null,

        @SerializedName("account_number")
        val accountNumber: String? = null,

        @SerializedName("owner_name")
        val accountHolderName: String? = null,

        val isSelected: Boolean = false
) : Parcelable {

    fun getBankLogoIfAvailable(): String? {
        val currentBank = Bank(bankCode ?: "", "")
        return if (Bank.BANKS.contains(currentBank)) {
            Bank.BANKS.first { it.bankCode == currentBank.bankCode }.logo
        } else {
            null
        }
    }

    fun getSenderInfo(): String {
        return "$bankCode - $accountHolderName"
    }

}