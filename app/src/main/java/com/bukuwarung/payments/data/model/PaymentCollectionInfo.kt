package com.bukuwarung.payments.data.model

import android.os.Parcelable
import androidx.annotation.Keep
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Keep
@Parcelize
data class PaymentCollectionInfo(
    @SerializedName("cash_transaction")
    val paymentCollectionCashTransactionInfo: PaymentCollectionCashTransactionInfo? = null,
    @SerializedName("detail")
    val paymentCollection: PaymentCollection? = null
) : Parcelable
