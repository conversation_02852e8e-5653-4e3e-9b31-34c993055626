package com.bukuwarung.payments.data.model.ppob

import android.os.Parcelable
import androidx.annotation.Keep
import com.bukuwarung.payments.data.model.Biller
import com.bukuwarung.utils.DateTimeUtils
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Keep
@Parcelize
data class FavouriteResponse(

        @SerializedName("metadata")
        val metadata: Metadata? = null,

        @SerializedName("profiles")
        val profiles: List<ProfilesItem>? = null
) : Parcelable

@Keep
@Parcelize
data class Detail(

        @SerializedName("ACCOUNT_NUMBER")
        val accountNumber: String? = null,

        @SerializedName("zoneId")
        val zoneId: String? = null,

        @SerializedName("PHONE_NUMBER")
        val phoneNumber: String? = null,

        @SerializedName("REGION")
        val region: String? = null,
        @SerializedName("MACHINE_NUMBER")
        val machineNumber: String? = null,
        @SerializedName("POLICY_NUMBER")
        val policyNumber: String? = null,
        @SerializedName("NIK")
        val nik: String? = null
) : Parcelable

@Keep
@Parcelize
data class ProfilesItem(

        @SerializedName("alias")
        val alias: String? = null,

        @SerializedName("favourites_text")
        val favouritesText: String? = null,

        @SerializedName("additional_count")
        val additionalCount: String? = null,

        @SerializedName("details")
        val details: Detail? = null,

        @SerializedName("id")
        val id: String? = null,

        @SerializedName("accountId")
        val accountId: String? = null,

        @SerializedName("lookupKey")
        val lookupKey: String? = null,

        @SerializedName("favorite")
        val favorite: Boolean? = null,

        @SerializedName("recent_transaction")
        val recentTransaction: RecentTransaction? = null,

        @SerializedName("category")
        val category: String? = null,

        @SerializedName("biller")
        val biller: Biller? = null,

        val viewType: Int = 0
) : Parcelable

@Parcelize
data class RecentTransaction(

        @SerializedName("order_id")
        val orderId: String? = null,

        @SerializedName("timestamp")
        val timestamp: String? = null,

        @SerializedName("amount")
        val amount: Double? = null
) : Parcelable {

    fun formattedDate(): String {
        return DateTimeUtils.getFormattedLocalDateTime(timestamp, DateTimeUtils.DD_MMM_YYYY_HH_MM)
    }
}
