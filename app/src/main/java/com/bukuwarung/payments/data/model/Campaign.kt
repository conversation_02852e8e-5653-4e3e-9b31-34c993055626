package com.bukuwarung.payments.data.model

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class Campaign(
        @SerializedName("id")
        var id: String? = null,
        @SerializedName("type")
        var type: String? = null,
        @SerializedName("display_text")
        var displayText: String? = null,
        @SerializedName("amount")
        var amount: Double? = null,
        @SerializedName("payment_method_channel")
        var paymentMethodChannel: String? = null,
        @SerializedName("payment_method_family")
        var paymentMethodfamily: String? = null,
        @SerializedName("product_code")
        var productCode: String? = null) : Parcelable