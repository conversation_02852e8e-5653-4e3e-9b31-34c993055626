package com.bukuwarung.payments.data.repository

import com.bukuwarung.data.restclient.ApiResponse
import com.bukuwarung.data.session.SessionRemoteRepository
import com.bukuwarung.payments.core.model.PaymentOutStatusPollingResponse
import com.bukuwarung.payments.data.model.*
import com.bukuwarung.payments.data.model.ppob.*
import retrofit2.http.Body
import javax.inject.Inject


open class FinproRepository @Inject constructor(
        val remoteDataSource: FinproRemoteDataSource,
        paymentRemoteDataSource: PaymentsRemoteDataSource,
        sessionRemoteRepository: SessionRemoteRepository,
) : FinproRemoteRepository, PaymentsBaseRepository(paymentRemoteDataSource, sessionRemoteRepository) {

    override suspend fun getPpobProductsWithBillerDetails(
        category: String,
        map: Map<String, String>,
        isSpecialProduct: Boolean?
    ): ApiResponse<PpobProductsWithBillerDetails> {
        val sb = StringBuilder()
        for ((key, value) in map) {
            sb.append("$key:$value,")
        }
        sb.removeSuffix(",")
        return callWithEnablePaymentCheck {
            remoteDataSource.getPpobProductsWithBillerDetails(
                category,
                sb.toString(),
                isSpecialProduct
            )
        }
    }

    override suspend fun addItemToCart(bookId: String, request: FinproAddCartRequest) = remoteDataSource.addItemToCart(bookId, request)

    override suspend fun getTrainTicketUrl(accountId: String, request: TrainEnrollmentRequest) = remoteDataSource.getTrainTicketUrl(accountId, request)

    override suspend fun getTrainEnrollmentDetail(accountId: String, enrollmentId: String) =
            remoteDataSource.getTrainEnrollmentDetail(accountId, enrollmentId)

    override suspend fun getPaymentMethods(amount: Double, category: String, productCode: String) = remoteDataSource.getPaymentMethods(amount, category, productCode)

    override suspend fun getPaymentMethodsV2(amount: Double, category: String, productCode: String) = remoteDataSource.getPaymentMethodsV2(amount, category, productCode)

    override suspend fun checkoutOrder(bookId: String, orderId: String, bankAccount: FinproCheckoutOrderRequest, checkoutToken: String) = callWithEnablePaymentCheck { remoteDataSource.checkoutOrder(bookId, orderId, bankAccount, checkoutToken) }

    override suspend fun getOrderDetail(bookId: String, orderId: String, ledgerAccountId: String?) = callWithEnablePaymentCheck { remoteDataSource.getOrderDetail(bookId, orderId, ledgerAccountId) }

    override suspend fun getPaymentOutStatus(
        accountId: String,
        disbursementId: String
    ): ApiResponse<PaymentOutStatusPollingResponse> = callWithEnablePaymentCheck {
        remoteDataSource.getPaymentOutStatus(
            accountId,
            disbursementId
        )
    }

    override suspend fun getPpobStatus(
        accountId: String,
        orderId: String
    ): ApiResponse<PpobStatusPollingResponse> =
        callWithEnablePaymentCheck { remoteDataSource.getPpobStatus(accountId, orderId) }
    override suspend fun disableFavFlagOnMerchantLevel(bookId: String, flagName: String, operation: String) = callWithEnablePaymentCheck { remoteDataSource.disableFavFlagOnMerchantLevel(bookId, flagName, operation) }
    override suspend fun disableFavFlagOnTransactionLevel(bookId: String, orderId: String, flagName: String, operation: String) = callWithEnablePaymentCheck { remoteDataSource.disableFavFlagOnTransactionLevel(bookId, orderId, flagName, operation) }
    override suspend fun getOrders(
            bookId: String,
            customerId: String?,
            startDate: String?,
            endDate: String?,
            type: List<String>?,
            status: List<String>?,
            page: Int?,
            limit: Int?, billerCode: String?,
            sorting: String?
    ) = callWithEnablePaymentCheck { remoteDataSource.getOrders(bookId, customerId, startDate, endDate, type, status, page, limit, billerCode, sorting) }

    override suspend fun getPaymentBannerInfo(bookId: String): ApiResponse<List<PaymentBannerInfoResponse>> = callWithEnablePaymentCheck { remoteDataSource.getPaymentBannerInfo(bookId) }

    override suspend fun getPaymentSummary(bookId: String) = remoteDataSource.getPaymentSummary(bookId)
    override suspend fun getHistoryMonths(bookId: String, customerId: String) = remoteDataSource.getHistoryMonths(bookId, customerId)
    override suspend fun doHealthCheck(paymentHealthCheckRequest: PaymentHealthCheckRequest) = remoteDataSource.doHealthCheck(paymentHealthCheckRequest)
    override suspend fun getBillers(category: String?): ApiResponse<List<Biller>> = remoteDataSource.getBillers(category)
    override suspend fun getSaldo(): ApiResponse<SaldoResponse> = remoteDataSource.getSaldo()
    override suspend fun getSaldoAdminFee(): ApiResponse<SaldoAdminFeeResponse> = remoteDataSource.getSaldoAdminFee()
    override suspend fun topupSaldo(request: TopupSaldoRequest): ApiResponse<FinproOrderResponse> = remoteDataSource.topupSaldo(request)
    override suspend fun activateSaldo(request: ActivateSaldoRequest): ApiResponse<SaldoResponse> = remoteDataSource.activateSaldo(request)
    override suspend fun getExistingSaldoTopup(): ApiResponse<List<ExistingTopupSaldoResponse>> = remoteDataSource.getExistingSaldoTopup("CREATED")
    override suspend fun addFavourite(bookId: String, favouriteRequest: FavouriteRequest) = remoteDataSource.addFavourite(bookId, favouriteRequest)
    override suspend fun deleteFavourite(bookId: String, favouriteId: String) = remoteDataSource.deleteFavourite(bookId, favouriteId)
    override suspend fun cancelOrder(accountId: String, orderId: String) = remoteDataSource.cancelOrder(accountId, orderId)
    override suspend fun getRecentTransactions(bookId: String, category: String, page: Int, limit: Int) = remoteDataSource.getRecentTransactions(bookId, category, page, limit)
    override suspend fun getBillersList(category: String): ApiResponse<CatalogListResponse> = remoteDataSource.getBillersList(category)
    override suspend fun getCategory(): ApiResponse<CategoryResponse> = remoteDataSource.getCategory()
    override suspend fun setSellingPrice(
        request: SellingPriceRequest,
        category: String?,
        code: String?
    ): ApiResponse<Void> = remoteDataSource.setSellingPrice(request, category, code)
    override suspend fun getRecommendations(
        bookId: String,
        orderId: String,
        query: String,
        page: Int,
        count: Int
    ): ApiResponse<FavouriteResponse> = remoteDataSource.getRecommendations(bookId, orderId, query, page, count)
    override suspend fun getPromotionBannerFilter(): ApiResponse<PromotionBannerFilterResponse> = remoteDataSource.getPromotionBannerFilter()
    override suspend fun getPromotionBannerData(sectionId: String): ApiResponse<PromoBannerDataResponse> = remoteDataSource.getPromotionBannerData(sectionId)

    override suspend fun addRefundBankAccounts(accountId: String, finproRefunds: FinproRefunds) =
        callWithEnablePaymentCheck {
            remoteDataSource.addRefundBankAccount(accountId, finproRefunds)
        }

    override suspend fun getRefundMethods(accountId: String) = callWithEnablePaymentCheck {
        remoteDataSource.getRefundMethods(accountId)
    }

    override suspend fun changeRefundBankAccount(accountId: String, finproRefunds: FinproRefunds) =
        callWithEnablePaymentCheck {
            remoteDataSource.setSelectedRefundBank(accountId, finproRefunds)
        }

    override suspend fun getFavourites(bookId: String, category: String, page: Int, count: Int) =
            remoteDataSource.getFavourites(bookId, category, page, count)
}

interface FinproRemoteRepository {
    suspend fun checkoutOrder(
        bookId: String,
        orderId: String,
        bankAccount: FinproCheckoutOrderRequest,
        checkoutToken: String
    ): ApiResponse<FinproOrderResponse>
    suspend fun getPpobProductsWithBillerDetails(
        category: String,
        map: Map<String, String>,
        isSpecialProduct: Boolean?
    ): ApiResponse<PpobProductsWithBillerDetails>
    suspend fun addItemToCart(bookId: String, @Body request: FinproAddCartRequest): ApiResponse<FinproOrderResponse>
    suspend fun getTrainTicketUrl(accountId: String, request: TrainEnrollmentRequest) : ApiResponse<TrainEnrollmentResponse>
    suspend fun getTrainEnrollmentDetail(accountId: String, enrollmentId: String) : ApiResponse<TrainEnrollmentDetailResponse>
    suspend fun getPaymentMethods(amount: Double, category: String, productCode: String): ApiResponse<List<FinproGetPaymentMethodsResponse>>
    suspend fun getPaymentMethodsV2(amount: Double, category: String, productCode: String): ApiResponse<FinproGetPaymentMethodsV2Response>
    suspend fun getOrderDetail(bookId: String, orderId: String, ledgerAccountId: String?): ApiResponse<FinproOrderResponse>

    suspend fun getPaymentOutStatus(accountId: String, disbursementId: String): ApiResponse<PaymentOutStatusPollingResponse>

    suspend fun getPpobStatus(accountId: String, orderId: String): ApiResponse<PpobStatusPollingResponse>

    suspend fun disableFavFlagOnMerchantLevel(bookId: String, flagName: String, operation: String): ApiResponse<Any>
    suspend fun disableFavFlagOnTransactionLevel(bookId: String, orderId: String, flagName: String, operation: String): ApiResponse<Any>
    suspend fun getOrders(
            bookId: String,
            customerId: String? = null,
            startDate: String? = null,
            endDate: String? = null,
            type: List<String>? = null,
            status: List<String>? = null,
            page: Int? = null,
            limit: Int? = null,
            billerCode: String? = null,
            sorting: String? = null
    ): ApiResponse<List<PaymentHistory>>

    suspend fun getPaymentBannerInfo(bookId: String): ApiResponse<List<PaymentBannerInfoResponse>>
    suspend fun getPaymentSummary(bookId: String): ApiResponse<PaymentSummaryResponse>
    suspend fun addFavourite(bookId: String, favouriteRequest: FavouriteRequest): ApiResponse<AddFavouriteResponse>
    suspend fun getHistoryMonths(bookId: String, customerId: String): ApiResponse<List<String>>
    suspend fun doHealthCheck(paymentHealthCheckRequest: PaymentHealthCheckRequest): ApiResponse<PaymentHealthCheckResponse>
    suspend fun getBillers(category: String?): ApiResponse<List<Biller>>
    suspend fun getSaldo(): ApiResponse<SaldoResponse>
    suspend fun getSaldoAdminFee(): ApiResponse<SaldoAdminFeeResponse>
    suspend fun topupSaldo(request: TopupSaldoRequest): ApiResponse<FinproOrderResponse>
    suspend fun activateSaldo(request: ActivateSaldoRequest): ApiResponse<SaldoResponse>
    suspend fun getExistingSaldoTopup(): ApiResponse<List<ExistingTopupSaldoResponse>>
    suspend fun deleteFavourite(bookId: String, favouriteId: String): ApiResponse<DeleteFavouriteResponse>
    suspend fun cancelOrder(accountId: String, orderId: String)
    suspend fun getRecentTransactions(bookId: String, category: String, page: Int, limit: Int): ApiResponse<FavouriteResponse>
    suspend fun getCategory(): ApiResponse<CategoryResponse>
    suspend fun getBillersList(category: String): ApiResponse<CatalogListResponse>
    suspend fun setSellingPrice(request: SellingPriceRequest, category: String?, code: String?): ApiResponse<Void>
    suspend fun getRecommendations(
        bookId: String,
        orderId: String,
        query: String,
        page: Int,
        count: Int
    ): ApiResponse<FavouriteResponse>
    suspend fun getPromotionBannerFilter(): ApiResponse<PromotionBannerFilterResponse>
    suspend fun getPromotionBannerData(sectionId: String): ApiResponse<PromoBannerDataResponse>
    suspend fun addRefundBankAccounts(
        accountId: String, finproRefunds: FinproRefunds
    ): ApiResponse<FinproRefunds>

    suspend fun getRefundMethods(accountId: String): ApiResponse<List<FinproRefunds>>
    suspend fun changeRefundBankAccount(
        accountId: String, finproRefunds: FinproRefunds
    ): ApiResponse<Boolean>
    suspend fun getFavourites(
        bookId: String, category: String, page: Int, count: Int
    ): ApiResponse<FavouriteResponse>
}
