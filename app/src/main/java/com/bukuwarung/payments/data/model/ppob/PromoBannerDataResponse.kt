package com.bukuwarung.payments.data.model.ppob

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class PromoBannerDataResponse(

	@field:SerializedName("profit_margins")
	val profitMargins: Map<String, Double>? = null,

	@field:SerializedName("icon_sprites")
	val iconSprites: Map<String, List<String>>? = null,

	@field:SerializedName("title")
	val title: String? = null,

	@field:SerializedName("product_groups")
	val productGroups: List<ProductGroupsItem?>? = null
) : Parcelable


@Parcelize
data class ProductGroupsItem(

	@field:SerializedName("biller_icon")
	val billerIcon: String? = null,

	@field:SerializedName("biller_code")
	val billerCode: String? = null,

	@field:SerializedName("biller_display_name")
	val billerDisplayName: String? = null,

	@field:SerializedName("products")
	val products: List<ProductItem>? = null
) : Parcelable

@Parcelize
data class ProductItem(

	@field:SerializedName("product_code")
	val productCode: String? = null,

	@field:SerializedName("is_default")
	val isDefault: Boolean? = null,

	@field:SerializedName("product_name")
	val productName: String? = null,

	@field:SerializedName("pricing")
	val pricing: Double? = null
) : Parcelable
