package com.bukuwarung.payments.data.model

import android.os.Parcelable
import androidx.annotation.Keep
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize


@Keep
@Parcelize
data class AggregationData(
    @SerializedName("metadata")
    val metadata: List<Metadata>? = null
) : Parcelable

@Keep
@Parcelize
data class Metadata(
    @SerializedName("ledgerable")
    val ledgerable: Ledgerable? = null
) : Parcelable

@Keep
@Parcelize
data class Ledgerable(
    @SerializedName("type")
    val type: String? = null
) : Parcelable
