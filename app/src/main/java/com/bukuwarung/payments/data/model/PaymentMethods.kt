package com.bukuwarung.payments.data.model

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize


@Parcelize
data class PaymentMethodsResponse(
    @SerializedName("payment_methods")
    val paymentMethods: PaymentMethods? = null,

    @SerializedName("type")
    val type: String? = null,
) : Parcelable


@Parcelize
data class PaymentMethods(
    @SerializedName("bank_transfer")
    val bankTransfers: List<PaymentMethod>? = null,
    @SerializedName("buku")
    val buku: List<PaymentMethod>? = null,
    @SerializedName("ewallet")
    val eWallets: List<PaymentMethod>? = null,
    @SerializedName("retail_outlet")
    val retailOutlets: List<PaymentMethod>? = null,
) : Parcelable


@Parcelize
data class PaymentMethod(
    @SerializedName("minimum_disbursement_amount")
    val minDisbursement: Double? = null,
    @SerializedName("maximum_disbursement_amount")
    val maxDisbursement: Double? = null,
    @SerializedName("name")
    val name: String? = null,
    @SerializedName("code")
    val code: String? = null,
    @SerializedName("logo")
    val logo: String? = null,
    @SerializedName("enabled")
    val enabled: EnabledStatus? = null,
    @SerializedName("fee_details")
    val feeDetails: FeeDetails? = null,
    val saldoBalance: Double? = null,
    val dailySaldoLimit: Double? = null,
    val monthlySaldoLimit: Double? = null
) : Parcelable


@Parcelize
data class EnabledStatus(
    @SerializedName("status")
    val status: Boolean? = null,
    @SerializedName("message")
    val message: String? = null,
    @SerializedName("reason_code")
    val reasonCode: String? = null,
) : Parcelable