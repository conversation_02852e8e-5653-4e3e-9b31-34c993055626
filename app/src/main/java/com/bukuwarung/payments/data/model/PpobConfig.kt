package com.bukuwarung.payments.data.model

import com.bukuwarung.constants.AppConst
import com.bukuwarung.payments.constants.PpobConst
import com.google.gson.annotations.SerializedName


data class PpobConfig(
        @SerializedName("ppob_list")
        val ppobList: List<PpobListItem>? = null,
        val trainPollingConfig: TrainPollingConfig? = null,
        val supportUrls: PpobSupportUrls = PpobSupportUrls(),
        val freeFormEwalletCap: Double = 5000.0,
        val ppobPollingConfig: PpobPollingConfig? = null,
        val favouritesPaginationLimit: Int? = 10
)

data class PpobPollingConfig(
        val ppobStatusPollingIntervalSeconds: Long = 5,
        val ppobStatusPollingTotalTimeSeconds: Long = 30
)

data class TrainPollingConfig(
        val pollingTimeInSeconds: Int? = PpobConst.DEFAULT_POLLING_TIME,
        val pollingInternalSeconds: Int = PpobConst.DEFAULT_POLLING_INTERVAL,
        val redirectionUrlContains: String = PpobConst.REDIRECTION_URL_CONTAINS
)

data class PpobSupportUrls(
        val pulsa: String = AppConst.PULSA_BANTUAN,
        val tokenListrik: String = AppConst.TOKEN_LISTRIK_BANTUAN,
        val eWallet: String = AppConst.EWALLET_BANTUAN,
        val packetData: String = AppConst.PAKET_DATA_BANTUAN,
        val postpaidListrik: String = AppConst.TAGIHAN_LISTRIK_BANTUAN,
        val voucherGame: String = AppConst.VOUCHER_GAME_BANTUAN,
        val bpjs: String = AppConst.BPJS_BANTUAN,
        val pdam: String = AppConst.PDAM_BANTUAN,
        val multiFinance: String = AppConst.MULTIFINANCE_BANTUAN,
        val internetTvCable: String = AppConst.INTERNET_DAN_TV_CABLE_BANTUAN,
        val vehicleTax: String = AppConst.VEHICLE_TAX_BANTUAN,
        val trainTickets: String = AppConst.TRAIN_TICKET_BANTUAN
)