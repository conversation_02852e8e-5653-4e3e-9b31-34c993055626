package com.bukuwarung.payments.data.model


data class AppTexts(
    val poweredByFooter: String? = null,
    val poweredByFooterPlain: String? = null,
    val paymentTutorialTitle: String? = null,
    val qrisSuccessMessage: String? = null,
    val qrisMDRInfoTooltip: String? = null,
    val qrisMDRLabel: String? = null,
    val kycRequiredTitle: String? = null,
    val kycRequiredMessage: String? = null,
    val kycSupremeRequiredTitle: String? = null,
    val kycSupremeRequiredMessage: String? = null,
    val saldoTopupWarningTitle: String? = null,
    val saldoTopupWarningMessage: String? = null,
    val saldoOutNonKycError: String? = null,
    val saldoOutLimitError: String? = null,
    val cashbackMessage: String? = null,
    val cashbackTransactionMessage: String? = null,
    val manualVerificationTime: String? = null,
    val appealFlowSubmittedText: String? = null,
    val kybVerificationDrawerTitle: String? = null,
    val kybVerificationDrawerMessage: String? = null,
    val qrisKybPendingMessage: String? = null,
    val qrisKybRequiredMessage: String? = null,
    val qrisKybProcessedMessage: String? = null,
    val appUpdateTitle: String? = null,
    val appUpdateMessage: String? = null,
    val kybSubmittedTitle: String? = null,
    val kybSubmittedMessage: String? = null,
    val kybPendingBannerTitle: String? = null,
    val kybPendingBannerMessage: String? = null,
    val kycRequiredInfoTitle1: String? = null,
    val kycRequiredInfoMessage1: String? = null,
    val kycRequiredInfoTitle2: String? = null,
    val kycRequiredInfoMessage2: String? = null,
    val kycRequiredInfoTitle3: String? = null,
    val kycRequiredInfoMessage3: String? = null,
    val kybRequiredTitle: String? = null,
    val kybRequiredInfoTitle1: String? = null,
    val kybRequiredInfoMessage1: String? = null,
    val kybRequiredInfoTitle2: String? = null,
    val kybRequiredInfoMessage2: String? = null,
    val kybRequiredInfoTitle3: String? = null,
    val kybRequiredInfoMessage3: String? = null,
    val kybRequiredInfoTitle4: String? = null,
    val kybRequiredInfoMessage4: String? = null,
    val kycInProgressTitle: String? = null,
    val kycInProgressMessage: String? = null,
    val kybInProgressTitle: String? = null,
    val kybInProgressMessage: String? = null,
    val kybVerifiedTitle: String? = null,
    val kycVerifiedTitle: String? = null,
    val kybRejectedTitle: String? = null,
    val kybRejectedMessage: String? = null,
    val kycRejectedTitle: String? = null,
    val kycRejectedMessage: String? = null,
    val kycRequiredBannerTitle: String? = null,
    val kycRequiredBannerMessage: String? = null,
    val kycProcessedBannerTitle: String? = null,
    val kycProcessedBannerMessage: String? = null,
    val kycRejectedBannerTitle: String? = null,
    val kycRejectedBannerMessage: String? = null,
    val kybRequiredBannerTitle: String? = null,
    val kybRequiredBannerMessage: String? = null,
    val kybProcessedBannerTitle: String? = null,
    val kybProcessedBannerMessage: String? = null,
    val kybRejectedBannerTitle: String? = null,
    val kybRejectedBannerMessage: String? = null,
    val kycKybApprovedBannerTitle: String? = null,
    val kycKybApprovedBannerMessage: String? = null,
    val qrisDetailToastMessage: String? = null,
    val saldoTopupAdminFeesInfo: String? = null,
    val saldoTopupFeesInfo: String? = null
)
