package com.bukuwarung.payments.data.model

import com.google.gson.annotations.SerializedName
import java.math.BigDecimal

data class PaymentOverviewResponse(
        @SerializedName("received_amount")
        val amount: BigDecimal,
        @SerializedName("fee")
        val fee: Double?,
        @SerializedName("discount_fee")
        val discountFee: Double?,
        @SerializedName("discount_fee_text")
        val discountFeeText: String? = null,
        @SerializedName("total_transfer")
        private val totalTransfer: BigDecimal,
        @SerializedName("remaining_free_quota")
        val remainingFreeQuota: Int?,
        @SerializedName("saldo_cashback_amount")
        val saldoCashbackAmount: Double?,
        @SerializedName("destination_bank_information")
        val destinationBankInformation: DestinationBankInformation? = null,
        @SerializedName("loyalty")
        val loyaltyDiscount: LoyaltyDiscount? = null
)

