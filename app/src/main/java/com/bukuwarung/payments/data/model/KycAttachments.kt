package com.bukuwarung.payments.data.model

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class KycAttachments(
        @SerializedName("type")
        val type: String = "",
        @SerializedName("attachment_id")
        val attachmentId: String = "",
        @SerializedName("created_at")
        val createdAt: String? = null
) : Parcelable
