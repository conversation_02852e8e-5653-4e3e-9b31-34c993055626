package com.bukuwarung.payments.data.model

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class TrainPassenger(
    @SerializedName("name")
    val name: String? = null,
    @SerializedName("type")
    val type: String? = null,
    @SerializedName("id_number")
    val idNumber: String? = null,
    @SerializedName("seat")
    val seat: String? = null,
    val viewType: Int = 0
) : Parcelable