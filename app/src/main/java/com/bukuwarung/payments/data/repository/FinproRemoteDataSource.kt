package com.bukuwarung.payments.data.repository

import com.bukuwarung.data.restclient.ApiResponse
import com.bukuwarung.payments.core.model.PaymentOutStatusPollingResponse
import com.bukuwarung.payments.data.model.*
import com.bukuwarung.payments.data.model.ppob.*
import retrofit2.http.*

interface FinproRemoteDataSource {
    @PUT("{account_id}/orders/{order_id}/checkout")
    suspend fun checkoutOrder(@Path("account_id") accountId: String, @Path("order_id") orderId: String, @Body bankAccount: FinproCheckoutOrderRequest, @Header("TWOFA-AUTH-TOKEN") otpToken: String = ""): ApiResponse<FinproOrderResponse>

    @POST("{account_id}/orders/items")
    suspend fun addItemToCart(@Path("account_id") accountId: String, @Body request: FinproAddCartRequest): ApiResponse<FinproOrderResponse>

    @GET("ppob/products/v2")
    suspend fun getPpobProductsWithBillerDetails(
        @Query("category") category: String,
        @Query("filter") filter: String,
        @Query("is_special_product") isSpecialProduct: Boolean?
    ): ApiResponse<PpobProductsWithBillerDetails>

    @GET("payments/methods")
    suspend fun getPaymentMethods(@Query("amount") amount: Double, @Query("productCategory") productCategory: String, @Query("productCode") productCode: String): ApiResponse<List<FinproGetPaymentMethodsResponse>>

    @GET("payments/v2/methods")
    suspend fun getPaymentMethodsV2(@Query("amount") amount: Double, @Query("productCategory") productCategory: String, @Query("productCode") productCode: String): ApiResponse<FinproGetPaymentMethodsV2Response>

    @GET("{account_id}/orders/{order_id}")
    suspend fun getOrderDetail(@Path("account_id") accountId: String, @Path("order_id") orderId: String, @Query("ledger_account_id") ledgerAccountId: String?): ApiResponse<FinproOrderResponse>

    @GET("{account_id}/orders/{order_id}/poll")
    suspend fun getPaymentOutStatus(@Path("account_id") accountId: String, @Path("order_id") disbursementId: String) : ApiResponse<PaymentOutStatusPollingResponse>

    @GET("{account_id}/orders/finpro/{order_id}/poll")
    suspend fun getPpobStatus(@Path("account_id") accountId: String, @Path("order_id") orderId: String) : ApiResponse<PpobStatusPollingResponse>

    @PUT("flags/{account_id}/{flag_name}/update")
    suspend fun disableFavFlagOnMerchantLevel(
        @Path("account_id") accountId: String,
        @Path("flag_name") flagName: String,
        @Query("operation") operation: String
    ): ApiResponse<Any>

    @PUT("{account_id}/orders/{order_id}/flags/{flag_name}/update")
    suspend fun disableFavFlagOnTransactionLevel(
        @Path("account_id") accountId: String,
        @Path("order_id") orderId: String,
        @Path("flag_name") flagName: String,
        @Query("operation") operation: String
    ): ApiResponse<Any>

    @GET("v2/{account_id}/orders")
    suspend fun getOrders(
            @Path("account_id") accountId: String,
            @Query("customer_id") customerId: String? = null,
            @Query("start_date") startDate: String? = null,
            @Query("end_date") end_date: String? = null,
            @Query("type") type: List<String>? = null,
            @Query("status") status: List<String>? = null,
            @Query("page") page: Int? = null,
            @Query("limit") limit: Int? = null,
            @Query("biller_code") billerCode: String? = null,
            @Query("sort") sorting: String? = null,
    ): ApiResponse<List<PaymentHistory>>

    @GET("banners")
    suspend fun getPaymentBannerInfo(@Query("account_id") accountId: String): ApiResponse<List<PaymentBannerInfoResponse>>

    @GET("{account_id}/orders/months")
    suspend fun getHistoryMonths(
            @Path("account_id") accountId: String,
            @Query("customer_id") customerId: String
    ): ApiResponse<List<String>>

    @GET("{account_id}/orders/summaries")
    suspend fun getPaymentSummary(@Path("account_id") bookId: String): ApiResponse<PaymentSummaryResponse>

    @GET("saldo")
    suspend fun getSaldo(): ApiResponse<SaldoResponse>

    @GET("saldo/fees")
    suspend fun getSaldoAdminFee(): ApiResponse<SaldoAdminFeeResponse>

    @GET("saldo/topup")
    suspend fun getExistingSaldoTopup(@Query("status") status: String): ApiResponse<List<ExistingTopupSaldoResponse>>

    @POST("saldo/topup")
    suspend fun topupSaldo(@Body request: TopupSaldoRequest): ApiResponse<FinproOrderResponse>

    @POST("saldo")
    suspend fun activateSaldo(@Body request: ActivateSaldoRequest): ApiResponse<SaldoResponse>

    // ppob health check?
    @POST("health")
    suspend fun doHealthCheck(
            @Body paymentHealthCheckRequest: PaymentHealthCheckRequest
    ): ApiResponse<PaymentHealthCheckResponse>

    @GET("ppob/products/billers")
    suspend fun getBillers(@Query("category") category: String?): ApiResponse<List<Biller>>

    @POST("favourite/{bookId}/add")
    suspend fun addFavourite(@Path("bookId") bookId: String, @Body request: FavouriteRequest): ApiResponse<AddFavouriteResponse>

    @DELETE("favourite/{bookId}/remove/{favouriteId}")
    suspend fun deleteFavourite(@Path("bookId") bookId: String, @Path("favouriteId") favouriteId: String): ApiResponse<DeleteFavouriteResponse>

    @DELETE("{account_id}/orders/enroll/{order_id}/cancel")
    suspend fun cancelOrder(@Path("account_id") accountId: String, @Path("order_id") orderId: String)

    @GET("favourite/{bookId}/recents")
    suspend fun getRecentTransactions(
            @Path("bookId") bookId: String,
            @Query("category") category: String? = null,
            @Query("page") page: Int? = null,
            @Query("limit") limit: Int? = null
    ): ApiResponse<FavouriteResponse>

    @GET("favourite/{bookId}/recommendations/{orderId}")
    suspend fun getRecommendations(
        @Path("bookId") bookId: String,
        @Path("orderId") orderId: String,
        @Query("search") search: String,
        @Query("page") page: Int,
        @Query("count") count: Int
    ) : ApiResponse<FavouriteResponse>

    @GET("pricing-catalogue/filters")
    suspend fun getCategory(): ApiResponse<CategoryResponse>

    @GET("pricing-catalogue/pricing")
    suspend fun getBillersList(@Query("category") category: String? = null): ApiResponse<CatalogListResponse>

    @PUT("pricing-catalogue/add")
    suspend fun setSellingPrice(
        @Body request: SellingPriceRequest,
        @Query("category") category: String? = null,
        @Query("code") code: String? = null
    ): ApiResponse<Void>

    @GET("pricing-catalogue/banners")
    suspend fun getPromotionBannerFilter(): ApiResponse<PromotionBannerFilterResponse>

    @GET("pricing-catalogue/banners-data")
    suspend fun getPromotionBannerData(@Query("banner_type") sectionId: String? = null): ApiResponse<PromoBannerDataResponse>

    @PUT("payments/refund/{account_id}/addMethod")
    suspend fun addRefundBankAccount(@Path("account_id") accountId: String, @Body fniproRefunds: FinproRefunds): ApiResponse<FinproRefunds>

    @GET("payments/refund/{account_id}/methods")
    suspend fun getRefundMethods(@Path("account_id") accountId: String): ApiResponse<List<FinproRefunds>>

    @PUT("payments/refund/{account_id}/methods/set_selected")
    suspend fun setSelectedRefundBank(@Path("account_id") accountId: String, @Body finproRefunds: FinproRefunds) : ApiResponse<Boolean>

    @GET("favourite/{bookId}")
    suspend fun getFavourites(
        @Path("bookId") bookId: String,
        @Query("category") category: String,
        @Query("page") page: Int,
        @Query("count") count: Int
    ): ApiResponse<FavouriteResponse>

    @POST("{account_id}/orders/enroll")
    suspend fun getTrainTicketUrl(
        @Path("account_id") accountId: String,
        @Body request: TrainEnrollmentRequest
    ): ApiResponse<TrainEnrollmentResponse>

    @GET("{account_id}/orders/enroll/{enrollment_id}")
    suspend fun getTrainEnrollmentDetail(
        @Path("account_id") accountId: String,
        @Path("enrollment_id") enrollmentId: String
    ): ApiResponse<TrainEnrollmentDetailResponse>

}
