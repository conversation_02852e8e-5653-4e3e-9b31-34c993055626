package com.bukuwarung.payments.data.model

import android.os.Parcelable
import com.bukuwarung.payments.core.model.BankAccountDetail
import kotlinx.android.parcel.Parcelize


@Parcelize
data class PayOutFilledData(
    var bankAccountDetail: BankAccountDetail? = null,
    var amount: Long? = null,
    var category: PaymentCategoryItem? = null,
    var notes: String? = null,
    var timestamp: Long? = null
) : Parcelable