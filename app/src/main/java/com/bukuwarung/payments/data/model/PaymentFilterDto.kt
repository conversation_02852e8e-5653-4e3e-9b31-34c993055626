package com.bukuwarung.payments.data.model

import android.os.Parcelable
import com.bukuwarung.payments.constants.PpobConst
import kotlinx.android.parcel.Parcelize

@Parcelize
data class PaymentFilterDto(
    var typeFilters: ArrayList<String> = getListOfAllTypes(),
    var statusFilters: ArrayList<String> = getListOfAllStatus()
) : Parcelable {

    // to check whether the filter is using default schema
    fun isOriginal() = typeFilters.contains(TYPE_ALL) && statusFilters.contains(STATUS_ALL)

    companion object {
        const val TYPE_ALL = "ALL"
        const val TYPE_OUT = PaymentHistory.TYPE_PAYMENT_OUT
        const val TYPE_IN = PaymentHistory.TYPE_PAYMENT_IN
        const val TYPE_SALDO_IN = PaymentHistory.TYPE_SALDO_IN
        const val TYPE_SALDO_OUT = PaymentHistory.TYPE_SALDO_OUT
        const val TYPE_SALDO_REDEMPTION = PaymentHistory.TYPE_SALDO_REDEMPTION
        const val TYPE_SALDO_CASHBACK = PaymentHistory.TYPE_SALDO_CASHBACK
        const val TYPE_PULSA = PpobConst.CATEGORY_PULSA
        const val TYPE_PAKET_DATA = PpobConst.CATEGORY_PAKET_DATA
        const val TYPE_LISTRIK = PpobConst.CATEGORY_LISTRIK
        const val TYPE_EWALLET = PpobConst.CATEGORY_EWALLET
        const val TYPE_GAMING_VOUCHER = PpobConst.CATEGORY_VOUCHER_GAME
        const val TYPE_BPJS = PpobConst.CATEGORY_BPJS
        const val TYPE_PDAM = PpobConst.CATEGORY_PDAM
        const val TYPE_MULTIFINANCE = PpobConst.CATEGORY_MULTIFINANCE
        const val TYPE_INTERNET_DAN_TV_CABLE = PpobConst.CATEGORY_INTERNET_DAN_TV_CABLE
        const val TYPE_VEHICLE_TAX = PpobConst.CATEGORY_VEHICLE_TAX

        const val STATUS_ALL = "ALL"
        const val STATUS_COMPLETED = PaymentHistory.STATUS_COMPLETED
        const val STATUS_PENDING = PaymentHistory.STATUS_PENDING
        const val STATUS_FAILED = PaymentHistory.STATUS_FAILED
        const val STATUS_EXPIRED = PaymentHistory.STATUS_EXPIRED
        const val STATUS_HOLD = PaymentHistory.STATUS_HOLD
    }
}

fun getListOfAllTypes(): ArrayList<String> {
    val list = getListOfTypesWoAll()
    list.add(0, PaymentFilterDto.TYPE_ALL)
    return list
}

fun getListOfTypesWoAll() = arrayListOf(
    PaymentFilterDto.TYPE_OUT, PaymentFilterDto.TYPE_IN,
    PaymentFilterDto.TYPE_SALDO_IN, PaymentFilterDto.TYPE_SALDO_OUT,
    PaymentFilterDto.TYPE_SALDO_REDEMPTION,
    PaymentFilterDto.TYPE_PULSA,
    PaymentFilterDto.TYPE_PAKET_DATA,
    PaymentFilterDto.TYPE_LISTRIK,
    PaymentFilterDto.TYPE_EWALLET,
    PaymentFilterDto.TYPE_GAMING_VOUCHER,
    PaymentFilterDto.TYPE_BPJS,
    PaymentFilterDto.TYPE_PDAM,
    PaymentFilterDto.TYPE_MULTIFINANCE,
    PaymentFilterDto.TYPE_INTERNET_DAN_TV_CABLE,
    PaymentFilterDto.TYPE_VEHICLE_TAX
)

fun getListOfAllStatus(): ArrayList<String> {
    val list = getListOfStatusWoAll()
    list.add(0, PaymentFilterDto.STATUS_ALL)
    return list
}

fun getListOfStatusWoAll() = arrayListOf(
    PaymentFilterDto.STATUS_COMPLETED, PaymentFilterDto.STATUS_PENDING,
    PaymentFilterDto.STATUS_FAILED, PaymentFilterDto.STATUS_EXPIRED, PaymentFilterDto.STATUS_HOLD
)