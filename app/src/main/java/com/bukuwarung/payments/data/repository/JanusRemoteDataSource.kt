package com.bukuwarung.payments.data.repository

import com.bukuwarung.data.restclient.ApiResponse
import com.bukuwarung.payments.data.model.KycStatusResponse
import retrofit2.http.*

interface JanusRemoteDataSource {

    @POST("accounts")
    suspend fun createKycAccount(): ApiResponse<KycStatusResponse>

    @GET("accounts/{account_id}")
    suspend fun getKycAccount(
            @Path("account_id") accountId: String
    ): ApiResponse<KycStatusResponse>
}
