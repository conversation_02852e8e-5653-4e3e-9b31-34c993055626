package com.bukuwarung.payments.data.model

import android.os.Parcelable
import androidx.annotation.Keep
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Keep
@Parcelize
data class PaymentCollectionCashTransactionInfo(
    @SerializedName("transaction_id")
    val transactionId: String,
    @SerializedName("status")
    val status: String,
    @SerializedName("amount")
    val amount: Double? = null
) : Parcelable