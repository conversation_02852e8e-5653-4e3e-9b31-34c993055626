package com.bukuwarung.payments.data.model.ppob

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class CatalogListResponse(

	@SerializedName("pricing_type")
	val pricingType: PricingType? = null,

	@SerializedName("flags")
	val flags: Flags? = null,

	@SerializedName("billers")
	val billers: List<BillersItem>? = null,

	@SerializedName("step_change")
	val stepChange: Double? = null,

	@SerializedName("min_admin_fee")
	val minAdminFee: Double? = null,

	@SerializedName("max_admin_fee")
	val maxAdminFee: Double? = null
) : Parcelable

@Parcelize
data class Flags(

	@SerializedName("SHOW_ADMIN_FEE")
	val showAdminFee: Boolean? = null,

	@SerializedName("USE_BILLER_COLLAPSE")
	val useBillerCollapse: Boolean? = null,

	@SerializedName("IS_READY_FOR_EXPORT")
	val isReadyForExport: Boolean? = null,

	@SerializedName("DIRECT_INPUT_BOX")
	val directInputBox: Boolean? = null
) : Parcelable

@Parcelize
data class BillersItem(

	@SerializedName("biller_icon")
	val billerIcon: String? = null,

	@SerializedName("biller_code")
	val billerCode: String? = null,

	@SerializedName("biller_display_name")
	val billerDisplayName: String? = null,

	@SerializedName("products")
	val products: List<ProductsItem>? = null
) : Parcelable

@Parcelize
data class ProductsItem(

	@SerializedName("admin_fee")
	val adminFee: Double? = null,

	@SerializedName("price")
	val price: Double? = null,

	@SerializedName("capital_price")
	val capitalPrice: Double? = null,

	@SerializedName("discount")
	val discount: Double? = null,

	@SerializedName("product_code")
	val productCode: String? = null,

	@SerializedName("is_default")
	val isDefault: Boolean? = null,

	@SerializedName("product_name")
	val productName: String? = null,

	@SerializedName("pricing")
	val pricing: Double? = null,

	@SerializedName("discounted_fee")
	val discountedFee: Double? = null
) : Parcelable {
	override fun equals(other: Any?): Boolean {
		return other is ProductsItem && other.productCode.isNullOrBlank().not() && other.productCode == productCode
	}

	override fun hashCode(): Int {
		return productCode?.hashCode() ?: 0
	}
}