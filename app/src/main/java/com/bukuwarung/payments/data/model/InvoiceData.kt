package com.bukuwarung.payments.data.model

import android.view.View


data class InvoiceItem(
    val type: InvoiceItemType? = InvoiceItemType.ITEM_DETAIL,
    val label: String? = null,
    val value: String? = null,
    val hideIfMissing: Boolean? = false,
    val labelTextDecor: TextDecor? = null,
    val valueTextDecor: TextDecor? = null,
    val margins: Margin = Margin(top = 4),
    val heading: String? = null,
    val viewTag: String? = null,
    val viewVisibility: Int = View.VISIBLE
)

data class TextDecor(
    val textColor: Int? = null,
    val textStyle: TextStyle? = null,
    val fontSize: Int? = null,
)

data class Margin(
    val left: Int? = 0,
    val top: Int? = 0,
    val right: Int? = 0,
    val bottom: Int? = 0,
)

enum class TextStyle {
    BOLD, ITALIC
}

enum class InvoiceItemType {
    ITEM_DETAIL, DIVIDER, CODE_INFO, NOTES, PAYMENT_CODE_INFO
}