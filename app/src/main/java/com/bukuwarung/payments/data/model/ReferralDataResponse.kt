package com.bukuwarung.payments.data.model

import com.google.gson.annotations.SerializedName

data class ReferralDataResponse(

	@SerializedName("result")
	val result: Boolean? = null,

	@SerializedName("data")
	val data: List<DataItem?>? = null
)

data class DataItem(

	@SerializedName("pointsL1Referrer")
	val pointsL1Referrer: Int? = null,

	@SerializedName("userReferralCode")
	val userReferralCode: String? = null,

	@SerializedName("referredAt")
	val referredAt: String? = null,

	@SerializedName("levelScoreL1Referrer")
	val levelScoreL1Referrer: Int? = null,

	@SerializedName("userName")
	val userName: String? = null,

	@SerializedName("userId")
	val userId: String? = null,

	@SerializedName("levelScoreL2ReferrerSum")
	val levelScoreL2ReferrerSum: Int? = null
)
