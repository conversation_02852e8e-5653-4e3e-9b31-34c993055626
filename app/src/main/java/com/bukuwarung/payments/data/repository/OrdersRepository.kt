package com.bukuwarung.payments.data.repository

import com.bukuwarung.data.restclient.ApiResponse
import com.bukuwarung.data.session.SessionRemoteRepository
import com.bukuwarung.payments.data.model.PaymentHistory
import com.bukuwarung.payments.data.model.TransactionServiceFeatureFlag
import javax.inject.Inject


open class OrdersRepository @Inject constructor(
    val remoteDataSource: OrdersRemoteDataSource,
    paymentRemoteDataSource: PaymentsRemoteDataSource,
    sessionRemoteRepository: SessionRemoteRepository,
) : OrdersRemoteRepository,
    PaymentsBaseRepository(paymentRemoteDataSource, sessionRemoteRepository) {

    override suspend fun getOrders(
        accountId: String,
        customerId: String?,
        startDate: String?,
        endDate: String?,
        type: List<String>?,
        status: List<String>?,
        page: Int?,
        limit: Int?, billerCode: String?,
        sorting: String?,
        searchQuery: String?,
        bukuOrigin: String?
    ) = callWithEnablePaymentCheck {
        val searchQueryString = if (searchQuery.isNullOrEmpty()) null else searchQuery
        remoteDataSource.getOrders(
            accountId, customerId, startDate, endDate,
            type, status, page, limit, billerCode, sorting, searchQueryString, bukuOrigin
        )
    }

    override suspend fun getFeatureFlag(): ApiResponse<TransactionServiceFeatureFlag> =
        callWithEnablePaymentCheck {
            remoteDataSource.getFeatureFlag()
        }

    override suspend fun getLinkedOrders(orderId: String) =
        remoteDataSource.getLinkedOrders(orderId)
}

interface OrdersRemoteRepository {
    suspend fun getOrders(
        accountId: String,
        customerId: String? = null,
        startDate: String? = null,
        endDate: String? = null,
        type: List<String>? = null,
        status: List<String>? = null,
        page: Int? = null,
        limit: Int? = null,
        billerCode: String? = null,
        sorting: String? = null,
        searchQuery: String? = null,
        bukuOrigin: String?
    ): ApiResponse<List<PaymentHistory>>

    suspend fun getFeatureFlag(): ApiResponse<TransactionServiceFeatureFlag>

    suspend fun getLinkedOrders(orderId: String): ApiResponse<List<PaymentHistory>>
}
