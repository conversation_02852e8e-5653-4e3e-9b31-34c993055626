package com.bukuwarung.payments.data.model

import android.os.Parcelable
import androidx.annotation.Keep
import com.bukuwarung.payments.constants.PpobConst
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Keep
@Parcelize
data class FinproError(
        @SerializedName("code")
        val code: Int? = null,
        @SerializedName("message")
        val message: String? = null
) : Parcelable {
        fun isBillAlreadyPaid() = code == PpobConst.BILL_ALREADY_PAID_CODE
}
