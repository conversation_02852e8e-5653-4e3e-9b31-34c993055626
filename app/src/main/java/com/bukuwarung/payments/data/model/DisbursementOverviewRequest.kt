package com.bukuwarung.payments.data.model

import com.google.gson.annotations.SerializedName
import java.math.BigDecimal


data class DisbursementOverviewRequest(
        @SerializedName("amount")
        val amount: BigDecimal,

        @SerializedName("description")
        val description: String,

        @SerializedName("va_bank_code")
        val vaBankCode: String?,

        @SerializedName("customer_bank_account_id")
        val customerBankAccountId: String?,

        @SerializedName("customer_name")
        val customerName: String,

        val accountId: String,
        val customerId: String,

        @SerializedName("extras")
        val extras: PaymentExtras? = null,

        @SerializedName("payment_category_id")
        val paymentCategoryId: String? = null,

        @SerializedName("total_transfer")
        val totalTransfer: BigDecimal? = null,

        @SerializedName("game_name")
        val gameName: String? = null
)
