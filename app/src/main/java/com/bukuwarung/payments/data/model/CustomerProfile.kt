package com.bukuwarung.payments.data.model

import android.os.Parcelable
import com.bukuwarung.payments.data.model.ppob.FavouriteDetail
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class CustomerProfile(
        @SerializedName("favorite_details")
        var favouriteDetails: FavouriteDetail? = null,
        @SerializedName("is_favorite")
        var isFavorite: Boolean? = null,
        @SerializedName("details")
        val details: Details? = null,
        @SerializedName("category")
        val category: String? = null
) : Parcelable