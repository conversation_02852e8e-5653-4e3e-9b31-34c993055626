package com.bukuwarung.payments.data.model

import android.os.Parcelable
import com.bukuwarung.database.entity.RefundBankAccount
import com.bukuwarung.utils.Utility
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize
import kotlinx.android.parcel.RawValue
import java.io.Serializable
import java.math.BigDecimal

@Parcelize
data class Disbursement(
        @SerializedName("disbursement_id")
        val disbursementId: String,

        @SerializedName("customer_id")
        val customerId: String,

        @SerializedName("status")
        val status: String,

        @SerializedName("amount")
        val amount: BigDecimal,

        @SerializedName("fee")
        val fee: Float = 0.0f,

        @SerializedName("discount")
        val discount: Float = 0.0f,

        @SerializedName("virtual_account")
        val virtualAccount: VirtualAccount,

        @SerializedName("invoice_url")
        val invoiceUrl: String?,

        @SerializedName("share_url_template")
        val shareUrlTemplate: String? = null,

        @SerializedName("payment_instruction_url")
        val paymentInstructionsUrl: String?,

        @SerializedName("checkout_url")
        val checkoutUrl: String?,

        @SerializedName("receiver_bank")
        val receiverBank: ReceiverBank? = null,

        @SerializedName("transaction_id")
        val transactionId: String? = null,

        @SerializedName("description")
        val description: String? = null,

        @SerializedName("payment_method")
        val paymentMethod: String? = null,

        @SerializedName("payment_channel")
        val paymentChannel: String? = null,

        @SerializedName("agent_fee")
        val agentFeeInfo: @RawValue AgentFeeInfo? = null,

        @SerializedName("transaction_type")
        val transactionType: String? = null,

        @SerializedName("payment_category")
        val paymentCategory: PaymentCategoryItem? = null,

        @SerializedName("progress")
        val progress: List<PaymentProgress>,

        @SerializedName("bankAccount")
        val refundBankAccount: DisbursementRefundAccount?

) : Parcelable {
        fun isPaid() = status.equals(PaymentHistory.STATUS_PAID, ignoreCase = true)
        fun isCompleted() = status.equals(PaymentHistory.STATUS_COMPLETED, ignoreCase = true)

        fun getFormattedTotal(): String? = Utility.formatAmount(amount.toDouble())

}

@Parcelize
data class DisbursementRefundAccount(
        @SerializedName("account")
        val account: AccountDetail? = null,
        @SerializedName("bankCode")
        val bankCode: String? = "",
        @SerializedName("accountNumber")
        val accountNumber: String? = "",
        @SerializedName("customerId")
        val customerId: String? = "",
        @SerializedName("ownerName")
        val ownerName: String? = "",
        @SerializedName("isRefundableBank")
        val isRefundableBank: Boolean? = true
) : Parcelable

@Parcelize
data class AccountDetail(
        @SerializedName("id")
        val id: AccountId? = null
) : Parcelable

@Parcelize
data class AccountId(
        @SerializedName("value")
        val value: String? = ""
) : Parcelable