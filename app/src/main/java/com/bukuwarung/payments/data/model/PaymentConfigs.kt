package com.bukuwarung.payments.data.model

import android.os.Parcelable
import com.bukuwarung.BuildConfig
import com.bukuwarung.constants.AppConst
import com.bukuwarung.constants.PaymentConst
import com.bukuwarung.lib.webview.data.PrivyCredentials
import com.bukuwarung.lib.webview.util.Constant
import com.bukuwarung.lib.webview.util.FileSizeLimits
import com.bukuwarung.lib.webview.util.UploadsValidations
import com.google.android.gms.maps.model.LatLng
import kotlinx.android.parcel.Parcelize

data class PaymentConfigs(
    val disbursalMaxRetryAttempts: Int = 2,
    val showBnplEntrypoint: Boolean = false,
    val visibleCategoryCount: Int = 3,
    val qrisBankChangeAllowedCount: Int? = null,
    val qrisBankChangeAllowedDays: Int? = null,
    val shouldShowQrisBankWarning: Boolean = false,
    val shouldCheckLuminosity: Boolean = false,
    val minRequiredLuminosity: Float = 0f,
    val photoCompressionSize: Float = 1536f,
    val minQrisAmountForRetry: Double? = 0.0,
    val qrisBatchDisbursementTime: String? = null,
    val qrisChargingAmount: String? = "",
    val qrisProcessingTimeFAQ: String? = null,
    val customCalendarMaxRange:Int? = PaymentConst.DEFAULT_FILTER_CALENDAR_MAX_RANGE,
    val hideBiayaAdminForQris: Boolean? = true,
    val fileSizeLimits: FileSizeLimits? = null,
    val performImageValidations: Boolean? = false,
    val uploadsValidations: UploadsValidations? = null,
    val imageQuality: Int? = null,
    val videoQuality: String? = null,
    val compressionQuality: String? = null,
    val receiverChannelWarnings: List<ReceiverWarning>? = null,
    val kycTierConfig: KycTierConfig? = null,
    val enableNameMatching: Boolean? = false,
    val enableKyb: Boolean? = false,
    val enableKybAlerts: Boolean? = false,
    val enablePaymentKyb: Boolean? = false,
    val isQrisDiscontinued: Boolean? = false,
    val livenessVendor: Constant.LivenessVendor? = Constant.LivenessVendor.VIDA,
    val bankingApi: String = BuildConfig.API_BASE_URL_BANKING,
    val riskApi: String = BuildConfig.API_BASE_URL_RISK,
    val janusApi: String = BuildConfig.API_BASE_URL_JANUS,
    val paymentApi: String = BuildConfig.API_BASE_URL_PAYMENT,
    val finProApi: String = BuildConfig.API_BASE_URL_FINPRO,
    val transactionsApi: String = BuildConfig.API_BASE_URL_TRANSACTIONS,
    val forgotPinUrl: String = BuildConfig.FORGOT_PIN_URL,
    val kycWebUrl: String = BuildConfig.KYC_WEB_URL,
    val kycDocsUrl: String = BuildConfig.KYC_DOCS_URL,
    val accountVerificationUrl: String = BuildConfig.KYC_WEB_URL,
    val qrisWebUrl: String = BuildConfig.QRIS_WEB_URL,
    val qrisWebUrlMatching: String = BuildConfig.QRIS_WEB_URL,
    val qrisWebUrlV3: String = BuildConfig.QRIS_WEB_URL,
    val qrisFormUrl: String = BuildConfig.QRIS_FORM_URL,
    val qrisFormUrlMatching: String = BuildConfig.QRIS_FORM_URL,
    val qrisFormUrlV3: String = BuildConfig.QRIS_FORM_URL,
    val merchantBanksUrl: String = "${BuildConfig.API_BASE_URL}/mx-mweb/edc/landing/registration/connect-bank?from=android",
    val edcRegistrationUrl: String = "${BuildConfig.API_BASE_URL}/mx-mweb/edc/landing/registration?step=1&readonly=true",
    val lendingFormUrl: String = "${BuildConfig.API_BASE_URL}/los-web/clf?page=Personal_Information",
    val bnplFormUrl: String = "${BuildConfig.API_BASE_URL}/los-web/bnpl-forms",
    val ppobBnplFormUrl: String = "${BuildConfig.API_BASE_URL}/los-web/bnpl/forms/ppob-bnpl?form=1",
    val supportUrls: PaymentSupportUrls = PaymentSupportUrls(),
    val kycTierInfoUrl: String = AppConst.KYC_TIER_INFO_URL,
    val aboutKycUrl: String = AppConst.ABOUT_ACCOUNT_VERIFICATION,
    val qrisBankUrl: String = BuildConfig.QRIS_BANK_URL,
    val appealFlowUrl: String = BuildConfig.APPEAL_FLOW_URL,
    val matchingInfoUrl: String = AppConst.FAQ_MATCHING_INFO_URL,
    val faqUsedAccountUrl: String = AppConst.FAQ_USED_ACCOUNT_BW_URL,
    val faqBlockedAccountUrl: String = AppConst.FAQ_BLOCKED_ACCOUNT_BW_URL,
    val saldoBonusUrl: String = AppConst.SALDO_BONUS_URL,
    val privyCredentials: PrivyCredentials? = null,
    val kycRedirectionData: HashMap<String, String>? = null,
    val qrisCoachMarks: List<QrisCoachMarks>? = null,
    val qrisFAQs: List<QrisFAQ>? = null,
    val historyFilters: List<DateFilter> = arrayListOf(),
    val historyFiltersNew: Filters? = null,
    val kybMandatoryFromDate: String? = null,
    val kybVerificationMaxDays: Int? = null,
    val mapConfig: MapConfig? = null,
    val paginationConfig: PaginationConfig? = null,
    val shouldEnableKybMapFlow: Boolean = true,
    val deactivatedQrisGroupCode: String? = null,
    val whitelistedUserLimitInfo: String? = null,
    val kybUpgradeInfo: KybUpgradeInfo? = null,
    val paymentOutPollingConfig: PaymentOutPollingConfig? = null,
    val qrisAlertConfig: QrisAlertConfig? = null,
    val favouritesPaginationLimit: Int? = 10,
    val recentsPaginationLimit: Int? = 10,
    val reKycRedirectUrl: String = "${BuildConfig.API_BASE_URL}/los-web/product-details/external/community-loan?statusKyc=RE_KYC_PENDING",
)
data class KybUpgradeInfo(
    val text: String? = null,
    val highlightedText: String? = null
)

data class QrisAlertConfig(
    val qrisAlertMessage: String? = null,
    val qrisAlertButtonText: String? = null,
    val qrisAlertButtonRedirection: String? = null,
)

data class PaymentOutPollingConfig(
    val paymentOutStatusPollingIntervalSeconds: Long,
    val paymentOutStatusPollingTotalTimeSeconds: Long
)

data class ReceiverWarning(
    val applicableBankCodes: List<String>? = null,
    val applicableBankCodesExcept: List<String>? = null,
    val paymentTypes: List<String>? = null,
    val minAmount: Double? = 0.0,
    val maxAmount: Double? = null,
    val message: String? = "",
    val title: String? = ""
)

data class Filters(
    val all: FilterValues,
    val ppob: FilterValues,
    val pembayaran: FilterValues,
    val saldo: FilterValues,
    val cashback: FilterValues,
)

data class FilterValues(
    val status: ArrayList<FilterSection>,
    val products: ArrayList<FilterSection>,
    val date: ArrayList<DateFilter>,
    val sort: ArrayList<SortOption>,
)

data class FilterSection(
    val sectionLabel: String,
    val filters: ArrayList<Filter>
)

data class Filter(
    val key: String,
    val label: String,
    var isChecked: Boolean = false,
)

data class DateFilter(
    val label: String,
    val presetValue: PaymentConst.DATE_PRESET?,
    val endDays: Int? = null,
    val startDays: Int? = null,
    var isChecked: Boolean = false,
    var startDate: Long? = null,
    var endDate: Long? = null
)

data class QrisCoachMarks(
    val title: String? = "",
    val message: String? = "",
    val id: String? = "",
    val index: Int? = 1
)

data class QrisFAQ(
    val title: String? = "",
    val message: String? = ""
)

data class SortOption(
    val key: String,
    val label: String,
    var isChecked: Boolean = false,
)

data class KycTierConfig(
    val featuresAllowedForNonKyc: List<String>? = null,
    val featuresAllowedForKycAdvanced: List<String>? = null,
    val featuresAllowedForKycSupreme: List<String>? = null,
)

data class PaginationConfig(
    val limitPerPage: Int? = 20,
    val debounceDelay: Long? = 1000
)

@Parcelize
data class MapConfig(
    var interval: Long? = null,
    val fastestInterval: Long? = null,
    val maxWaitTime: Long? = null,
    val zoom: Float? = null,
    val defaultLocation: LatLng? = null,
    val priority: Int? = null
): Parcelable


data class PaymentSupportUrls(
    val payments: String = AppConst.PAYMENT_ASSIST_URL,
    val liveness: String = AppConst.ZOHODESK_HOME_URL
)