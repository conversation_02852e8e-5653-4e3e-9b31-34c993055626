package com.bukuwarung.payments.data.model

import com.google.gson.annotations.SerializedName


data class QrisDashboardResponse(
    @SerializedName("disbursed_payments")
    val disbursedPayments: PaymentDetails?,
    @SerializedName("to_be_disbursed_payments")
    val toBeDisbursedPayments: PaymentDetails?,
    @SerializedName("total_incoming_payments")
    val totalIncomingPayments: TotalIncomingPayments?

)

data class PaymentDetails(
    @SerializedName("transaction_count")
    val transactionCount: Int?,
    @SerializedName("transaction_amount")
    val transactionAmount: Double?,
    @SerializedName("percentage")
    val percentage: Double?
)

data class TotalIncomingPayments(
    @SerializedName("transaction_count")
    val transactionCount: Int?,
    @SerializedName("transaction_amount")
    val transactionAmount: Double?
)