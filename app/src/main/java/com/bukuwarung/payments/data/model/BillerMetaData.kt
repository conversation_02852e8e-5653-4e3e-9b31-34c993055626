package com.bukuwarung.payments.data.model

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class BillerMetaData(
    @SerializedName("required_parameters")
    val requriedParametersList: List<BillerRequiredParameters>? = null,
    @SerializedName("icon")
    val icon: String? = null,
    @SerializedName("extra_data")
    val extraData: BillerExtraData? = null,
    @SerializedName("type")
    val type: String?
) : Parcelable
