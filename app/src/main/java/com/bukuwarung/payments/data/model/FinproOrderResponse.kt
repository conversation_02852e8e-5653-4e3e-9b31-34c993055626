package com.bukuwarung.payments.data.model

import android.os.Parcelable
import com.bukuwarung.database.entity.RefundBankAccount
import com.bukuwarung.utils.orNil
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class FinproOrderResponse(
        @SerializedName("order_id")
        val orderId: String? = null,
        @SerializedName("transaction_id")
        val transactionId: String? = null,
        @SerializedName("status")
        val status: String? = null,
        @SerializedName("amount")
        val amount: Double? = null,
        @SerializedName("fee")
        val fee: Double? = null,
        @SerializedName("description")
        val description: String? = null,
        @SerializedName("metadata")
        val metadata: PpobMetaData? = null,
        @SerializedName("items")
        val items: List<PpobProductDetail>? = null,
        @SerializedName("payments")
        val payments: List<FinproPayments?>? = null,
        @SerializedName("created_at")
        val createdAt: String? = null,
        @SerializedName("updated_at")
        val updatedAt: String? = null,
        @SerializedName("progress")
        val progress: List<PaymentProgress>? = null,
        @SerializedName("customer")
        val customer: PaymentCustomer? = null,
        @SerializedName("transaction_type")
        val transactionType: String? = null,
        @SerializedName("type")
        val type: String? = null,
        @SerializedName("payment_category")
        var paymentCategory: PaymentCategoryItem? = null,
        @SerializedName("agent_fee")
        var agentFeeInfo: AgentFeeInfo? = null,
        @SerializedName("available_refund_methods")
        val refunds: List<FinproRefunds?>? = null,
        @SerializedName("payment_collection")
        val paymentCollectionInfo: PaymentCollectionInfo? = null,
        @SerializedName("purchase_type")
        val purchaseType: String? = null,
        @SerializedName("channel_pending_eta")
        val channelPendingEta: String? = null,
        @SerializedName("share_url_template")
        val shareUrlTemplate: String? = null,
        @SerializedName("template")
        val template: String? = null,
        @SerializedName("customer_profile")
        val customerProfile: CustomerProfile? = null,
        @SerializedName("invalid_disbursable_accounts")
        val invalidDisburableAccounts: List<String>? = null,
        @SerializedName("campaigns")
        val campaigns: List<Campaign>? = null,
        @SerializedName("cost")
        val cost: Double? = null,
        @SerializedName("total_discount")
        val totalDiscount: Double? = null,
        @SerializedName("total_bonus")
        val totalBonus: Double? = null,
        @SerializedName("flags")
        var flags: Map<String, Boolean>? = null,
        @SerializedName("error")
        var finproError: FinproError? = null,
        @SerializedName("kyb_status")
        var kybStatus: String? = null,
        @SerializedName("used_refund_bank")
        var usedRefundBank: RefundBankAccount? = null,
        @SerializedName("loyalty")
        val loyalty: LoyaltyDiscount? = null
        ) : Parcelable {
        fun isPaid() = status?.equals(PaymentHistory.STATUS_PAID, ignoreCase = true)?:false
        fun isCompleted() = status?.equals(PaymentHistory.STATUS_COMPLETED, ignoreCase = true)?:false
        fun isFailed() = status?.equals(PaymentHistory.STATUS_FAILED, ignoreCase = true)?:false
        fun isPpob() = PaymentHistory.TYPE_PAYMENT_IN != items?.firstOrNull()?.sku && PaymentHistory.TYPE_PAYMENT_OUT != items?.firstOrNull()?.sku
}

fun FinproOrderResponse.getCompletedStatusDate(): String? {
        return if (isCompleted() && progress?.size.orNil != 5) {
                progress?.firstOrNull { it.state == PaymentHistory.STATUS_COMPLETED }
                        ?.getFormattedTimestamp()
        } else null
}