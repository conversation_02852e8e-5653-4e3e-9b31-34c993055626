package com.bukuwarung.payments.data.repository

import com.bukuwarung.activities.businessdashboard.model.PaymentCategoriesHistoryResponse
import com.bukuwarung.activities.businessdashboard.model.PpobProducts
import com.bukuwarung.activities.businessdashboard.model.TotalPayment
import com.bukuwarung.data.restclient.ApiEmptyResponse
import com.bukuwarung.data.restclient.ApiResponse
import com.bukuwarung.data.restclient.ApiSuccessResponse
import com.bukuwarung.data.session.SessionRemoteRepository
import com.bukuwarung.database.entity.Bank
import com.bukuwarung.database.entity.BankAccount
import com.bukuwarung.database.repository.BusinessRepository
import com.bukuwarung.payments.core.model.*
import com.bukuwarung.payments.data.model.*
import com.bukuwarung.payments.data.model.ppob.FirstPaymentTransaction
import com.bukuwarung.session.SessionManager
import com.bukuwarung.session.User

class PaymentsRepository(
    val remoteDataSource: PaymentsRemoteDataSource,
    sessionRemoteRepository: SessionRemoteRepository,
    private val bankAccountLocalDataStore: BankAccountLocalDataStore? = null,
    val bankingRemoteDataSource: BankingRemoteDataSource? = null,
    val businessRepository: BusinessRepository? = null
) : PaymentRemoteRepository, PaymentsBaseRepository(remoteDataSource, sessionRemoteRepository) {

    override suspend fun getMetadata(): ApiResponse<PaymentMetadata> {
        return remoteDataSource.getMetadata()
    }

    override suspend fun getMerchantBankAccounts(
        accountId: String, type: String?
    ) = callWithEnablePaymentCheck {
        val response = remoteDataSource.getMerchantBankAccounts(accountId, type)
        if (response is ApiSuccessResponse) {
            bankAccountLocalDataStore?.deleteMerchantBankAccounts(accountId)
            bankAccountLocalDataStore?.insertBankAccounts(response.body)
        }
        return@callWithEnablePaymentCheck response
    }

    override suspend fun updateBusinessName(businessName: String): ApiResponse<Unit> {
        val response = remoteDataSource.updateBusinessName(
            User.getBusinessId(),
            mapOf(Pair("business_name", businessName))
        )
        if (response is ApiSuccessResponse) {
            val bookEntity =
                businessRepository?.getBusinessByIdSync(SessionManager.getInstance().businessId)
            bookEntity?.businessName = businessName
            businessRepository?.insertBookSync(bookEntity)
        }
        return response
    }

    override suspend fun validateBankAccount(accountId: String, bankValidationRequest: BankValidationRequest) = callWithEnablePaymentCheck { remoteDataSource.validateBankAccount(accountId, bankValidationRequest) }

    override suspend fun validateAndAddCustomerBankAccount(
        accountId: String,
        customerId: String,
        bankValidationRequest: BankValidationRequest
    ) = callWithEnablePaymentCheck {
        remoteDataSource.validateAndAddCustomerBankAccount(
            accountId,
            customerId,
            bankValidationRequest
        )
    }

    override suspend fun addMerchantBankAccount(accountId: String, bankAccount: BankAccountRequest): ApiResponse<BankAccount> {
        val response = callWithEnablePaymentCheck { remoteDataSource.addMerchantBankAccount(accountId, bankAccount) }
        if (response is ApiSuccessResponse) {
            bankAccountLocalDataStore?.insertBankAccounts(listOf(response.body))
        }
        return response
    }

    override suspend fun deleteMerchantBankAccount(accountId: String, bankAccountId: String) = callWithEnablePaymentCheck { remoteDataSource.deleteMerchantBankAccount(accountId, bankAccountId) }

    override suspend fun getCustomerBankAccounts(accountId: String, customerId: String) = callWithEnablePaymentCheck { remoteDataSource.getCustomerBankAccounts(accountId, customerId) }

    override suspend fun addCustomerBankAccounts(
        accountId: String, customerId: String, bankAccount: BankAccount
    ): ApiResponse<BankAccount> {
        val response = callWithEnablePaymentCheck {
            remoteDataSource.addCustomerBankAccount(accountId, customerId, bankAccount)
        }
        if (response is ApiSuccessResponse) {
            bankAccountLocalDataStore?.insertBankAccounts(listOf(response.body))
        }
        return response
    }

    override suspend fun deleteCustomerBankAccount(
        accountId: String, customerId: String, bankAccountId: String
    ): ApiResponse<Any> {
        val response = callWithEnablePaymentCheck {
            remoteDataSource.deleteCustomerBankAccount(accountId, customerId, bankAccountId)
        }
        if (response is ApiSuccessResponse || response is ApiEmptyResponse) {
            bankAccountLocalDataStore?.deleteCustomerBankAccount(accountId, customerId, bankAccountId)
        }
        return response
    }

    override suspend fun requestPayment(accountId: String, customerId: String, paymentCollection: PaymentCollection) = callWithEnablePaymentCheck { remoteDataSource.requestPayment(accountId, customerId, paymentCollection) }

    override suspend fun getRequestedPayment(accountId: String, customerId: String, requestId: String) = callWithEnablePaymentCheck { remoteDataSource.getRequestedPayment(accountId, customerId, requestId) }

    override suspend fun getDisbursementOverview(accountId: String, customerId: String, request: DisbursementOverviewRequest) = callWithEnablePaymentCheck { remoteDataSource.getDisbursementOverview(accountId, customerId, request) }

    override suspend fun createDisbursement(accountId: String, customerId: String, request: DisbursementOverviewRequest) = callWithEnablePaymentCheck { remoteDataSource.createDisbursement(accountId, customerId, request) }

    override suspend fun getPaymentOverview(accountId: String, customerId: String, request: PaymentOverviewRequest) = callWithEnablePaymentCheck { remoteDataSource.getPaymentInOverview(accountId, customerId, request) }

    override suspend fun getDisbursement(accountId: String, customerId: String, requestId: String) = callWithEnablePaymentCheck { remoteDataSource.getDisbursement(accountId, customerId, requestId) }

    override suspend fun getBanks(type: String?) = callWithEnablePaymentCheck { remoteDataSource.getBanks(type) }

    override suspend fun getVirtualAccountBanks() = callWithEnablePaymentCheck { remoteDataSource.getVirtualAccountBanks() }

    override suspend fun doHealthCheck(paymentHealthCheckRequest: PaymentHealthCheckRequest) = callWithEnablePaymentCheck { remoteDataSource.doHealthCheck(paymentHealthCheckRequest) }

    override suspend fun expirePayment(bookId: String, customerId: String, paymentId: String) = remoteDataSource.expirePayment(bookId, customerId, paymentId)



    //TODO: MOVE TO API
    override fun getBanksLocal() = Bank.BANKS

    override suspend fun updatePaymentInAgentFee(
        businessId: String, customerId: String,
        paymentRequestId: String, updateFeeRequest: UpdateFeeRequest
    ) = remoteDataSource.updatePaymentInAgentFee(
        businessId, customerId, paymentRequestId, updateFeeRequest
    )

    override suspend fun updatePaymentOutAgentFee(
        businessId: String, customerId: String,
        paymentRequestId: String, updateFeeRequest: UpdateFeeRequest
    ) = remoteDataSource.updatePaymentOutAgentFee(
        businessId, customerId, paymentRequestId, updateFeeRequest
    )

    override suspend fun updateQrisInAgentFee(
        businessId: String, orderId: String, updateFeeRequest: UpdateFeeRequest
    ): ApiResponse<Void> =
        remoteDataSource.updateQrisInAgentFee(businessId, orderId, updateFeeRequest)

    override suspend fun getPaymentCategoryList(disbursableType: String): ApiResponse<List<PaymentCategory>> = remoteDataSource.getPaymentCategoryList(disbursableType)

    override suspend fun getTotalPayment(accountId: String, startDate: String, endDate: String, disbursableType: String): ApiResponse<TotalPayment>? = bankingRemoteDataSource?.getTotalPayment(accountId, startDate, endDate, disbursableType)

    override suspend fun updatePaymentInCategorySelected(accountId: String, customerId: String, paymentRequestId: String, paymentCategoryId: String) = remoteDataSource.updatePaymentInCategorySelected(accountId, customerId, paymentRequestId, mapOf(Pair("payment_category_id", paymentCategoryId)))

    override suspend fun updatePaymentOutCategorySelected(accountId: String, disbursementRequestId: String, paymentCategoryId: String) = remoteDataSource.updatePaymentOutCategorySelected(accountId, disbursementRequestId, mapOf(Pair("payment_category_id", paymentCategoryId)))

    override suspend fun retryQrisPayment(accountId: String, disbursableId: String) =
        remoteDataSource.retryQrisPayment(accountId, disbursableId)

    override suspend fun getPaymentMethods(
        accountId: String, paymentType: String, bankCode: String, transactionAmount: Double
    ) = callWithEnablePaymentCheck {
        remoteDataSource.getPaymentMethods(accountId, paymentType, bankCode, transactionAmount)
    }

    override suspend fun getLoyaltyTierDiscounts() = callWithEnablePaymentCheck {
        remoteDataSource.getLoyaltyTierDiscounts()
    }

    override suspend fun fetchPpobProducts(accountId: String, startDate: String, endDate: String) =
        remoteDataSource.fetchPpobProducts(accountId, startDate, endDate)

    override suspend fun getPaymentCategoriesHistory(accountId: String, startDate: String, endDate: String) =
        remoteDataSource.getPaymentCategoriesHistory(accountId, startDate, endDate)

    override suspend fun retryDisbursal(disbursableId: String, requestBody: DisbursalRequest) =
        remoteDataSource.retryDisbursal(disbursableId, requestBody)

    override suspend fun getDisbursementTimings(
        bankCode: String, createdAt: String?, amount: Double?
    ) = remoteDataSource.getDisbursementTimings(bankCode, createdAt, amount)

    override suspend fun getPaymentInLimits(
        accountId: String,
        customerId: String
    ) = remoteDataSource.getPaymentInLimits(accountId, customerId)

    override suspend fun getPaymentOutLimits(
        accountId: String,
        customerId: String
    ) = remoteDataSource.getPaymentOutLimits(accountId, customerId)

    override suspend fun getFirstTransaction(accountId: String): ApiResponse<FirstPaymentTransaction> =
            remoteDataSource.getFirstTransaction(accountId)

    override suspend fun getPaymentLimits() = remoteDataSource.getPaymentLimits()

    override suspend fun getRecentBankAccounts(
        accountId: String,
        page: Int,
        count: Int
    ): ApiResponse<BankAccountList> {
        return remoteDataSource.getRecentBankAccounts(accountId, page, count)
    }

    override suspend fun getFavouriteBankAccounts(
        accountId: String,
        page: Int,
        count: Int
    ): ApiResponse<BankAccountList> {
        return remoteDataSource.getFavouriteBankAccounts(accountId, page, count)
    }

    override suspend fun getSearchBankAccounts(
        accountId: String,
        page: Int,
        count: Int,
        searchTerm: String
    ): ApiResponse<SearchBankAccountList> {
        return remoteDataSource.getSearchBankAccounts(accountId, page, count, searchTerm)
    }

    override suspend fun addFavourite(
        accountId: String,
        id: String
    ): ApiResponse<BankAccountResponse> {
        return remoteDataSource.addFavourite(accountId, id)
    }

    override suspend fun removeFavourite(
        accountId: String,
        id: String
    ): ApiResponse<BankAccountResponse> {
        return remoteDataSource.removeFavourite(accountId, id)
    }

    override suspend fun removeBankAccount(
        accountId: String,
        id: String
    ): ApiResponse<BankAccountResponse> {
        return remoteDataSource.removeBankAccount(accountId, id)
    }

    override suspend fun fetchDeviceDetails(): ApiResponse<UserDetail> {
        return remoteDataSource.fetchUserDetails()
    }
}

interface PaymentRemoteRepository {
    suspend fun getMetadata(): ApiResponse<PaymentMetadata>
    suspend fun getMerchantBankAccounts(accountId: String, type: String? = null): ApiResponse<List<BankAccount>>
    suspend fun updateBusinessName(businessName: String): ApiResponse<Unit>
    suspend fun validateBankAccount(
        accountId: String, bankValidationRequest: BankValidationRequest
    ): ApiResponse<BankAccount>

    suspend fun validateAndAddCustomerBankAccount(
        accountId: String, customerId: String, bankValidationRequest: BankValidationRequest
    ): ApiResponse<AddBankAccountDetail>

    suspend fun addMerchantBankAccount(
        accountId: String, bankAccount: BankAccountRequest
    ): ApiResponse<BankAccount>

    suspend fun deleteMerchantBankAccount(
        accountId: String, bankAccountId: String
    ): ApiResponse<Any>

    suspend fun getCustomerBankAccounts(
        accountId: String, customerId: String
    ): ApiResponse<List<BankAccount>>

    suspend fun addCustomerBankAccounts(
        accountId: String, customerId: String, bankAccount: BankAccount
    ): ApiResponse<BankAccount>

    suspend fun deleteCustomerBankAccount(
        accountId: String, customerId: String, bankAccountId: String
    ): ApiResponse<Any>

    suspend fun requestPayment(
        accountId: String, customerId: String, paymentCollection: PaymentCollection
    ): ApiResponse<PaymentCollection>

    suspend fun getRequestedPayment(
        accountId: String, customerId: String, requestId: String
    ): ApiResponse<PaymentCollection>

    suspend fun getDisbursementOverview(
        accountId: String, customerId: String, request: DisbursementOverviewRequest
    ): ApiResponse<DisbursementOverviewResponse>

    suspend fun createDisbursement(
        accountId: String, customerId: String, request: DisbursementOverviewRequest
    ): ApiResponse<Disbursement>

    suspend fun getPaymentOverview(
        accountId: String, customerId: String, request: PaymentOverviewRequest
    ): ApiResponse<PaymentOverviewResponse>

    suspend fun getDisbursement(
        accountId: String, customerId: String, requestId: String
    ): ApiResponse<Disbursement>

    suspend fun getBanks(type: String? = null): ApiResponse<List<Bank>>
    suspend fun getVirtualAccountBanks(): ApiResponse<List<Bank>>
    suspend fun doHealthCheck(
        paymentHealthCheckRequest: PaymentHealthCheckRequest
    ): ApiResponse<PaymentHealthCheckResponse>

    suspend fun expirePayment(
        bookId: String, customerId: String, paymentId: String
    ): ApiResponse<Disbursement>

    fun getBanksLocal(): ArrayList<Bank>
    suspend fun updatePaymentInAgentFee(
        businessId: String, customerId: String,
        paymentRequestId: String, updateFeeRequest: UpdateFeeRequest
    ): ApiResponse<PaymentCollection>

    suspend fun updatePaymentOutAgentFee(
        businessId: String, customerId: String,
        paymentRequestId: String, updateFeeRequest: UpdateFeeRequest
    ): ApiResponse<Disbursement>

    suspend fun updateQrisInAgentFee(
        businessId: String, orderId: String,
        updateFeeRequest: UpdateFeeRequest
    ): ApiResponse<Void>

    suspend fun getPaymentCategoryList(disbursableType: String): ApiResponse<List<PaymentCategory>>

    suspend fun getTotalPayment(accountId: String,startDate: String, endDate: String, disbursableType: String): ApiResponse<TotalPayment>?
    suspend fun updatePaymentInCategorySelected(
        accountId: String, customerId: String, paymentRequestId: String, paymentCategoryId: String
    ): ApiResponse<Unit>

    suspend fun updatePaymentOutCategorySelected(
        accountId: String, disbursementRequestId: String, paymentCategoryId: String
    ): ApiResponse<Unit>

    suspend fun retryQrisPayment(accountId: String, disbursableId: String): ApiResponse<Any>
    suspend fun getPaymentMethods(
        accountId: String, paymentType: String, bankCode: String, transactionAmount: Double
    ): ApiResponse<PaymentMethodsResponse>

    suspend fun fetchPpobProducts(
        accountId: String, startDate: String, endDate: String
    ): ApiResponse<PpobProducts>

    suspend fun getLoyaltyTierDiscounts(): ApiResponse<List<LoyaltyTierDiscountsResponse>>

    suspend fun getPaymentCategoriesHistory(
        accountId: String, startDate: String, endDate: String
    ): ApiResponse<PaymentCategoriesHistoryResponse>

    suspend fun retryDisbursal(
        disbursableId: String, requestBody: DisbursalRequest
    ): ApiResponse<Any>

    suspend fun getDisbursementTimings(
        bankCode: String, createdAt: String?, amount: Double?
    ): ApiResponse<DisbursementTimings>

    suspend fun getPaymentInLimits(
        accountId: String, customerId: String
    ): ApiResponse<PaymentTransactionLimits>

    suspend fun getPaymentOutLimits(
        accountId: String, customerId: String
    ): ApiResponse<PaymentTransactionLimits>

    suspend fun getFirstTransaction(accountId: String):
            ApiResponse<FirstPaymentTransaction>

    suspend fun getPaymentLimits(): ApiResponse<PaymentLimits>

    suspend fun getRecentBankAccounts(
        accountId: String,
        page: Int,
        count: Int
    ): ApiResponse<BankAccountList>

    suspend fun getFavouriteBankAccounts(
        accountId: String,
        page: Int,
        count: Int
    ): ApiResponse<BankAccountList>

    suspend fun getSearchBankAccounts(
        accountId: String,
        page: Int,
        count: Int,
        searchTerm: String
    ): ApiResponse<SearchBankAccountList>

    suspend fun addFavourite(
        accountId: String,
        id: String
    ): ApiResponse<BankAccountResponse>

    suspend fun removeFavourite(
        accountId: String,
        id: String
    ): ApiResponse<BankAccountResponse>

    suspend fun removeBankAccount(
        accountId: String,
        id: String
    ): ApiResponse<BankAccountResponse>

    suspend fun fetchDeviceDetails(): ApiResponse<UserDetail>
}