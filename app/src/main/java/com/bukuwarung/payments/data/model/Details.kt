package com.bukuwarung.payments.data.model

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class Details(
    @SerializedName("ACCOUNT_NUMBER")
    val accountNumber: String? = null,
    @SerializedName("PHONE_NUMBER")
    val phoneNumber: String? = null,
    @SerializedName("region")
    val region: String? = null,
    @SerializedName("zoneId")
    val zoneId: String? = null
) : Parcelable