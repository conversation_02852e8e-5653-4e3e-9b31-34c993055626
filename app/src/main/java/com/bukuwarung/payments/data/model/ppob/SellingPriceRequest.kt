package com.bukuwarung.payments.data.model.ppob

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class SellingPriceRequest(

    @SerializedName("pricing")
    val pricing: Double,

    @SerializedName("pricing_type")
    val pricingType: PricingType,

    @SerializedName("rounding")
    val rounding: Boolean,

): Parcelable

enum class PricingType{
    SELLING_AMOUNT, PROFIT_MARGIN
}
