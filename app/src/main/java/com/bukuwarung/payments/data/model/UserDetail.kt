package com.bukuwarung.payments.data.model

import androidx.annotation.Keep

@Keep
data class UserDetail(
    val `data`: UserData?,
    val result: Boolean?
)

@Keep
data class UserData(
    val bankAccount: String,
    val bankName: String,
    val beneficiaryName: String,
    val deviceId: String,
    val janusAccountId: String,
    val paymentAccountId: String,
    val serialNumber: String,
    val storeAddress: String,
    val storeName: String,
    val tid: String,
    val userId: String
)
