package com.bukuwarung.payments.data.model.ppob

import android.os.Parcelable
import androidx.annotation.Keep
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Keep
@Parcelize
data class AddFavouriteResponse(

        @SerializedName("favourite_detail")
        val favouriteDetail: FavouriteDetail? = null,

        @SerializedName("message")
        val message: String? = null,

        @SerializedName("success")
        val success: Boolean? = null
) : Parcelable

@Keep
@Parcelize
data class FavouriteDetail(

        @SerializedName("alias")
        var alias: String? = null,

        @SerializedName("details")
        var details: Details? = null,

        @SerializedName("id")
        var id: String? = null,

        @SerializedName("category")
        var category: String? = null
) : Parcelable

