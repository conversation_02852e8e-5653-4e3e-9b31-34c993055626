package com.bukuwarung.payments.data.repository

import com.bukuwarung.data.restclient.ApiResponse
import com.bukuwarung.model.request.*
import com.bukuwarung.model.response.*
import com.bukuwarung.payments.data.model.CheckPinLengthResponse
import com.bukuwarung.payments.data.model.PinChangeResponse
import com.bukuwarung.session.SessionManager
import retrofit2.http.*

interface PaymentTwoFARemoteDataSource {
    @POST("/api/v2/auth/otp/send")
    suspend fun requestOTPForPIN(
            @Body pinOTPRequest: LoginRequest,
            @Query("phone_number") phoneNumber: String
    ): ApiResponse<OtpResponse>

    @POST("/api/v2/auth/otp/verify")
    suspend fun verifyOTP(
        @Header("x-ops-token") otpToken: String = "",
        @Body otpVerificationRequest: OtpVerifyRequest
    ): ApiResponse<OtpResponse>

    @POST("/api/v2/auth/twofa/verify")
    suspend fun verifyPin(
            @Body pinVerificationRequest: PinVerifyRequest
    ): ApiResponse<PinVerifyResponse>

    @GET("/api/v3/auth/twofa/status/{user_id}")
    suspend fun checkPinChangeRequest(
        @Path("user_id") userId: String
    ): ApiResponse<PinChangeResponse>

    @GET("/api/v3/auth/twofa/{user_id}")
    suspend fun checkPinLength(
        @Path("user_id") userId: String
    ): ApiResponse<CheckPinLengthResponse>

    @POST("/api/v3/auth/twofa/register")
    suspend fun createPinV3(
        @Header("x-ops-token") otpToken: String = "",
        @Body createPinRequest: PinSetupRequest): ApiResponse<PinSetupResponse>

    @PUT("/api/v3/auth/twofa/update")
    suspend fun updatePinV3(
        @Header("x-ops-token") otpToken: String = "",
        @Body updatePinRequest: PinUpdateRequest): ApiResponse<PinUpdateResponse>

    @PUT("/api/v3/auth/twofa/forgot")
    suspend fun forgotPinV3(
        @Header("x-ops-token") otpToken: String = "",
        @Body forgetPinRequest: PinForgetRequest,
        @Header("app-instance-installation-id") insallationId: String = SessionManager.getInstance().fcmDeviceId
    ): ApiResponse<PinForgetResponse>
}