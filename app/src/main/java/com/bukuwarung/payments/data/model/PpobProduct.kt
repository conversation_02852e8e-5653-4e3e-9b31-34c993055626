package com.bukuwarung.payments.data.model

import android.os.Parcelable
import com.bukuwarung.payments.data.model.ppob.PpobProductInfo
import com.bukuwarung.payments.data.model.ppob.ValidationInfo
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class PpobProduct(
        @SerializedName("name")
        val name: String? = null,
        @SerializedName("description")
        val description: String? = null,
        @SerializedName("sku")
        val sku: String? = null,
        @SerializedName("amount")
        val amount: Double? = null,
        @SerializedName("product_info")
        val productInfo: PpobProductInfo? = null,
        @SerializedName("validation")
        val validationInfo: ValidationInfo? = null,
        @SerializedName("active")
        val active: Boolean? = null,
        @SerializedName("discount")
        val discount: Discount? = null,
        var isSelected: Boolean = false
) : Parcelable
