package com.bukuwarung.payments.data.model.ppob

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class ReminderResponse(

        @field:SerializedName("reminders")
		val reminders: List<RemindersItem>? = null,

        @field:SerializedName("metadata")
		val metadata: Metadata? = null
) : Parcelable

@Parcelize
data class CutomerProfile(

        @field:SerializedName("accountId")
		val accountId: String? = null,

        @field:SerializedName("is_favorite")
		val isFavorite: Boolean? = null,

        @field:SerializedName("lookupKey")
		val lookupKey: String? = null,

        @field:SerializedName("alias")
		val alias: String? = null,

        @field:SerializedName("details")
		val details: Details? = null,

        @field:SerializedName("id")
		val id: String? = null,

        @field:SerializedName("category")
		val category: String? = null
) : Parcelable

@Parcelize
data class RemindersItem(

        @field:SerializedName("reminder_message")
		val reminderMessage: String? = null,

        @field:SerializedName("is_order_created")
		val isOrderCreated: Boolean? = null,

        @field:SerializedName("created_order_id")
		val createdOrderId: String? = null,

        @field:SerializedName("due_date")
		val dueDate: String? = null,

        @field:SerializedName("customer_profile")
		val cutomerProfile: CutomerProfile? = null,

        @field:SerializedName("id")
		val id: String? = null,

        @field:SerializedName("product")
		val product: Product? = null,

        @field:SerializedName("status")
		val status: String? = null
) : Parcelable

@Parcelize
data class Details(

		@field:SerializedName("BILL_CUSTOMER_NAME")
		val billCustomerName: String? = null,

		@field:SerializedName("PHONE_NUMBER")
		val phoneNumber: String? = null,

		@field:SerializedName("ACCOUNT_NUMBER")
		val accountNumber: String? = null
) : Parcelable

@Parcelize
data class Product(

		@field:SerializedName("category")
		val category: String? = null,

		@field:SerializedName("name")
		val name: String? = null,

		@field:SerializedName("item_price")
		val itemPrice: Double? = null,

		@field:SerializedName("selling_price")
		val sellingPrice: Double? = null
) : Parcelable

