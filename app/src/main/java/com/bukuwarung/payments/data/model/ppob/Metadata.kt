package com.bukuwarung.payments.data.model.ppob

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class Metadata(

        @SerializedName("page_number")
        val pageNumber: Int? = null,

        @SerializedName("total_count")
        val totalCount: Int? = null,

        @SerializedName("page_length")
        val pageLength: Int? = null
) : Parcelable