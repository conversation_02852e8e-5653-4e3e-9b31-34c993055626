package com.bukuwarung.payments.data.model

import android.os.Parcelable
import com.bukuwarung.database.entity.BankAccount
import com.bukuwarung.database.entity.RefundBankAccount
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize
import java.io.Serializable

@Parcelize
data class FinproRefunds(
    @SerializedName("payment_channel")
    val paymentChannel: String?,
    @SerializedName("payment_method")
    val refundPaymentMethod: List<RefundBankAccount>? = null
) : Parcelable, Serializable
