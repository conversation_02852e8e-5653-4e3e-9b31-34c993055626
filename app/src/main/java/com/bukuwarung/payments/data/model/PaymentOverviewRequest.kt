package com.bukuwarung.payments.data.model

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize
import java.math.BigDecimal

@Parcelize
data class PaymentOverviewRequest(
        @SerializedName("amount")
        val amount: BigDecimal,

        @SerializedName("description")
        val description: String,

        @SerializedName("bank_account_id")
        val bankAccountId: String?,

        @SerializedName("customer_name")
        val customerName: String,

        val accountId: String,
        val customerId: String,
        val note: String,
        val bankCode: String?
) : Parcelable
