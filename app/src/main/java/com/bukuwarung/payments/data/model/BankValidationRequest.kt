package com.bukuwarung.payments.data.model

import android.os.Parcelable
import com.bukuwarung.constants.PaymentConst
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class BankValidationRequest(
        @SerializedName("bank_code")
        val bankCode: String,

        @SerializedName("account_number")
        val accountNumber: String,

        @SerializedName("is_payment_in")
        val isPaymentIn: Boolean = false,

        @SerializedName("proposed_qris_bank")
        val proposedQrisBank: Boolean = false,

        @SerializedName("is_refundable_bank")
        val isRefundableBank: Boolean = false,

        @SerializedName("account_owner")
        val accountOwner: PaymentConst.BankAccountOwner = PaymentConst.BankAccountOwner.SELF
) : Parcelable