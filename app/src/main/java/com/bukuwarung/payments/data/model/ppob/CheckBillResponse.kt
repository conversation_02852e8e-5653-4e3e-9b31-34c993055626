package com.bukuwarung.payments.data.model.ppob

import android.os.Parcelable
import com.bukuwarung.payments.data.model.Campaign
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class CheckBillResponse(
        @SerializedName("reminder_id")
        val reminderId: String? = null,

        @SerializedName("product")
        val product: Product? = null,

        @SerializedName("bill_details")
        val billDetail: BillDetails? = null,

        @SerializedName("campaigns")
        val campaigns: List<Campaign>? = null
) : Parcelable

@Parcelize
data class BillDetails(

        @SerializedName("customer_name")
        val customerName: String? = null,

        @SerializedName("amount")
        val amount: String? = null,

        @SerializedName("customer_number")
        val customerNumber: String? = null,

        @SerializedName("biller_name")
        val billerName: String? = null,

        @SerializedName("period")
        val period: String? = null,

        @SerializedName("id")
        val id: String? = null,

        @SerializedName("account_number")
        val accountNumber: String? = null,

        @SerializedName("no_meter")
        val meterNumber: String? = null,

        @SerializedName("tarif")
        val tarif: String? = null,

        @SerializedName("total_lembar_tagihan")
        val totalLembarTagihan: String? = null,

        @SerializedName("periode")
        val periode: String? = null,

        @SerializedName("product_name")
        val productName: String? = null,

        @SerializedName("phone_number")
        val phoneNumber: String? = null,

        @SerializedName("provider")
        val provider: String? = null,

        @SerializedName("usage")
        val usage: String? = null,

        @SerializedName("fee")
        val fee: Double? = null,

        @SerializedName("discounted_fee")
        val discountedFee: Double? = null,

        @SerializedName("discounted_amount")
        val discountedAmount: Double? = null,

        @SerializedName("cost")
        val cost: Double? = null,

        @SerializedName("admin_fee")
        val adminFee: Double? = null
) : Parcelable