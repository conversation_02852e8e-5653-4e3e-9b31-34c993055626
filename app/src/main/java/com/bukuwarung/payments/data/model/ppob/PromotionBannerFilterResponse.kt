package com.bukuwarung.payments.data.model.ppob

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class PromotionBannerFilterResponse(

        @SerializedName("filter_groups")
        val filterGroups: List<FilterGroupsItem>? = null
) : Parcelable

@Parcelize
data class FilterGroupsItem(

        @SerializedName("data")
        val data: Data? = null,

        @SerializedName("id")
        val id: String? = null,

        @SerializedName("display_name")
        val displayName: String? = null,
        var isSelected: Boolean = false
) : Parcelable

@Parcelize
data class ToggleFiltersItem(

        @SerializedName("selector_key")
        val selectorKey: String? = null,

        @SerializedName("id")
        val id: String? = null,

        @SerializedName("display_text")
        val displayText: String? = null,
        
        var isSelected: Boolean = false,

        var showToggle: Boolean = true
) : Parcelable

@Parcelize
data class Data(

        @SerializedName("toggle_filters")
        val toggleFilters: List<ToggleFiltersItem>? = null,

        @SerializedName("flags")
        val flags: Map<String, Boolean>? = null,

        @SerializedName("banner_url")
        val bannerUrl: String? = null
) : Parcelable
