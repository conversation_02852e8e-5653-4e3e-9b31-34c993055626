package com.bukuwarung.payments.data.model

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize
import java.io.Serializable

@Parcelize
data class PpobProductDetail(
        @SerializedName("sku")
        val sku: String,
        @SerializedName("name")
        val name: String,
        @SerializedName("amount")
        val amount: Double,
        @SerializedName("fee")
        val fee: Double,
        @SerializedName("admin_fee")
        val adminFee: Double,
        @SerializedName("discounted_fee")
        val discountedFee: Double,
        @SerializedName("details")
        val details: PpobDetailParam,
        @SerializedName("beneficiary")
        val beneficiary: FinproBeneficiary,
        @SerializedName("selling_price")
        val sellingPrice: Double,
        @SerializedName("disbursableType")
        val disbursableType: String? = null
): Parcelable, Serializable

const val qrisDisbursableType = "Order"

const val payInDisbursableType = "PaymentRequest"