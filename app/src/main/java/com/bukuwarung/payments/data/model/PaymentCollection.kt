package com.bukuwarung.payments.data.model

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize
import java.math.BigDecimal

@Parcelize
data class PaymentCollection(
        @SerializedName("account_id")
        val accountId: String? = null,

        @SerializedName("customer_id")
        val customerId: String? = null,

        @SerializedName("customer_name")
        val customerName: String? = null,

        @SerializedName("receiver_bank")
        val receiverBank: ReceiverBank? = null,

        @SerializedName("transaction_id")
        val transactionId: String? = null,

        @SerializedName("payment_request_id")
        val paymentRequestId: String? = null,

        @SerializedName("amount")
        val amount: BigDecimal = BigDecimal.ZERO,

        @SerializedName("fee")
        val fee: Float = 0.0f,

        @SerializedName("discount")
        val discount: Float = 0.0f,

        @SerializedName("invoice_url")
        val invoiceUrl: String? = null,

        @SerializedName("share_url_template")
        val shareUrlTemplate: String? = null,

        @SerializedName("expired_at")
        val expiredAt: String? = null,

        @SerializedName("template")
        val template: String? = null,

        @SerializedName("status")
        val status: String? = null,

        @SerializedName("description")
        val description: String? = null,

        @SerializedName("bank_account_id")
        val bankAccountId: String? = null,

        @SerializedName("payment_method")
        val paymentMethod: String? = null,

        @SerializedName("payment_channel")
        val paymentChannel: String? = null,

        @SerializedName("progress")
        val progress: List<PaymentProgress> = emptyList(),

        @SerializedName("agent_fee")
        val agentFeeInfo: AgentFeeInfo? = null,

        @SerializedName("transaction_type")
        val transactionType: String? = null,

        @SerializedName("payment_category")
        val paymentCategory: PaymentCategoryItem? = null,

        @SerializedName("payment_category_id")
        val paymentCategoryId: String? = null,

        @SerializedName("reference_id")
        val referenceId: String? = null,

        @SerializedName("received_amount")
        val receivedAmount: BigDecimal?,

        @SerializedName("extras")
        val extras: PaymentExtras? = null): Parcelable {

    fun isPaid() = status.equals(PaymentHistory.STATUS_PAID, ignoreCase = true)
    fun isCompleted() = status.equals(PaymentHistory.STATUS_COMPLETED, ignoreCase = true)


    companion object {
            fun newCollectionRequest(
                    amount: Long,
                    description: String,
                    bankAccountId: String?,
                    customerName: String?,
                    extras: PaymentExtras? = null,
                    paymentCategoryId: String? = null,
                    referenceId: String? = null,
                    receivedAmount: BigDecimal? = null
            ): PaymentCollection {
                    return PaymentCollection(
                            amount = BigDecimal(amount),
                            description = description,
                            bankAccountId = bankAccountId,
                            customerName = customerName,
                            extras = extras,
                            paymentCategoryId = paymentCategoryId,
                            referenceId = referenceId,
                            receivedAmount = receivedAmount
                    )
            }
    }
}
