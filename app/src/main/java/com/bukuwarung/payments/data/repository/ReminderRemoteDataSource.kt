package com.bukuwarung.payments.data.repository

import com.bukuwarung.data.restclient.ApiResponse
import com.bukuwarung.payments.data.model.FinproOrderResponse
import com.bukuwarung.payments.data.model.ppob.*
import retrofit2.http.*

interface ReminderRemoteDataSource {
    @GET
    suspend fun getList(@Url url: String): ApiResponse<ReminderResponse>

    @GET("finpro/api/reminder/{accountId}/bill-check/{id}")
    suspend fun checkBill(@Path("accountId") accountId: String,
                               @Path("id") reminderId: String) : ApiResponse<CheckBillResponse>

    @POST("finpro/api/reminder/{accountId}/remind/{id}")
    suspend fun markAsReminded(@Path("accountId") accountId: String,
                               @Path("id") reminderId: String,
                               @Body request : MarkRemindedRequest) : ApiResponse<RemindersItem>

    @POST("finpro/api/reminder/{accountId}/create-order/{id}")
    suspend fun checkout(@Path("accountId") accountId: String,
                         @Path("id") reminderId: String,
                         @Body request: MarkRemindedRequest) : ApiResponse<FinproOrderResponse>

    @GET("finpro/api/flags/{accountId}")
    suspend fun checkEligibility(@Path("accountId") accountId: String) : ApiResponse<ReminderEligibilityResponse>

    @GET("finpro/api/reminder/{accountId}/filters")
    suspend fun getFilters(@Path("accountId") accountId: String, @Query("tab") tab: String): ApiResponse<ReminderFilterResponse>
}