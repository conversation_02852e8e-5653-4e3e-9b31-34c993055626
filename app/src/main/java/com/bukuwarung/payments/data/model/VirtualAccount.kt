package com.bukuwarung.payments.data.model

import android.os.Parcelable
import com.bukuwarung.utils.DateTimeUtils
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class VirtualAccount(
        @SerializedName("bank_code")
        val bankCode: String,

        @SerializedName("account_number")
        val accountNumber: String?,

        @SerializedName("name")
        val name: String,

        @SerializedName("logo")
        val logo: String,

        @SerializedName("expiry_time")
        val expiryTime: String?
) : Parcelable {
    fun getFormattedExpiryDate(): String? = DateTimeUtils.getFormattedLocalDateTimeForPayment(expiryTime)
    fun getRemainingExpiryTime(): Long? = DateTimeUtils.getInvoiceExpiryTime(expiryTime)
}