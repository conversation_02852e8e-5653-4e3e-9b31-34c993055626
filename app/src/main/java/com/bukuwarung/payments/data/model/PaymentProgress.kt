package com.bukuwarung.payments.data.model

import android.os.Parcelable
import com.bukuwarung.utils.DateTimeUtilsKt
import kotlinx.android.parcel.Parcelize
import java.io.Serializable

@Parcelize
data class PaymentProgress(
    val state: String,
    val timestamp: String?,
    val description: String?,
    val additional_info: String?,
    var hasNextTimestamp: Boolean = false,
    var hasPreviousTimestamp: Boolean = false,
    val hasAdditionalInfo: Boolean = false
) : Parcelable, Serializable {
    fun getFormattedTimestamp(): String {
        return if (timestamp != null) {
            DateTimeUtilsKt.getStringFromUtc(timestamp, DateTimeUtilsKt.DD_MMM_YYYY_HH_MM)
        } else ""
    }
}
