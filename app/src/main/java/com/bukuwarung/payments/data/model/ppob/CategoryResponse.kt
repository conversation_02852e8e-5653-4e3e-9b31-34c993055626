package com.bukuwarung.payments.data.model.ppob

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class CategoryResponse(

        @SerializedName("category_groups")
        val categoryGroups: List<CategoryGroupsItem>? = null
) : Parcelable

@Parcelize
data class CategoryGroupsItem(

        @SerializedName("values")
        val values: List<ValuesItem>? = null,

        @SerializedName("display_name")
        val displayName: String? = null
) : Parcelable

@Parcelize
data class ValuesItem(

        @SerializedName("category_code")
        val categoryCode: String? = null,

        @SerializedName("display_name")
        val displayName: String? = null,

        var isSelected: Boolean = false
) : Parcelable
