package com.bukuwarung.payments.data.repository

import com.bukuwarung.activities.businessdashboard.model.BusinessDashboardCashbackResponseItem
import com.bukuwarung.data.restclient.ApiResponse
import com.bukuwarung.payments.data.model.*


open class BankingRepository(val remoteDataSource: BankingRemoteDataSource) :
    BankingRemoteRepository {
    override suspend fun getQrisStatus(bookId: String) = remoteDataSource.getQrisStatus()

    override suspend fun getFeatureFlags() = remoteDataSource.getFeatureFlags()

    override suspend fun updateQrisDetails(
        qrisAccountId: String,
        qrisUpdateMap: Map<String, String>
    ) = remoteDataSource.updateQrisDetails(qrisAccountId, qrisUpdateMap)

    override suspend fun getQrisBatchSettlementDashboard(
        userId: String,
        startDate: String,
        endDate: String
    ) = remoteDataSource.getQrisBatchSettlementDashboard(userId, startDate, endDate)

    override suspend fun getCashbackList(
        userId: String,
        pageCount: Int,
        bookId: String?,
        start_date: String?,
        end_date: String?,
        disbursableType: String?
    ) =
        remoteDataSource.getCashbackList(userId, pageCount, bookId = bookId, start_date= start_date, end_date= end_date, disbursableType)
}

interface BankingRemoteRepository {
    suspend fun getQrisStatus(bookId: String): ApiResponse<QrisResponse>

    suspend fun getFeatureFlags(): ApiResponse<BankingFeatureFlags>

    suspend fun updateQrisDetails(
        qrisAccountId: String,
        qrisUpdateMap: Map<String, String>
    ): ApiResponse<Unit>

    suspend fun getQrisBatchSettlementDashboard(
        userId: String,
        startDate: String,
        endDate: String
    ): ApiResponse<QrisDashboardResponse>

    suspend fun getCashbackList(
        userId: String,
        pageCount: Int,
        bookId: String? = null,
        start_date: String? = null,
        end_date: String? = null,
        disbursableType: String?
    ): ApiResponse<List<BusinessDashboardCashbackResponseItem>>

}
