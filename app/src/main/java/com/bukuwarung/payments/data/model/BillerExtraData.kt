package com.bukuwarung.payments.data.model

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class BillerExtraData(
    @SerializedName("faq_link")
    val faqLink: String?,
    @SerializedName("banner_text")
    val bannerText: String?,
    @SerializedName("banner_image")
    val bannerImage: String?,
    @SerializedName("banner_title")
    val bannerTitle: String?,
    @SerializedName("info_box_text")
    val infoBoxText: String?,
    @SerializedName("info_box_cta")
    val infoBoxCta: String?,
    @SerializedName("layout_type")
    val layoutType: String?,
) : Parcelable
