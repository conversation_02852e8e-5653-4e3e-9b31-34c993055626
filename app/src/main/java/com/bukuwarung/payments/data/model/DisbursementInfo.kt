package com.bukuwarung.payments.data.model

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize


@Parcelize
data class DisbursementTimings(
    @SerializedName("operational_hours")
    val operatingHours: List<BankDisbursementTimings>? = null,
    @SerializedName("non_operational_hours")
    val nonOperatingHours: List<BankDisbursementTimings>? = null
) : Parcelable

@Parcelize
data class BankDisbursementTimings(
    @SerializedName("bank_code")
    val bankCode: String? = "",
    @SerializedName("amount")
    val amount: String? = "",
    @SerializedName("time")
    val requestTimestamp: String? = "",
    @SerializedName("eta")
    val eta: String? = "",
    @SerializedName("timezone")
    val timezone: String? = "",
) : Parcelable