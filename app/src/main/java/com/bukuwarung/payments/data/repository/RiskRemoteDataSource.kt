package com.bukuwarung.payments.data.repository

import com.bukuwarung.data.restclient.ApiResponse
import com.bukuwarung.payments.data.model.BookValidationRequest
import com.bukuwarung.payments.data.model.WhitelistGroup
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.POST
import retrofit2.http.Query

interface RiskRemoteDataSource {
    @POST("blacklist/name/validate")
    suspend fun validateBookName(@Body requestBody: BookValidationRequest): ApiResponse<Any>

    @GET("whitelist")
    suspend fun getWhitelistGroups(@Query("groupCode") groupCode: String): ApiResponse<List<WhitelistGroup>>
}
