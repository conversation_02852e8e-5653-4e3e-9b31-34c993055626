package com.bukuwarung.payments.data.model

import com.google.gson.annotations.SerializedName


data class PaymentTransactionLimits(
    @SerializedName("daily_transaction_limit")
    val dailyTrxLimit: Double? = null,
    @SerializedName("remaining_daily_transaction_limit")
    val remainingDailyTrxLimit: Double? = null,
    @SerializedName("transaction_limit")
    val perTrxLimit: Double? = null,
    @SerializedName("whitelist_limit")
    val whitelistLimits: WhitelistTransactionLimits? = null
)

data class WhitelistTransactionLimits(
    @SerializedName("remaining_trx_amount_limit")
    val remainingTrxAmountLimit: Double? = null,
    @SerializedName("max_trx_amount_limit")
    val maxTrxAmountLimit: Double? = null,
)