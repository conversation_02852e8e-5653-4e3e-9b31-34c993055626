package com.bukuwarung.payments.data.model

import com.google.gson.annotations.SerializedName

data class TopupSaldoRequest(
        @SerializedName("amount")
        val amount: Double? = null,
        @SerializedName("cancel_unpaid_topup_id")
        val cancelUnpaidTopupId: String? = null,
        @SerializedName("account_id")
        val accountId: String? = null,
        @SerializedName("payment_methods")
        val paymentMethods: List<FinproCheckoutPayment>? = null
)
