package com.bukuwarung.payments.data.repository

import com.bukuwarung.BukuWarungKeys
import com.bukuwarung.data.restclient.ApiResponse
import com.bukuwarung.model.request.LoginRequest
import com.bukuwarung.model.request.OtpVerifyRequest
import com.bukuwarung.model.request.PinForgetRequest
import com.bukuwarung.model.request.PinSetupRequest
import com.bukuwarung.model.request.PinUpdateRequest
import com.bukuwarung.model.request.PinVerifyRequest
import com.bukuwarung.model.response.OtpResponse
import com.bukuwarung.model.response.PinVerifyResponse
import com.bukuwarung.payments.data.model.PinChangeResponse
import com.bukuwarung.preference.AppConfigManager
import com.bukuwarung.session.SessionManager
import com.bukuwarung.session.User
import com.bukuwarung.utils.Utility

class PaymentTwoFARepository(private val remoteDataSource: PaymentTwoFARemoteDataSource) {

    private fun getDeviceId(): String {
        val deviceId = AppConfigManager.getInstance().paymentDeviceId ?: Utility.uuid()

        AppConfigManager.getInstance().paymentDeviceId = deviceId
        return deviceId
    }

    suspend fun requestOTPForPIN(otpChannel: String, authAction: String): ApiResponse<OtpResponse> {
        val pinOTPRequest = LoginRequest(
            action = authAction,
            countryCode = SessionManager.getInstance().countryCode,
            method = otpChannel,
            deviceId = getDeviceId(),
            clientId = BukuWarungKeys.clientId.orEmpty(),
            clientSecret = BukuWarungKeys.clientSecret.orEmpty()
        )
        return remoteDataSource.requestOTPForPIN(pinOTPRequest, User.getUserId())
    }

    suspend fun verifyOTP(otp: String, authAction: String): ApiResponse<OtpResponse> {
        val otpVerificationRequest = OtpVerifyRequest().apply {
            action = authAction
            countryCode = SessionManager.getInstance().countryCode
            this.otp = otp
            phone = User.getUserId()
            userId = User.getUserId()
            deviceId = getDeviceId()
            clientId = BukuWarungKeys.clientId.orEmpty()
            clientSecret = BukuWarungKeys.clientSecret.orEmpty()
        }
        return remoteDataSource.verifyOTP(
            SessionManager.getInstance().opToken,
                otpVerificationRequest
        )
    }

    suspend fun verifyPIN(pin: String): ApiResponse<PinVerifyResponse> {
        val pinVerificationRequest = PinVerifyRequest().apply {
            this.pin = pin
            deviceId = getDeviceId()
        }
        return remoteDataSource.verifyPin(
            pinVerificationRequest
        )
    }

    suspend fun checkPinChangeRequest(): ApiResponse<PinChangeResponse> {
        return remoteDataSource.checkPinChangeRequest(User.getUserId())
    }

    suspend fun checkPinLength() = remoteDataSource.checkPinLength(User.getUserId())

    suspend fun createPinV3(pinSetupRequest: PinSetupRequest) = remoteDataSource.createPinV3(SessionManager.getInstance().opToken,pinSetupRequest)
    suspend fun forgotPinV3(pinForgotRequest: PinForgetRequest) = remoteDataSource.forgotPinV3(SessionManager.getInstance().opToken,pinForgotRequest)
    suspend fun updatePinV3(pinUpdateRequest: PinUpdateRequest) = remoteDataSource.updatePinV3(SessionManager.getInstance().opToken, pinUpdateRequest)

}