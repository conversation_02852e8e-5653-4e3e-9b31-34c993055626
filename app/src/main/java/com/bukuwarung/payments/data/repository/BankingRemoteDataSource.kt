package com.bukuwarung.payments.data.repository

import com.bukuwarung.activities.businessdashboard.model.BusinessDashboardCashbackResponseItem
import com.bukuwarung.activities.businessdashboard.model.TotalPayment
import com.bukuwarung.data.restclient.ApiResponse
import com.bukuwarung.payments.data.model.*
import retrofit2.http.*

interface BankingRemoteDataSource {
    @GET("api/qris/status")
    suspend fun getQrisStatus(): ApiResponse<QrisResponse>

    @GET("api/feature-flags")
    suspend fun getFeatureFlags(): ApiResponse<BankingFeatureFlags>

    @PATCH("api/qris/status/{qris_account_id}")
    suspend fun updateQrisDetails(
        @Path("qris_account_id") qrisAccountId: String,
        @Body qrisUpdateMap: Map<String, String>
    ): ApiResponse<Unit>

    @GET("api/qris/payment/batch-settlements/user")
    suspend fun getQrisBatchSettlementDashboard(
        @Header("X-User-Id") userId: String,
        @Query("start_date") startDate: String,
        @Query("end_date") endDate: String
    ): ApiResponse<QrisDashboardResponse>

    @GET("api/saldo/cashback/redemptions/user")
    suspend fun getCashbackList(
        @Header("x-user-id") userId: String,
        @Query("page") page: Int,
        @Query("bookId") bookId: String?,
        @Query("start_date") start_date: String?,
        @Query("end_date") end_date: String?,
        @Query("disbursable_type") disbursableType: String?
    ): ApiResponse<List<BusinessDashboardCashbackResponseItem>>

    @GET("api/saldo/cashback/redemptions/user/total")
    suspend fun getTotalPayment(
        @Header("x-user-id") userId: String, @Query("start_date") startDate: String,
        @Query("end_date") endDate: String, @Query("disbursable_type") disbursableType: String
    ): ApiResponse<TotalPayment>

}
