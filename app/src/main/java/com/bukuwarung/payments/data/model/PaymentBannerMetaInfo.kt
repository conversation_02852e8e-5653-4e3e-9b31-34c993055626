package com.bukuwarung.payments.data.model

import android.os.Parcelable
import com.bukuwarung.payments.constants.KycStatus
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class PaymentBannerMetaInfo(
        @SerializedName("banner_type")
        val bannerType: String? = null,
        @SerializedName("kyc_status")
        val kycStatus: KycStatus? = null
) : Parcelable
