package com.bukuwarung.payments.data.model

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class FinproGetPaymentMethodsV2Response(
    @SerializedName("title")
    val title: String?,
    @SerializedName("highlighted_payment_channels")
    val highlightedPaymentChannels: HighlightedPaymentChannels?,
    @SerializedName("other_payment_methods")
    val otherPaymentChannels: OtherPaymentChannels?
) : Parcelable

@Parcelize
data class HighlightedPaymentChannels(
    @SerializedName("title")
    val title: String?,
    @SerializedName("channels")
    val channels: List<FinproPaymentMethod>?
) : Parcelable

@Parcelize
data class OtherPaymentChannels(
    @SerializedName("title")
    val title: String?,
    @SerializedName("methods")
    val methods: List<FinproGetPaymentMethodsResponse>?
) : Parcelable
