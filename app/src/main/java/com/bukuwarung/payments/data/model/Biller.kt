package com.bukuwarung.payments.data.model

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize
import java.io.Serializable

@Parcelize
data class Biller(
        @SerializedName("code")
        val code: String?,
        @SerializedName("name")
        val name: String?,
        @SerializedName("icon")
        val icon: String?,
        @SerializedName("metadata")
        val billerMetaData: BillerMetaData? = null,
        @SerializedName("category")
        val category: String? = null,
        @SerializedName("provider")
        val provider: String? = null,
        @SerializedName("type")
        val type: String? = null
) : Parcelable
