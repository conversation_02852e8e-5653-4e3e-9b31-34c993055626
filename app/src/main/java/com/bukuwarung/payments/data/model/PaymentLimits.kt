package com.bukuwarung.payments.data.model

import com.google.gson.annotations.SerializedName


data class PaymentLimits(
    @SerializedName("whitelist_limit")
    val whitelistLimits: WhitelistLimits? = null
)

data class WhitelistLimits(
    @SerializedName("payment_in_limit")
    val paymentInLimits: PaymentInLimits? = null,
    @SerializedName("payment_out_limit")
    val paymentOutLimits: PaymentOutLimits? = null,
    @SerializedName("total_trx_count_limit")
    val totalTrxCountLimit: Int? = null
)

data class PaymentInLimits(
    @SerializedName("remaining_trx_count_limit")
    val remainingTrxCountLimit: Int? = null,
    @SerializedName("max_trx_count_limit")
    val maxTrxCountLimit: Int? = null,
    @SerializedName("remaining_trx_amount_limit")
    val remainingTrxAmountLimit: Double? = null,
    @SerializedName("max_trx_amount_limit")
    val maxTrxAmountLimit: Double? = null
)

data class PaymentOutLimits(
    @SerializedName("remaining_trx_count_limit")
    val remainingTrxCountLimit: Int? = null,
    @SerializedName("max_trx_count_limit")
    val maxTrxCountLimit: Int? = null,
    @SerializedName("remaining_trx_amount_limit")
    val remainingTrxAmountLimit: Double? = null,
    @SerializedName("max_trx_amount_limit")
    val maxTrxAmountLimit: Double? = null
)