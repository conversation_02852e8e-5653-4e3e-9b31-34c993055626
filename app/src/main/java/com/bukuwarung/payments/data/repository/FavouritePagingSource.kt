package com.bukuwarung.payments.data.repository

import androidx.lifecycle.MutableLiveData
import androidx.paging.PagingSource
import androidx.paging.PagingState
import com.bukuwarung.data.restclient.ApiEmptyResponse
import com.bukuwarung.data.restclient.ApiErrorResponse
import com.bukuwarung.data.restclient.ApiSuccessResponse
import com.bukuwarung.domain.payments.FinproUseCase
import com.bukuwarung.payments.data.model.ppob.ProfilesItem
import com.bukuwarung.payments.ppob.base.model.PagingStatus
import com.bukuwarung.session.SessionManager
import com.bukuwarung.utils.Utility
import com.bukuwarung.utils.orNil

class FavouritePagingSource(
    private val finproUseCase: FinproUseCase,
    private val pagingStatusLiveData: MutableLiveData<PagingStatus>,
    private val category: String
) : PagingSource<Int, ProfilesItem>() {

    override suspend fun load(params: LoadParams<Int>): LoadResult<Int, ProfilesItem> {
        val page = params.key ?: 0

        // Show loading status
        pagingStatusLiveData.postValue(if (page == 0) PagingStatus.Loading else PagingStatus.LoadingNextPage)

        // Check internet connectivity
        if (!Utility.hasInternet()) {
            pagingStatusLiveData.postValue(PagingStatus.NoInternet)
            return LoadResult.Error(Exception("No Internet Connection"))
        }

        // Fetch data using the UseCase
        return when (val response = finproUseCase.getFavourites(
            SessionManager.getInstance().businessId,
            category,
            page,
            params.loadSize
        )) {
            is ApiSuccessResponse -> {
                val data = response.body.profiles
                val totalItemCount = response.body.metadata?.totalCount.orNil
                val nextPage = if (page * params.loadSize < totalItemCount) page + 1 else null

                if (data.isNullOrEmpty()) {
                    pagingStatusLiveData.postValue(if (page == 0) PagingStatus.Empty else PagingStatus.EmptyNextPage)
                    LoadResult.Page(
                        emptyList(),
                        prevKey = if (page == 0) null else page - 1,
                        nextKey = null
                    )
                } else {
                    pagingStatusLiveData.postValue(PagingStatus.Loaded(totalItemCount))
                    LoadResult.Page(data, if (page == 0) null else page - 1, nextPage)
                }
            }

            is ApiEmptyResponse -> {
                pagingStatusLiveData.postValue(if (page == 0) PagingStatus.Empty else PagingStatus.EmptyNextPage)
                LoadResult.Page(
                    emptyList(),
                    prevKey = if (page == 0) null else page - 1,
                    nextKey = null
                )
            }

            is ApiErrorResponse -> {
                pagingStatusLiveData.postValue(PagingStatus.Error(response.errorMessage))
                LoadResult.Error(Exception(response.errorMessage))
            }
        }
    }

    override fun getRefreshKey(state: PagingState<Int, ProfilesItem>): Int? {
        // Return the closest page to the anchor position
        return state.anchorPosition?.let { anchorPosition ->
            state.closestPageToPosition(anchorPosition)?.prevKey?.plus(1)
                ?: state.closestPageToPosition(anchorPosition)?.nextKey?.minus(1)
        }
    }
}
