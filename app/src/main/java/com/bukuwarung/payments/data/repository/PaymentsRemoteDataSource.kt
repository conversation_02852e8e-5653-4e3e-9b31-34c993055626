package com.bukuwarung.payments.data.repository

import com.bukuwarung.activities.businessdashboard.model.PaymentCategoriesHistoryResponse
import com.bukuwarung.activities.businessdashboard.model.PpobProducts
import com.bukuwarung.data.restclient.ApiResponse
import com.bukuwarung.database.entity.Bank
import com.bukuwarung.database.entity.BankAccount
import com.bukuwarung.payments.core.model.*
import com.bukuwarung.payments.data.model.*
import com.bukuwarung.payments.data.model.ppob.FirstPaymentTransaction
import retrofit2.Response
import retrofit2.http.*

interface PaymentsRemoteDataSource {
    @GET("/api/payments/accounts/metadata")
    suspend fun getMetadata(): ApiResponse<PaymentMetadata>

    @POST("/api/payments/accounts/{account_id}")
    suspend fun enabledMerchantPayments(@Path("account_id") accountId: String, @Body businessNameMap: Map<String, String>): ApiResponse<EnableCustomerResponse>

    @PATCH("/api/payments/accounts/{business_id}")
    suspend fun updateBusinessName(@Path("business_id") businessId: String, @Body businessNameMap: Map<String, String>):ApiResponse<Unit>

    @GET("/api/payments/{account_id}/bank_accounts")
    suspend fun getMerchantBankAccounts(
        @Path("account_id") accountId: String,
        @Query("type") type: String?
    ): ApiResponse<List<BankAccount>>

    @POST("/api/payments/{account_id}/bank_accounts/validate")
    suspend fun validateBankAccount(@Path("account_id") accountId: String, @Body bankValidationRequest: BankValidationRequest): ApiResponse<BankAccount>

    @POST("/api/payments/{account_id}/bank_accounts_v2/customer_accounts/add/{customer_id}")
    suspend fun validateAndAddCustomerBankAccount(
        @Path("account_id") accountId: String,
        @Path("customer_id") customerId: String,
        @Body bankValidationRequest: BankValidationRequest
    ): ApiResponse<AddBankAccountDetail>

    @POST("/api/payments/{account_id}/customers/{customer_id}/bank_accounts")
    suspend fun addCustomerBankAccount(@Path("account_id") accountId: String, @Path("customer_id") customerId: String, @Body bankAccount: BankAccount): ApiResponse<BankAccount>

    @GET("/api/payments/{account_id}/customers/{customer_id}/bank_accounts")
    suspend fun getCustomerBankAccounts(@Path("account_id") accountId: String, @Path("customer_id") customerId: String): ApiResponse<List<BankAccount>>

    @DELETE("/api/payments/{account_id}/customers/{customer_id}/bank_accounts/{bank_account_id}")
    suspend fun deleteCustomerBankAccount(@Path("account_id") accountId: String, @Path("customer_id") customerId: String, @Path("bank_account_id") bankAccountId: String): ApiResponse<Any>

    @POST("/api/payments/{account_id}/bank_accounts")
    suspend fun addMerchantBankAccount(@Path("account_id") accountId: String, @Body bankAccount: BankAccountRequest): ApiResponse<BankAccount>

    @DELETE("/api/payments/{account_id}/bank_accounts/{bank_account_id}")
    suspend fun deleteMerchantBankAccount(@Path("account_id") accountId: String, @Path("bank_account_id") bankAccountId: String): ApiResponse<Any>

    @POST("/api/payments/{account_id}/disbursements/{customer_id}/overview")
    suspend fun getDisbursementOverview(@Path("account_id") accountId: String, @Path("customer_id") customerId: String, @Body request: DisbursementOverviewRequest): ApiResponse<DisbursementOverviewResponse>

    @POST("/api/payments/{account_id}/disbursements/{customer_id}/")
    suspend fun createDisbursement(
            @Path("account_id") accountId: String,
            @Path("customer_id") customerId: String,
            @Body request: DisbursementOverviewRequest): ApiResponse<Disbursement>

    @GET("/api/payments/{account_id}/disbursements/{customer_id}/{request_id}")
    suspend fun getDisbursement(
            @Path("account_id") accountId: String,
            @Path("customer_id") customerId: String,
            @Path("request_id") requestId: String
    ): ApiResponse<Disbursement>

    @POST("/api/payments/{account_id}/collects/{customer_id}/overview")
    suspend fun getPaymentInOverview(@Path("account_id") accountId: String, @Path("customer_id") customerId: String, @Body request: PaymentOverviewRequest): ApiResponse<PaymentOverviewResponse>

//    @POST("/api/v2/payments/{account_id}/collects/{customer_id}")
    @POST("/api/payments/{account_id}/collects/{customer_id}")
    suspend fun requestPayment(
            @Path("account_id") accountId: String,
            @Path("customer_id") customerId: String,
            @Body paymentCollection: PaymentCollection
    ): ApiResponse<PaymentCollection>

    @GET("/api/payments/{account_id}/collects/{customer_id}/{request_id}")
    suspend fun getRequestedPayment(
            @Path("account_id") accountId: String,
            @Path("customer_id") customerId: String,
            @Path("request_id") requestId: String
    ): ApiResponse<PaymentCollection>

    @GET("/api/payments/banks/virtual_accounts")
    suspend fun getVirtualAccountBanks(): ApiResponse<List<Bank>>

    @GET("/api/payments/banks/disbursements")
    suspend fun getBanks(@Query("type") type: String?): ApiResponse<List<Bank>>

    @POST("/api/payments/health")
    suspend fun doHealthCheck(
            @Body paymentHealthCheckRequest: PaymentHealthCheckRequest
    ): ApiResponse<PaymentHealthCheckResponse>

    @PUT("/api/payments/{account_id}/collects/{customer_id}/{payment_request_id}/expire")
    suspend fun expirePayment(
            @Path("account_id") bookId: String,
            @Path("customer_id") customerId: String,
            @Path("payment_request_id") paymentId: String
    ): ApiResponse<Disbursement>

    @PUT("/api/payments/{account_id}/collects/{customer_id}/{payment_request_id}/agent_fee")
    suspend fun updatePaymentInAgentFee(@Path("account_id") accountId: String, @Path("customer_id") customerId: String,@Path("payment_request_id") paymentRequestId: String, @Body updateFeeRequest: UpdateFeeRequest):ApiResponse<PaymentCollection>

    @PUT("/api/payments/{account_id}/disbursements/{customer_id}/{disbursement_request_id}/agent_fee")
    suspend fun updatePaymentOutAgentFee(@Path("account_id") accountId: String, @Path("customer_id") customerId: String,@Path("disbursement_request_id") disbursementRequestId: String, @Body updateFeeRequest: UpdateFeeRequest):ApiResponse<Disbursement>

    @PUT("/payments/api/{account_id}/qris/disbursements/{external_id}/agent_fee")
    suspend fun updateQrisInAgentFee(
        @Path("account_id") accountId: String,
        @Path("external_id") orderId: String,
        @Body updateFeeRequest: UpdateFeeRequest
    ): ApiResponse<Void>

    @GET("/payments/api/categories")
    suspend fun getPaymentCategoryList(@Query("disbursableType") disbursableType: String): ApiResponse<List<PaymentCategory>>

    @PUT("api/payments/{account_id}/collects/{customer_id}/{payment_request_id}/update_payment_category")
    suspend fun updatePaymentInCategorySelected(@Path("account_id") accountId: String,@Path("customer_id") customerId: String,@Path("payment_request_id") paymentRequestId: String, @Body paymentCategoryIdMap: Map<String, String>): ApiResponse<Unit>

    @PUT("/api/payments/{account_id}/disbursements/{disbursement_request_id}/update_payment_category")
    suspend fun updatePaymentOutCategorySelected(@Path("account_id") accountId: String, @Path("disbursement_request_id") disbursementRequestId: String, @Body paymentCategoryIdMap: Map<String, String>): ApiResponse<Unit>

    @POST("/payments/api/{account_id}/qris/disbursements/{disbursable_id}/retry")
    suspend fun retryQrisPayment(
        @Path("account_id") accountId: String,
        @Path("disbursable_id") disbursableId: String
    ):ApiResponse<Any>

    @GET("/payments/api/{account_id}/checkout/methods")
    suspend fun getPaymentMethods(
        @Path("account_id") accountId: String,
        @Query("type") paymentType: String,
        @Query("receiver_bank_code") bankCode: String,
        @Query("transaction_amount") transactionAmount: Double
    ):ApiResponse<PaymentMethodsResponse>

    @GET("/payments/api/loyalties/all")
    suspend fun getLoyaltyTierDiscounts(): ApiResponse<List<LoyaltyTierDiscountsResponse>>
    
    @Headers("x-ops-token: SuperSecret")
    @GET("/data-analytics/api/v1/accounting/ppob/{account_id}")
    suspend fun fetchPpobProducts(
        @Path("account_id") accountId: String,
        @Query("from") startDate: String,
        @Query("to") endDate: String): ApiResponse<PpobProducts>

    @GET("/api/payments/{account_id}/histories/months/categories")
    suspend fun getPaymentCategoriesHistory(
        @Path("account_id") account_id: String,
        @Query("startDate") startDate: String,
        @Query("endDate") endDate: String
    ): ApiResponse<PaymentCategoriesHistoryResponse>

    @POST("/payments/api/disbursements/{disbursable_id}/retry")
    suspend fun retryDisbursal(
        @Path("disbursable_id") disbursableId: String,
        @Body requestBody: DisbursalRequest
    ): ApiResponse<Any>

    @GET("/api/payments/banks/processing_time")
    suspend fun getDisbursementTimings(
        @Query("bank_code") bankCode: String,
        @Query("created_at") createdAt: String?,
        @Query("amount") amount: Double?,
    ): ApiResponse<DisbursementTimings>

    @GET("/api/payments/{account_id}/collects/{customer_id}/transaction_limit")
    suspend fun getPaymentInLimits(
        @Path("account_id") accountId: String,
        @Path("customer_id") customerId: String,
    ): ApiResponse<PaymentTransactionLimits>

    @GET("/api/payments/{account_id}/disbursements/{customer_id}/transaction_limit")
    suspend fun getPaymentOutLimits(
        @Path("account_id") accountId: String,
        @Path("customer_id") customerId: String,
    ): ApiResponse<PaymentTransactionLimits>

    @GET("data-analytics/api/v1/account/{account_id}/get-first-transcations")
    suspend fun getFirstTransaction(@Path("account_id") accountId: String): ApiResponse<FirstPaymentTransaction>

    @GET("/api/payments/accounts/payment-limit-config")
    suspend fun getPaymentLimits(): ApiResponse<PaymentLimits>

    @GET("/api/payments/{account_id}/bank_accounts_v2/customer_accounts/recents")
    suspend fun getRecentBankAccounts(
        @Path("account_id") accountId: String,
        @Query("page") page: Int,
        @Query("count") count: Int
    ): ApiResponse<BankAccountList>

    @GET("/api/payments/{account_id}/bank_accounts_v2/customer_accounts/favourites")
    suspend fun getFavouriteBankAccounts(
        @Path("account_id") accountId: String,
        @Query("page") page: Int,
        @Query("count") count: Int
    ): ApiResponse<BankAccountList>

    @GET("/api/payments/{account_id}/bank_accounts_v2/customer_accounts/search")
    suspend fun getSearchBankAccounts(
        @Path("account_id") accountId: String,
        @Query("page") page: Int,
        @Query("count") count: Int,
        @Query("searchTerm") searchTerm: String
    ): ApiResponse<SearchBankAccountList>

    @PUT("api/payments/{account_id}/bank_accounts_v2/customer_accounts/add_favourite/{id}")
    suspend fun addFavourite(
        @Path("account_id") accountId: String,
        @Path("id") id: String
    ): ApiResponse<BankAccountResponse>

    @DELETE("api/payments/{account_id}/bank_accounts_v2/customer_accounts/remove_favourite/{id}")
    suspend fun removeFavourite(
        @Path("account_id") accountId: String,
        @Path("id") id: String
    ): ApiResponse<BankAccountResponse>

    @DELETE("api/payments/{account_id}/bank_accounts_v2/customer_accounts/remove_account/{id}")
    suspend fun removeBankAccount(
        @Path("account_id") accountId: String,
        @Path("id") id: String
    ): ApiResponse<BankAccountResponse>

    @GET("/ac/api/v2/edc/device-detail")
    suspend fun fetchUserDetails(): ApiResponse<UserDetail>

}
