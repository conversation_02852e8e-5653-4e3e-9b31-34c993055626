package com.bukuwarung.payments.data.model.ppob

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class ReminderFilterResponse(

        @field:SerializedName("filters_group")
        val filtersGroup: List<FiltersGroupItem>? = null
) : Parcelable

@Parcelize
data class FiltersGroupItem(

        @field:SerializedName("id")
        val id: String? = null,

        @field:SerializedName("filters")
        val filters: List<FiltersItem>? = null,

        @field:SerializedName("display_name")
        val displayName: String? = null
) : Parcelable

@Parcelize
data class FiltersItem(

        @field:SerializedName("action_value")
        val actionValues: List<String>? = null,

        @field:SerializedName("action_param")
        val actionParam: String? = null,

        @field:SerializedName("id")
        val id: String? = null,

        @field:SerializedName("display_name")
        val displayName: String? = null,
        var isSelected: Boolean = true
) : Parcelable
