package com.bukuwarung.payments.data.model.ppob

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class PpobProductInfo(
    @SerializedName("biller_name")
    val billerName: String?,
    @SerializedName("biller")
    val biller: String,
    @SerializedName("is_popular")
    val isPopular: String,
    @SerializedName("type")
    val type: String,
    @SerializedName("logo")
    val logo: String?,
    @SerializedName("validation_regex")
    val validationRegex: String?,
    @SerializedName("error_message")
    val errorMessage: String?,
    @SerializedName("is_freeform")
    val isFreeForm: Boolean?
):Parcelable
