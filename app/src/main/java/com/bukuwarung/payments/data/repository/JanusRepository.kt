package com.bukuwarung.payments.data.repository

import com.bukuwarung.data.restclient.ApiResponse
import com.bukuwarung.payments.data.model.KycStatusResponse
import javax.inject.Inject


open class JanusRepository @Inject constructor(val remoteDataSource: JanusRemoteDataSource) : JanusRemoteRepository {
    override suspend fun createKycAccount(): ApiResponse<KycStatusResponse> {
        return remoteDataSource.createKycAccount()
    }

    override suspend fun getKycAccount(accountId: String) = remoteDataSource.getKycAccount(accountId)
}

interface JanusRemoteRepository {
    suspend fun createKycAccount(): ApiResponse<KycStatusResponse>
    suspend fun getKycAccount(accountId: String): ApiResponse<KycStatusResponse>
}
