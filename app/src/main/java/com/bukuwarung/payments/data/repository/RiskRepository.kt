package com.bukuwarung.payments.data.repository

import com.bukuwarung.data.restclient.ApiResponse
import com.bukuwarung.payments.data.model.BookValidationRequest
import com.bukuwarung.payments.data.model.WhitelistGroup


class RiskRepository(private val remoteRemoteDataSource: RiskRemoteDataSource) : RiskRemoteRepository {

    override suspend fun validateBookName(requestBody: BookValidationRequest): ApiResponse<Any> =
        remoteRemoteDataSource.validateBookName(requestBody)

    override suspend fun getWhitelistGroups(groupCode: String) =
        remoteRemoteDataSource.getWhitelistGroups(groupCode)
}

interface RiskRemoteRepository {
    suspend fun validateBookName(requestBody: BookValidationRequest): ApiResponse<Any>

    suspend fun getWhitelistGroups(groupCode: String): ApiResponse<List<WhitelistGroup>>
}