package com.bukuwarung.payments.data.repository

import com.bukuwarung.database.dao.BankAccountDao
import com.bukuwarung.database.entity.BankAccount
import javax.inject.Inject

class BankAccountDataStore @Inject constructor(val dao: BankAccountDao) : BankAccountLocalDataStore {
    override suspend fun getBankAccounts(bookId: String?): List<BankAccount>? {
        return dao.getBankAccounts(bookId)
    }

    override fun getCustomerBankAccounts(bookId: String, customerId: String) =
        dao.getCustomerBankAccounts(bookId, customerId)

    override fun getCustomerBankAccountsByAccountNumber(bookId: String, accountNumber: String) =
        dao.getCustomerBankAccountsByAccountNumber(bookId, accountNumber)

    override suspend fun insertBankAccounts(list: List<BankAccount>) {
        dao.insert(list)
    }

    override suspend fun deleteMerchantBankAccounts(bookId: String?) {
        dao.deleteMerchantBankAccounts(bookId)
    }

    override suspend fun deleteCustomerBankAccount(bookId: String, customerId: String, bankAccountId: String) {
        dao.deleteCustomerBankAccount(bookId, customerId, bankAccountId)
    }
}

interface BankAccountLocalDataStore {
    suspend fun insertBankAccounts(list: List<BankAccount>)
    suspend fun deleteMerchantBankAccounts(bookId: String?)
    suspend fun deleteCustomerBankAccount(bookId: String, customerId: String, bankAccountId: String)
    suspend fun getBankAccounts(bookId: String?): List<BankAccount>?
    fun getCustomerBankAccounts(bookId: String, customerId: String): List<BankAccount>?

    fun getCustomerBankAccountsByAccountNumber(bookId: String, accountNumber: String): List<BankAccount>?
}
