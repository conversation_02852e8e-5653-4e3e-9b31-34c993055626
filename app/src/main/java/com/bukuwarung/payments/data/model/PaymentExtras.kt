package com.bukuwarung.payments.data.model

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class PaymentExtras(
        @SerializedName("category_id")
        val categoryId: String? = null,
        @SerializedName("record_in")
        val recordIn: String? = null,
        @SerializedName("accounting")
        val accounting: PaymentAccountingExtras? = null,
        @SerializedName("source")
        val source: String? = ""
): Parcelable
