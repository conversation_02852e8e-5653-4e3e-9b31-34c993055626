package com.bukuwarung.payments.data.model

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class PaymentBannerInfoResponse(
        @SerializedName("id")
        val id: String? = null,
        @SerializedName("title")
        val title: String? = null,
        @SerializedName("image_url")
        val imageUrl: String? = null,
        @SerializedName("web_landing_url")
        val webLandingUrl: String? = null,
        @SerializedName("mobile_landing_url")
        var mobileLandingUrl: String?,
        @SerializedName("meta")
        val meta: PaymentBannerMetaInfo?=null
) : Parcelable
