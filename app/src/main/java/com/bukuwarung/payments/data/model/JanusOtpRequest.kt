package com.bukuwarung.payments.data.model

import com.google.gson.annotations.SerializedName

data class JanusOtpRequest(
    @SerializedName("countryCode")
    var countryCode: String? = null,
    @SerializedName("userId")
    var userId: String? = null,
    @SerializedName("otp")
    var otp: String? = null,
    @SerializedName("clientId")
    val clientId: String? = null,
    @SerializedName("clientSecret")
    val clientSecret: String? = null
)