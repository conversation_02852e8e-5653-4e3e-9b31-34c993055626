package com.bukuwarung.payments.data.model

import android.os.Parcelable
import androidx.annotation.Keep
import com.bukuwarung.payments.constants.PpobConst
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Keep
@Parcelize
data class FinproAddCartRequest(
    @SerializedName("sku")
    val sku: String,
    @SerializedName("beneficiary")
    val beneficiary: FinproBeneficiary,
    @SerializedName("nik")
    val frameNumber: String? = null,
    @SerializedName("machine_number")
    val machineNumber: String? = null,
    @SerializedName("month")
    val month: Int? = null,
    @SerializedName("user_type")
    val userType: String = PpobConst.USERTYPE_CONTROL,
    @SerializedName("amount")
    val amount: Double? = null
) : Parcelable
