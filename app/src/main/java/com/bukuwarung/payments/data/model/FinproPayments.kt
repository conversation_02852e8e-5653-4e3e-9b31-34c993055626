package com.bukuwarung.payments.data.model

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize
import java.io.Serializable

@Parcelize
data class FinproPayments(
        @SerializedName("payment_id")
        val paymentId: String,
        @SerializedName("payment_url")
        val paymentUrl: String? = null,
        @SerializedName("checkout_url")
        val checkoutUrl: String? = null,
        @SerializedName("amount")
        val amount: Double,
        @SerializedName("expired_at")
        val expiredAt: String? = null,
        @SerializedName("invoice_payment")
        val invoicePayment: Boolean? = null,
        @SerializedName("payment_method")
        val paymentMethod: FinproPaymentMethod?,
        @SerializedName("destination_bank_information")
        val destinationBankInformation: DestinationBankInformation? = null
) : Parcelable, Serializable
