package com.bukuwarung.payments.data.model

import com.bukuwarung.constants.PaymentConst
import com.google.gson.annotations.SerializedName


data class BankAccountRequest(
    @SerializedName("bank_code")
    val bankCode: String? = "",

    @SerializedName("account_number")
    val accountNumber: String? = null,

    @SerializedName("is_qris_bank")
    var isQrisBank: Boolean? = false,

    @SerializedName("proposed_qris_bank")
    var proposedQrisBank: Boolean? = false,

    @SerializedName("account_owner")
    var accountOwner: PaymentConst.BankAccountOwner? = PaymentConst.BankAccountOwner.SELF
)