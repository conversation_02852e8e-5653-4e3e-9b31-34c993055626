package com.bukuwarung.activities.businessdashboard.viewmodel

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.MutableLiveData
import androidx.paging.LivePagedListBuilder
import androidx.paging.PagedList
import com.bukuwarung.activities.expense.adapter.dataholder.CashDataHolder
import com.bukuwarung.activities.expense.adapter.dataholder.TutorialVideoDataHolder
import com.bukuwarung.activities.expense.adapter.model.DailySummary
import com.bukuwarung.activities.expense.filter.CashTabFilter
import com.bukuwarung.activities.expense.sort.SortOrder
import com.bukuwarung.activities.superclasses.DataHolder
import com.bukuwarung.activities.superclasses.DataHolder.*
import com.bukuwarung.activities.transactionreport.DateRange
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.analytics.AppAnalytics.PropBuilder
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.database.dto.BookSummaryModel
import com.bukuwarung.database.dto.CashTransactionDto
import com.bukuwarung.database.entity.BookEntity
import com.bukuwarung.database.repository.BusinessRepository
import com.bukuwarung.database.repository.CashRepository
import com.bukuwarung.database.repository.CustomerRepository
import com.bukuwarung.database.repository.TransactionRepository
import com.bukuwarung.inventory.usecases.ProductInventory
import com.bukuwarung.preference.FeaturePrefManager
import com.bukuwarung.session.User
import com.bukuwarung.utils.DateTimeUtils
import com.bukuwarung.utils.Utility
import java.lang.Exception
import java.text.SimpleDateFormat
import java.util.*
import kotlin.collections.ArrayList

class BusinessCashListViewModel(application: Application) : AndroidViewModel(application) {

//    val TAG = "CashListViewModel"
//    private var trxCountLiveData: LiveData<Int>? = null
//    private var customerRepository: CustomerRepository? = null
//    private var trxCount = 0
//    private var cashRepository: CashRepository? = null
//    private var businessLiveData: LiveData<BookEntity>? = null
//    private val bookSummaryModel = MutableLiveData<BookSummaryModel>()
//    var cashList: PagedList<CashTransactionDto>? = null
//    var cashListLiveData: LiveData<PagedList<DataHolder>>? = null
//    private val liveDataMerger = MediatorLiveData<List<DataHolder>>()
//    var productInventory: ProductInventory? = null
//
//    var rangedCashList: LiveData<PagedList<CashTransactionDto>>? = null
//    private val rangedCash = MediatorLiveData<List<CashTransactionDto>>()
//    var cashVal = MediatorLiveData<PagedList<CashTransactionDto>>()
//    private var searchQuery: String? = null
//    private var sortOrder = 0
//    private var transactionFilter = 0
//    private var typeFilter = CashTabFilter.BY_DATE
//    var dataloaded = false
//
//
//    var currentMonth = 0
//    var currentYear = 0
//
//    var startDate = ""
//    var endDate = ""
//
//    fun BusinessCashListViewModel(application: Application, productInventory: ProductInventory?) {
//        cashRepository = CashRepository.getInstance(application)
//        sortOrder = FeaturePrefManager.getInstance().cashListSortOrder
//        transactionFilter = FeaturePrefManager.getInstance().selectedCashFilter
//        trxCountLiveData =
//            TransactionRepository.getInstance(application).getTransactionCountLiveData(
//                User.getUserId()
//            )
//        trxCount =
//            TransactionRepository.getInstance(application).countAllCashTrans(User.getBusinessId())
//        currentMonth = Utility.getCurrentMonthInt()
//        currentYear = Utility.getCurrentYearInt()
//        businessLiveData =
//            BusinessRepository.getInstance(application).getBusinessById(User.getBusinessId())
//        rangedCashList = LivePagedListBuilder(
//            cashRepository!!.getAllCashTransactionsWithDateRangePaging(
//                User.getBusinessId(), startDate, endDate
//            ), 2000000
//        ).build()
//        cashVal.addSource(
//            rangedCashList!!
//        ) { list: PagedList<CashTransactionDto> ->
//            cashList = list
//            cashVal.setValue(list)
//        }
//        customerRepository = CustomerRepository.getInstance(application)
//        this.productInventory = productInventory
//    }
//
//    fun getBookSummaryModel(): MutableLiveData<BookSummaryModel> {
//        return bookSummaryModel
//    }
//
//    fun getDataHolderList(): LiveData<List<DataHolder>> {
//        return liveDataMerger
//    }
//
//    fun getCashList(): List<CashTransactionDto>? {
//        return cashList
//    }
//
//    fun getTrxCountLiveData(): LiveData<Int>? {
//        return trxCountLiveData
//    }
//
//    fun setSearchQuery(str: String?): List<DataHolder> {
//        searchQuery = str
//        return refresh()
//    }
//
//    fun setDateRange(range: DateRange?): List<DataHolder>? {
//        if (range == null || startDate == range.startDate || endDate == range.endDate) return null
//        startDate = range.startDate
//        endDate = range.endDate
//        rangedCashList = LivePagedListBuilder(
//            cashRepository!!.getAllCashTransactionsWithDateRangePaging(
//                User.getBusinessId(), startDate, endDate
//            ), 20000
//        ).build()
//        cashVal.addSource(
//            rangedCashList!!
//        ) { list: PagedList<CashTransactionDto> ->
//            cashList = list
//            cashVal.setValue(list)
//        }
//        trxCount = cashRepository!!.getCountCashTransaction(User.getBusinessId())
//        return refresh()
//    }
//
//    fun setSortOrder(i: Int): List<DataHolder> {
//        sortOrder = i
//        return refresh()
//    }
//
//    fun setTransactionFilter(i: Int): List<DataHolder> {
//        transactionFilter = i
//        return refresh()
//    }
//
//    fun setTypeFilter(i: Int): List<DataHolder> {
//        typeFilter = i
//        return refresh()
//    }
//
//    fun getBusinessLiveData(): LiveData<BookEntity>? {
//        return businessLiveData
//    }
//
//
//    fun refreshDateFilter(cashList: List<CashTransactionDto>?): List<DataHolder> {
//        var arrayList = ArrayList<CashTransactionDto>()
//        if (cashList != null) {
//            arrayList.addAll(cashList)
//        }
//        if (Utility.isBlank(searchQuery)) {
//            arrayList = getFilteredList(arrayList)!!
//        }
//        var dataMap = LinkedHashMap<String?, MutableList<CashTransactionDto>?>()
//        sortCashList(arrayList, SortOrder.MOST_RECENT)
//        val searchInCashs = searchInCashs(arrayList, searchQuery!!)
//        val resultList = ArrayList<DataHolder>()
//        val summaryModel = BookSummaryModel(0.0, 0.0)
//        if (searchInCashs != null && !searchInCashs.isEmpty()) {
//            val summaryExpenseMap: MutableMap<String, Double?> = HashMap()
//            val summaryIncomeMap: MutableMap<String, Double?> = HashMap()
//            val summaryExistMap: MutableMap<String, Boolean> = HashMap()
//            for (cashRowHolder in searchInCashs) {
//                if (cashRowHolder == null) continue
//                if (DateTimeUtils.isSameMonth(
//                        cashRowHolder.transactionDate,
//                        currentMonth, currentYear
//                    )
//                ) {
//                    if (cashRowHolder.transactionAmount > 0) {
//                        summaryModel.amountIn += cashRowHolder.transactionAmount
//                        summaryModel.amountOut += -1 * cashRowHolder.buyingPrice
//                    } else {
//                        summaryModel.amountOut += cashRowHolder.transactionAmount
//                    }
//                }
//                val id = getGroupId(cashRowHolder)
//                if (!summaryExpenseMap.containsKey(id)) {
//                    summaryExpenseMap[id] = 0.0
//                    summaryExistMap[id] = true
//                }
//                if (!summaryIncomeMap.containsKey(id)) {
//                    summaryIncomeMap[id] = 0.0
//                    summaryExistMap[id] = true
//                }
//                if (cashRowHolder.transactionAmount <= 0) {
//                    if (summaryExpenseMap.containsKey(id)) {
//                        val total = cashRowHolder.transactionAmount + summaryExpenseMap[id]!!
//                        summaryExpenseMap[id] = total
//                    } else {
//                        summaryExpenseMap[id] = cashRowHolder.transactionAmount
//                        summaryExistMap[id] = true
//                    }
//                } else {
//                    if (summaryIncomeMap.containsKey(id)) {
//                        val total = cashRowHolder.transactionAmount + summaryIncomeMap[id]!!
//                        summaryIncomeMap[id] = total
//                    } else {
//                        summaryIncomeMap[id] = cashRowHolder.transactionAmount
//                        summaryExistMap[id] = true
//                    }
//
//                    // add buyingprice if any
//                    if (cashRowHolder.buyingPrice > 0) {
//                        if (summaryExpenseMap.containsKey(id)) {
//                            val total = summaryExpenseMap[id]!! - cashRowHolder.buyingPrice
//                            summaryExpenseMap[id] = total
//                        } else {
//                            summaryExpenseMap[id] = cashRowHolder.buyingPrice
//                            summaryExistMap[id] = true
//                        }
//                    }
//                }
//                if (dataMap[id] == null) {
//                    val dataList: MutableList<CashTransactionDto> = ArrayList()
//                    dataMap[id] = dataList
//                }
//                dataMap[id]!!.add(cashRowHolder)
//            }
//            if (sortOrder == SortOrder.MOST_OUT || sortOrder == SortOrder.LEAST_OUT) {
//                dataMap = sortedMap(sortByComparator(summaryExpenseMap, sortOrder), dataMap)
//            } else if (sortOrder == SortOrder.MOST_IN || sortOrder == SortOrder.LEAST_IN) {
//                dataMap = sortedMap(sortByComparator(summaryIncomeMap, sortOrder), dataMap)
//            }
//            for ((_, iterableData) in dataMap) {
//                if (sortOrder == SortOrder.MOST_RECENT) {
//                    sortCashListByDate(iterableData, sortOrder)
//                } else {
//                    sortCashList(iterableData, sortOrder)
//                }
//                for (cashRowHolder in iterableData!!) {
//                    val id = getGroupId(cashRowHolder)
//                    if (summaryExistMap[id]!!) {
//                        val expenseVal =
//                            if (summaryExpenseMap[id] == null) "0" else summaryExpenseMap[id].toString()
//                        val incomeVal =
//                            if (summaryIncomeMap[id] == null) "0" else summaryIncomeMap[id].toString()
//                        val dailySummary = DailySummary(
//                            id,
//                            expenseVal,
//                            incomeVal,
//                            cashRowHolder.cashCategoryId,
//                            cashRowHolder.type
//                        )
//                        val dayRowHolder = DayDataHolder(dailySummary)
//                        resultList.add(dayRowHolder)
//                        summaryExistMap[id] = false
//                    }
//                    resultList.add(CashDataHolder(cashRowHolder))
//                }
//            }
//            resultList.add(LastRowHolder())
//        } else {
//            if (Utility.isBlank(searchQuery) && trxCount == 0) {
//                resultList.add(TutorialVideoDataHolder())
//            } else if ((!Utility.isBlank(startDate) || !Utility.isBlank(endDate)) && Utility.isBlank(
//                    searchQuery
//                )
//            ) {
//                resultList.add(NoFilterResultRowHolder())
//            } else {
//                resultList.add(NoResultRowHolder())
//            }
//        }
//
////        this.bookSummaryModel.setValue(summaryModel);
//        bookSummaryModel.setValue(BookSummaryModel(cashList))
//        liveDataMerger.setValue(resultList)
//        return resultList
//    }
//
//    private fun sortedMap(
//        amountMap: Map<String, Double?>,
//        map: Map<String?, MutableList<CashTransactionDto>?>
//    ): LinkedHashMap<String?, MutableList<CashTransactionDto>?> {
//        val sortedMap = LinkedHashMap<String?, MutableList<CashTransactionDto>?>()
//        for ((key) in amountMap) {
//            sortedMap[key] = map[key]
//        }
//        return sortedMap
//    }
//
//    private fun sortByComparator(
//        unsortMap: Map<String, Double?>,
//        order: Int
//    ): Map<String, Double?> {
//        val list: List<Map.Entry<String, Double?>> = LinkedList(unsortMap.entries)
//        Collections.sort(
//            list
//        ) { o1: Map.Entry<String, Double?>, o2: Map.Entry<String, Double?> ->
//            if (order == SortOrder.MOST_OUT || order == SortOrder.LEAST_IN) {
//                return@sort o1.value!!.compareTo(o2.value!!)
//            } else if (order == SortOrder.MOST_IN || order == SortOrder.LEAST_OUT) {
//                return@sort o2.value!!.compareTo(o1.value!!)
//            } else {
//                return@sort o1.value!!.compareTo(o2.value!!)
//            }
//        }
//
//        // Maintaining insertion order with the help of LinkedList
//        val sortedMap: MutableMap<String, Double?> = LinkedHashMap()
//        for ((key, value) in list) {
//            sortedMap[key] = value
//        }
//        return sortedMap
//    }
//
//
//    fun refresh(): List<DataHolder> {
//        val allList = cashRepository!!.getAllCashTransactionsWithDateRange(
//            User.getBusinessId(),
//            startDate,
//            endDate
//        )
//        rangedCash.setValue(allList)
//        trxCount = cashRepository!!.getCountCashTransaction(User.getBusinessId())
//        if (typeFilter == CashTabFilter.BY_DATE) {
//            return refreshDateFilter(allList)
//        }
//        var arrayList = ArrayList<CashTransactionDto>()
//        if (cashList != null) {
//            arrayList.addAll(cashList!!)
//        }
//        if (Utility.isBlank(searchQuery)) {
//            arrayList = getFilteredList(arrayList)!!
//        }
//        sortCashList(arrayList, sortOrder)
//        val searchInCashs = searchQuery?.let { searchInCashs(arrayList, it) }
//        val resultList = ArrayList<DataHolder>()
//        val summaryModel = BookSummaryModel(0.0, 0.0)
//        if (searchInCashs != null && !searchInCashs.isEmpty()) {
//            val summaryExpenseMap: MutableMap<String, Double?> = HashMap()
//            val summaryIncomeMap: MutableMap<String, Double?> = HashMap()
//            val summaryExistMap: MutableMap<String, Boolean> = HashMap()
//            val summaryCountMap: MutableMap<String, Int?> = HashMap()
//            val summaryBuyingPrice: MutableMap<String, Double?> = HashMap()
//            for (cashRowHolder in searchInCashs) {
//                if (typeFilter == CashTabFilter.BY_CATEGORY) {
//                    if (cashRowHolder.transactionAmount <= 0) {
//                        if (summaryExpenseMap.containsKey(cashRowHolder.cashCategoryId)) {
//                            val total =
//                                cashRowHolder.transactionAmount + summaryExpenseMap[cashRowHolder.cashCategoryId]!!
//                            summaryExpenseMap[cashRowHolder.cashCategoryId] = total
//                            val old = summaryCountMap[cashRowHolder.cashCategoryId]!!
//                            summaryCountMap[cashRowHolder.cashCategoryId] = old + 1
//                        } else {
//                            summaryExpenseMap[cashRowHolder.cashCategoryId] =
//                                cashRowHolder.transactionAmount
//                            summaryExistMap[cashRowHolder.cashCategoryId] = true
//                            summaryCountMap[cashRowHolder.cashCategoryId] = 1
//                        }
//                    } else {
//                        if (summaryIncomeMap.containsKey(cashRowHolder.cashCategoryId)) {
//                            val total =
//                                cashRowHolder.transactionAmount + summaryIncomeMap[cashRowHolder.cashCategoryId]!!
//                            summaryIncomeMap[cashRowHolder.cashCategoryId] = total
//                            val old = summaryCountMap[cashRowHolder.cashCategoryId]!!
//                            summaryCountMap[cashRowHolder.cashCategoryId] = old + 1
//                        } else {
//                            summaryIncomeMap[cashRowHolder.cashCategoryId] =
//                                cashRowHolder.transactionAmount
//                            summaryExistMap[cashRowHolder.cashCategoryId] = true
//                            summaryCountMap[cashRowHolder.cashCategoryId] = 1
//                        }
//                        if (summaryBuyingPrice.containsKey(cashRowHolder.cashCategoryId)) {
//                            val totalBuyingPrice =
//                                cashRowHolder.buyingPrice + summaryBuyingPrice[cashRowHolder.cashCategoryId]!!
//                            summaryBuyingPrice[cashRowHolder.cashCategoryId] = totalBuyingPrice
//                        } else {
//                            summaryBuyingPrice[cashRowHolder.cashCategoryId] =
//                                cashRowHolder.buyingPrice
//                        }
//                    }
//                    if (DateTimeUtils.isSameMonth(
//                            cashRowHolder.transactionDate,
//                            currentMonth, currentYear
//                        )
//                    ) {
//                        if (cashRowHolder.transactionAmount > 0 && CashTabFilter.IN == transactionFilter) {
//                            summaryModel.amountIn += cashRowHolder.transactionAmount
//                            summaryModel.amountOut += -1 * cashRowHolder.buyingPrice
//                        } else if (cashRowHolder.transactionAmount < 0 && CashTabFilter.OUT == transactionFilter) {
//                            summaryModel.amountOut += cashRowHolder.transactionAmount
//                        } else {
//                            if (cashRowHolder.transactionAmount > 0) {
//                                summaryModel.amountIn += cashRowHolder.transactionAmount
//                                summaryModel.amountOut += -1 * cashRowHolder.buyingPrice
//                            } else {
//                                summaryModel.amountOut += cashRowHolder.transactionAmount
//                            }
//                        }
//                    }
//                } else {
//                    val id = getGroupId(cashRowHolder)
//                    if (cashRowHolder.transactionAmount <= 0) {
//                        if (summaryExpenseMap.containsKey(id)) {
//                            val total = cashRowHolder.transactionAmount + summaryExpenseMap[id]!!
//                            summaryExpenseMap[id] = total
//                        } else {
//                            summaryExpenseMap[id] = cashRowHolder.transactionAmount
//                            summaryExistMap[id] = true
//                        }
//                    } else {
//                        if (summaryIncomeMap.containsKey(id)) {
//                            val total = cashRowHolder.transactionAmount + summaryIncomeMap[id]!!
//                            summaryIncomeMap[id] = total
//                        } else {
//                            summaryIncomeMap[id] = cashRowHolder.transactionAmount
//                            summaryExistMap[id] = true
//                        }
//                    }
//                }
//            }
//            for (cashRowHolder in searchInCashs) {
//                if (typeFilter == CashTabFilter.BY_DATE) {
//                    val id = getGroupId(cashRowHolder)
//                    if (summaryExistMap[id]!!) {
//                        val expenseVal =
//                            if (summaryExpenseMap[id] == null) "0" else summaryExpenseMap[id].toString()
//                        val incomeVal =
//                            if (summaryIncomeMap[id] == null) "0" else summaryIncomeMap[id].toString()
//                        val dailySummary = DailySummary(
//                            id,
//                            expenseVal,
//                            incomeVal,
//                            cashRowHolder.cashCategoryId,
//                            cashRowHolder.type
//                        )
//                        val dayRowHolder = DayDataHolder(dailySummary)
//                        resultList.add(dayRowHolder)
//                        summaryExistMap[id] = false
//                    }
//                    resultList.add(CashDataHolder(cashRowHolder))
//                } else {
//                    if (summaryExistMap[cashRowHolder.cashCategoryId]!!) {
//                        val expenseVal =
//                            if (summaryExpenseMap[cashRowHolder.cashCategoryId] == null) "0" else summaryExpenseMap[cashRowHolder.cashCategoryId].toString()
//                        val incomeVal =
//                            if (summaryIncomeMap[cashRowHolder.cashCategoryId] == null) "0" else summaryIncomeMap[cashRowHolder.cashCategoryId].toString()
//                        val buyingPrice =
//                            if (summaryBuyingPrice[cashRowHolder.cashCategoryId] == null) "0" else summaryBuyingPrice[cashRowHolder.cashCategoryId].toString()
//                        val dailySummary = DailySummary(
//                            cashRowHolder.categoryName,
//                            expenseVal,
//                            incomeVal,
//                            buyingPrice,
//                            cashRowHolder.cashCategoryId,
//                            cashRowHolder.type
//                        )
//                        if (summaryCountMap.containsKey(cashRowHolder.cashCategoryId)) dailySummary.trxCount =
//                            summaryCountMap[cashRowHolder.cashCategoryId]!!
//                        val categoryRowHolder = CategoryRowHolder(dailySummary)
//                        resultList.add(categoryRowHolder)
//                        summaryExistMap[cashRowHolder.cashCategoryId] = false
//                    }
//                }
//            }
//            resultList.add(LastRowHolder())
//        } else {
//            if (Utility.isBlank(searchQuery) && trxCount == 0) {
//                resultList.add(TutorialVideoDataHolder())
//            } else if ((!Utility.isBlank(startDate) || !Utility.isBlank(endDate)) && Utility.isBlank(
//                    searchQuery
//                )
//            ) {
//                resultList.add(NoFilterResultRowHolder())
//            } else {
//                resultList.add(NoResultRowHolder())
//            }
//        }
//        //        this.bookSummaryModel.setValue(summaryModel);
//        bookSummaryModel.setValue(BookSummaryModel(cashList))
//        liveDataMerger.setValue(resultList)
//        return resultList
//    }
//
//    private fun getGroupId(info: CashTransactionDto): String {
//        return if (transactionFilter == CashTabFilter.DAILY) {
//            try {
//                val simpleDateFormat = SimpleDateFormat("yyyy-MM-dd", Locale.US)
//                val date = simpleDateFormat.parse(info.transactionDate)
//                SimpleDateFormat("dd MMM yyyy", Locale.US).format(date)
//            } catch (e: Exception) {
//                e.printStackTrace()
//                info.transactionDate
//            }
//        } else if (transactionFilter == CashTabFilter.WEEKLY) {
//            DateTimeUtils.getWeekBoundStr(info.transactionDate)
//        } else {
//            try {
//                val simpleDateFormat = SimpleDateFormat("yyyy-MM-dd", Locale.US)
//                val date = simpleDateFormat.parse(info.transactionDate)
//                SimpleDateFormat("MMMM yyyy", Locale("ID", "id")).format(date)
//            } catch (e: Exception) {
//                e.printStackTrace()
//                info.transactionDate
//            }
//        }
//    }
//
//    /**
//     * sort list of CashTransactionDto by updatedAt date.
//     * It can be used to sort list of transaction for a date group
//     * Data Map : dataMap('2020-07-06') = [transaction 1, transaction2, transcation3]
//     * return transaction list in sorted order
//     *
//     * @param list list of transaction for a date group
//     * @param i    sorting order
//     */
//    private fun sortCashListByDate(list: List<CashTransactionDto>?, i: Int) {
//        if (!(list == null || list.isEmpty())) {
//            Collections.sort(
//                list,
//                Comparator { cashEntity: CashTransactionDto, cashEntity2: CashTransactionDto ->
//                    val modifiedDate2 = cashEntity2.updatedAt
//                    val modifiedDate1 = cashEntity.updatedAt
//                    if (modifiedDate2.compareTo(modifiedDate1) > 0) 1 else if (modifiedDate2 == modifiedDate1
//                    ) 0 else -1
//                } as Comparator<CashTransactionDto>)
//        }
//    }
//
//    private fun sortCashList(list: List<CashTransactionDto>?, i: Int) {
//        if (!(list == null || list.isEmpty())) {
//            if (i == SortOrder.MOST_RECENT) {
//                Collections.sort(
//                    list,
//                    Comparator { cashEntity: CashTransactionDto, cashEntity2: CashTransactionDto ->
//                        try {
//                            val modifiedDate2 = cashEntity2.transactionDate
//                            val modifiedDate1 = cashEntity.transactionDate
//                            return@Comparator when {
//                                modifiedDate2 > modifiedDate1 -> 1
//                                modifiedDate2 == modifiedDate1 -> 0
//                                else -> -1
//                            }
//                        } catch (ex: Exception) {
//                            // error in parsing
//                            return@Comparator 0
//                        }
//                    } as Comparator<CashTransactionDto>)
//            } else if (i == SortOrder.MOST_OUT || i == SortOrder.LEAST_IN) {
//                Collections.sort(
//                    list,
//                    label@ Comparator { cashEntity: CashTransactionDto, cashEntity2: CashTransactionDto ->
//                        try {
//                            val modifiedDate2 = cashEntity2.transactionAmount
//                            val modifiedDate1 = cashEntity.transactionAmount
//                            return@label if (modifiedDate2.compareTo(modifiedDate1) < 0) 1 else if (modifiedDate2 == modifiedDate1
//                            ) 0 else -1
//                        } catch (ex: Exception) {
//                            // error in parsing
//                            return@label 0
//                        }
//                    } as Comparator<CashTransactionDto>)
//            } else if (i == SortOrder.LEAST_OUT || i == SortOrder.MOST_IN) {
//                Collections.sort(
//                    list,
//                    label@ Comparator { cashEntity: CashTransactionDto, cashEntity2: CashTransactionDto ->
//                        try {
//                            val modifiedDate2 = cashEntity2.transactionAmount
//                            val modifiedDate1 = cashEntity.transactionAmount
//                            return@label if (modifiedDate2.compareTo(modifiedDate1) > 0) 1 else if (modifiedDate2 == modifiedDate1
//                            ) 0 else -1
//                        } catch (ex: Exception) {
//                            // error in parsing
//                            return@label 0
//                        }
//                    } as Comparator<CashTransactionDto>)
//            } else if (i == SortOrder.NAME_ASC) {
//                Collections.sort(
//                    list,
//                    label@ Comparator { cashEntity: CashTransactionDto, cashEntity2: CashTransactionDto ->
//                        try {
//                            val name2 = cashEntity2.categoryName
//                            val name1 = cashEntity.categoryName
//                            if (name2 != null && name1 != null) {
//                                return@label name1.lowercase().compareTo(name2.lowercase())
//                            }
//                            return@label -1
//                        } catch (ex: Exception) {
//                            // error in parsing
//                            return@label 0
//                        }
//                    } as Comparator<CashTransactionDto>)
//            }
//        }
//    }
//
//    private fun searchInCashs(
//        cashList: java.util.ArrayList<CashTransactionDto>?,
//        searchStr: String
//    ): java.util.ArrayList<CashTransactionDto>? {
//        val summaryModel = BookSummaryModel()
//        if (cashList == null || cashList.isEmpty() || Utility.isBlank(searchStr)) {
//            bookSummaryModel.value = BookSummaryModel(0.0, 0.0)
//            if (!Utility.isBlank(FeaturePrefManager.getInstance().transaksiSearchKeyword)) {
//                val prop = PropBuilder()
//                prop.put(AnalyticsConst.ENTRY_POINT2, AnalyticsConst.TRANSAKSI_HOME_PAGE)
//                prop.put(
//                    AnalyticsConst.SEARCH_KEYWORD,
//                    FeaturePrefManager.getInstance().transaksiSearchKeyword
//                )
//                AppAnalytics.trackEvent(AnalyticsConst.EVENT_SEARCH_PERFORMED, prop)
//                FeaturePrefManager.getInstance().transaksiSearchKeyword = ""
//            }
//            return cashList
//        }
//        FeaturePrefManager.getInstance().transaksiSearchKeyword = searchStr
//        val matchingCashs = java.util.ArrayList<CashTransactionDto>()
//        for (cashEntity in cashList) {
//            if (cashEntity.getCustomerName() != null && isMatchingSearchStr(
//                    cashEntity.getCustomerName(),
//                    searchStr
//                )
//            ) {
//                matchingCashs.add(cashEntity)
//            }
//            if (isMatchingSearchStr(cashEntity.categoryName, searchStr) ||
//                isMatchingSearchStr(cashEntity.transactionDescription, searchStr) ||
//                isMatchingSearchStr(cashEntity.transactionAmount.toString(), searchStr)
//            ) {
//                matchingCashs.add(cashEntity)
//            }
//            if (DateTimeUtils.isSameMonth(
//                    cashEntity.transactionDate,
//                    currentMonth, currentYear
//                )
//            ) {
//                if (cashEntity.transactionAmount > 0) {
//                    summaryModel.amountIn += cashEntity.transactionAmount
//                } else {
//                    summaryModel.amountOut += cashEntity.transactionAmount
//                }
//            }
//        }
//        bookSummaryModel.value = summaryModel
//        return matchingCashs
//    }
//
//    private fun isMatchingSearchStr(targetField: String, searchStr: String): Boolean {
//        return if (!Utility.isBlank(targetField)) {
//            targetField.lowercase(Locale.ROOT).contains(searchStr.lowercase(Locale.ROOT))
//        } else false
//    }
//
//    private fun getFilteredList(arrayList: ArrayList<CashTransactionDto>?): ArrayList<CashTransactionDto>? {
//        if (arrayList == null) {
//            return java.util.ArrayList()
//        }
//        val filteredResults = java.util.ArrayList<CashTransactionDto>()
//        if (transactionFilter == CashTabFilter.OUT) {
//            for (customerEntity in arrayList) {
//                if (customerEntity.transactionAmount.compareTo(0.toDouble()) < 0) {
//                    filteredResults.add(customerEntity)
//                }
//            }
//            return filteredResults
//        } else if (transactionFilter == CashTabFilter.IN) {
//            for (customerEntity in arrayList) {
//                if (customerEntity.transactionAmount.compareTo(0.toDouble()) > 0) {
//                    filteredResults.add(customerEntity)
//                }
//            }
//            return filteredResults
//        }
//        return arrayList
//    }
}