package com.bukuwarung.activities.print

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.widget.FrameLayout
import com.bukuwarung.R
import com.bukuwarung.activities.print.BluetoothPrinter.Companion.appendCenterWithNewLine
import com.bukuwarung.activities.print.BluetoothPrinter.Companion.appendCenterWithNewLineBold
import com.bukuwarung.activities.print.BluetoothPrinter.Companion.appendDashLine
import com.bukuwarung.activities.print.BluetoothPrinter.Companion.appendExtraLine
import com.bukuwarung.activities.print.BluetoothPrinter.Companion.appendLeftAndRight
import com.bukuwarung.activities.print.BluetoothPrinter.Companion.appendLeftAndRightBold
import com.bukuwarung.activities.print.BluetoothPrinter.Companion.appendLeftWithNewLine
import com.bukuwarung.activities.print.BluetoothPrinter.Companion.appendRightWithNewLine
import com.bukuwarung.bluetooth_printer.model.printTypes.BasePrintType
import com.bukuwarung.database.dto.CustomerTransactionSummaryDto
import com.bukuwarung.database.entity.BookEntity
import com.bukuwarung.database.entity.CustomerEntity
import com.bukuwarung.database.entity.TransactionEntity
import com.bukuwarung.databinding.LayoutBodyInvoiceBinding
import com.bukuwarung.databinding.LayoutFooterInvoiceBinding
import com.bukuwarung.databinding.LayoutHeaderInvoiceBinding
import com.bukuwarung.databinding.LayoutStoreDetailBinding
import com.bukuwarung.databinding.LayoutUtangInvoiceBinding
import com.bukuwarung.utils.*
import kotlin.properties.Delegates

class UtangReceipt @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : FrameLayout(context, attrs, defStyleAttr) {
    private var bookEntity: BookEntity? = null
    private var invoiceDataBlock: InvoiceDataBlock? = null
    private var customerEntity: CustomerEntity? = null
    private var transactionEntity: TransactionEntity? = null
    private var customerTransactionSummaryDto: CustomerTransactionSummaryDto? = null
    private var isExpanded: Boolean = false
    private var isGuestUser: Boolean = false

    private val binding: LayoutUtangInvoiceBinding
    
    init {
        binding = LayoutUtangInvoiceBinding.inflate(LayoutInflater.from(context), this, true)
    }

    fun setup(initializer: Initializer.() -> Unit) = Initializer(initializer)


    inner class Initializer private constructor() {
        constructor(init: Initializer.() -> Unit) : this() {
            init()
        }

        fun setInvoiceData(init: Initializer.() -> InvoiceDataBlock?) = apply {
            invoiceDataBlock = init()
            initialize()
        }

        fun setBook(init: Initializer.() -> BookEntity?) = apply {
            bookEntity = init()
            initialize()
        }

        fun setTransaction(init: Initializer.() -> TransactionEntity?) = apply {
            transactionEntity = init()
            initialize()
        }

        fun setCustomer(init: Initializer.() -> CustomerEntity?) = apply {
            customerEntity = init()
            initialize()
        }

        fun setSummaryDto(init: Initializer.() -> CustomerTransactionSummaryDto?) = apply {
            customerTransactionSummaryDto = init()
            initialize()
        }

        fun setExpanded(init: Initializer.() -> Boolean) = apply {
            isExpanded = init()
            initialize()
        }

        fun setGuestUser(init: Initializer.() -> Boolean) = apply {
            isGuestUser = init()
            initialize()
        }
    }

    private fun initialize() {
        initializeStoreDetails()
        initializeHeaderDetails()
        initializeBodyDetails()
        initializeFooterDetails()
        handleVisibility()
    }

    private fun initializeStoreDetails() {
        bookEntity?.let { book ->
            with(binding.layoutStoreDetail) {
                tvStoreName.text = book.businessName.takeIf { it != "Usaha Saya" } ?: "-"
                tvStorePhone.text = Utility.beautifyPhoneNumber(book.businessPhone)
                    .takeIf { it.isNotBlank() }
                    ?: Utility.beautifyPhoneNumber(book.ownerId).takeIf { it.isNotBlank() } ?: "-"
                tvStoreAddress.text = book.businessAddress
            }
        }
    }

    private fun initializeHeaderDetails() {
        with(binding.layoutHeaderInvoice) {
            tvDate.tvKey.text = context.getString(R.string.date)
            tvNoteCode.tvKey.text = context.getString(R.string.kode_nota)
            tvCustomerDetails.tvKey.text = context.getString(R.string.customers)
        }

        transactionEntity?.let { transaction ->
            binding.layoutHeaderInvoice.tvDate.tvValue.text = "%s %s".format(
                Utility.formatReceiptDate(transaction.date),
                Utility.getReadableTimeString(transaction.updatedAt)
            )
        }

        customerEntity?.let { customer ->
            binding.layoutHeaderInvoice.tvCustomerDetails.tvValue.text =
                customer.name.plus(System.lineSeparator()).plus(Utility.beautifyPhoneNumber(customer.phone))
        }
    }

    private fun initializeBodyDetails() {
        with(binding.layoutBodyInvoice) {
            tvInsufficientPayment.tvKey.text = context.getString(R.string.remain_debt)
            
            customerTransactionSummaryDto?.let { dto ->
                val remainingAmount = dto.total.toDouble() - dto.paid.toDouble()
                tvInsufficientPayment.tvValue.text = if (dto.total >= dto.paid) {
                    Utility.formatAmount(remainingAmount)
                } else {
                    "-".plus(Utility.formatAmount(remainingAmount))
                }
            }
        }

        transactionEntity?.let { transaction ->
            with(binding.layoutBodyInvoice) {
                tvAmountGiven.text = Utility.formatAmount(transaction.amount)
                tvTransactionTypeUtang.text = if (transaction.amount >= 0.0) {
                    context.getString(R.string.debt_paid)
                } else {
                    context.getString(R.string.debt_given)
                }
            }
        }
    }

    private fun initializeFooterDetails() {
        transactionEntity?.let { transaction ->
            binding.layoutFooterInvoice.tvNotesTxt.setTextOrDefault(transaction.description)
        }
    }

    private fun handleVisibility() {
        val invoiceData = invoiceDataBlock?.data?.getOrNull(0)
        
        handleStoreDetailVisibility(invoiceData?.storeDetailData?.getOrNull(0))
        handleHeaderDataVisibility(invoiceData?.headerData?.getOrNull(0))
        handleBodyDataVisibility(invoiceData?.bodyData?.getOrNull(0))
        handleFooterDataVisibility(invoiceData?.footerData?.getOrNull(0))
        
        // Handle expanded state
        binding.layoutBodyInvoice.tvInsufficientPayment.root.visibility = isExpanded.asVisibility()
    }

    private fun handleFooterDataVisibility(footerDataItem: FooterDataItem?) {
        with(binding.layoutFooterInvoice) {
            if (footerDataItem?.visibility.isFalseOrNull) {
                root.hideView()
            } else {
                root.showView()
            }
            
            val notesVisible = footerDataItem?.elements?.getOrNull(0)?.notes?.visibility != false 
                && !transactionEntity?.description.isNullOrBlank()
            
            if (notesVisible) {
                tvNotes.showView()
                tvNotesTxt.showView()
            } else {
                tvNotes.hideView()
                tvNotesTxt.hideView()
            }
            
            if (footerDataItem?.elements?.getOrNull(0)?.bukuAd?.visibility.isFalseOrNull) {
                bukuwarungAdLayout.hideView()
            } else {
                bukuwarungAdLayout.showView()
            }
        }
    }

    private fun handleBodyDataVisibility(bodyDataItem: BodyDataItem?) {
        with(binding.layoutBodyInvoice) {
            if (bodyDataItem?.visibility.isFalseOrNull) {
                root.hideView()
            } else {
                root.showView()
            }
            if (bodyDataItem?.elements?.getOrNull(0)?.insufficientPayment?.visibility.isFalseOrNull) {
                tvInsufficientPayment.root.hideView()
            } else {
                tvInsufficientPayment.root.showView()
            }
        }
    }

    private fun handleHeaderDataVisibility(headerDataItem: HeaderDataItem?) {
        with(binding.layoutHeaderInvoice) {
            if (headerDataItem?.visibility.isFalseOrNull) {
                root.hideView()
            } else {
                root.showView()
            }
            if (headerDataItem?.elements?.getOrNull(0)?.customer?.visibility.isFalseOrNull) {
                tvCustomerDetails.root.hideView()
            } else {
                tvCustomerDetails.root.showView()
            }
            if (headerDataItem?.elements?.getOrNull(0)?.noteCode?.visibility.isFalseOrNull) {
                tvNoteCode.root.hideView()
            } else {
                tvNoteCode.root.showView()
            }
        }
    }

    private fun handleStoreDetailVisibility(storeDetailDataItem: StoreDetailDataItem?) {
        with(binding.layoutStoreDetail) {
            if (storeDetailDataItem?.visibility.isFalseOrNull) {
                root.hideView()
            } else {
                root.showView()
            }
            if (storeDetailDataItem?.elements?.getOrNull(0)?.icon?.visibility.isFalseOrNull) {
                ivLogo.hideView()
            } else {
                ivLogo.showView()
            }
            if (storeDetailDataItem?.elements?.getOrNull(0)?.address?.visibility.isFalseOrNull 
                || bookEntity?.businessAddress.isNullOrEmpty()) {
                tvStoreAddress.hideView()
            } else {
                tvStoreAddress.showView()
            }
            if (storeDetailDataItem?.elements?.getOrNull(0)?.phone?.visibility.isFalseOrNull) {
                tvStorePhone.hideView()
            } else {
                tvStorePhone.showView()
            }
        }
    }

    fun getFormattedText(): ArrayList<BasePrintType> {
        val printableList = ArrayList<BasePrintType>()

        val storeDetailDataItem = invoiceDataBlock?.data?.getOrNull(0)?.storeDetailData?.getOrNull(0)
        if (storeDetailDataItem?.visibility == true) {
            with(binding.layoutStoreDetail) {
                printableList.add(appendCenterWithNewLineBold(tvStoreName.text))
                if (storeDetailDataItem.elements?.getOrNull(0)?.address?.visibility == true 
                    && bookEntity?.businessAddress.isNotNullOrEmpty()) {
                    printableList.add(appendCenterWithNewLine(tvStoreAddress.text))
                }
                if (storeDetailDataItem.elements?.getOrNull(0)?.phone?.visibility == true) {
                    printableList.add(appendCenterWithNewLine(tvStorePhone.text))
                }
            }
            printableList.add(appendExtraLine())
        }

        val headerDataItem = invoiceDataBlock?.data?.getOrNull(0)?.headerData?.getOrNull(0)
        if (headerDataItem?.visibility == true) {
            with(binding.layoutHeaderInvoice) {
                printableList.add(appendLeftAndRight(tvDate.tvKey.text, tvDate.tvValue.text))
                if (headerDataItem.elements?.getOrNull(0)?.noteCode?.visibility == true) {
                    printableList.add(appendLeftAndRight(tvNoteCode.tvKey.text, tvNoteCode.tvValue.text))
                }
                if (headerDataItem.elements?.getOrNull(0)?.customer?.visibility == true) {
                    printableList.add(appendLeftAndRight(tvCustomerDetails.tvKey.text, customerEntity?.name))
                    printableList.add(appendRightWithNewLine(Utility.beautifyPhoneNumber(customerEntity?.phone)))
                }
            }
        }

        with(binding.layoutBodyInvoice) {
            printableList.add(appendDashLine())
            printableList.add(appendLeftAndRightBold(tvTransactionTypeUtang.text, tvAmountGiven.text))
            printableList.add(appendDashLine())
        }

        val bodyDataItem = invoiceDataBlock?.data?.getOrNull(0)?.bodyData?.getOrNull(0)
        if (bodyDataItem?.visibility == true && isExpanded) {
            if (bodyDataItem.elements?.getOrNull(0)?.insufficientPayment?.visibility == true) {
                with(binding.layoutBodyInvoice.tvInsufficientPayment) {
                    printableList.add(appendLeftAndRight(tvKey.text, tvValue.text))
                }
            }
            printableList.add(appendExtraLine())
        }

        val footerDataItem = invoiceDataBlock?.data?.getOrNull(0)?.footerData?.getOrNull(0)
        if (footerDataItem?.visibility == true) {
            with(binding.layoutFooterInvoice) {
                if (footerDataItem.elements?.getOrNull(0)?.notes?.visibility == true 
                    && !tvNotesTxt.text.isNullOrBlank()) {
                    printableList.add(appendLeftWithNewLine(tvNotes.text))
                    printableList.add(appendLeftWithNewLine(tvNotesTxt.text))
                    printableList.add(appendExtraLine())
                }

                if (footerDataItem.elements?.getOrNull(0)?.bukuAd?.visibility == true) {
                    printableList.add(appendCenterWithNewLine(tvFooterOne.text))
                    printableList.add(appendCenterWithNewLine(tvBukuwarungUrl.text))
                    printableList.add(appendExtraLine())
                }
            }
        }

        printableList.add(appendExtraLine())
        printableList.add(appendExtraLine())
        return printableList
    }
}