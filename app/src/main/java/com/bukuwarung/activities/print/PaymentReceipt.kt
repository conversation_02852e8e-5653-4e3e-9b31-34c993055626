package com.bukuwarung.activities.print

import android.content.Context
import android.text.Html
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.widget.FrameLayout
import androidx.core.content.ContextCompat
import com.bukuwarung.R
import com.bukuwarung.activities.print.BluetoothPrinter.Companion.appendCenterWithNewLine
import com.bukuwarung.activities.print.BluetoothPrinter.Companion.appendCenterWithNewLineBoldAndLarge
import com.bukuwarung.activities.print.BluetoothPrinter.Companion.appendDashLine
import com.bukuwarung.activities.print.BluetoothPrinter.Companion.appendExtraLine
import com.bukuwarung.activities.print.BluetoothPrinter.Companion.appendLeftAndRight
import com.bukuwarung.activities.print.BluetoothPrinter.Companion.appendLeftWithNewLine
import com.bukuwarung.activities.print.BluetoothPrinter.Companion.appendLeftWithNewLineBold
import com.bukuwarung.activities.print.BluetoothPrinter.Companion.appendLeftWithNewLineBoldAndLarge
import com.bukuwarung.bluetooth_printer.model.printTypes.BasePrintType
import com.bukuwarung.constants.AppConst.EMPTY_STRING
import com.bukuwarung.database.entity.BookEntity
import com.bukuwarung.database.entity.CustomerEntity
import com.bukuwarung.database.entity.UserProfileEntity
import com.bukuwarung.databinding.UtangPaymentReceiptLayoutBinding
import com.bukuwarung.payments.constants.PpobConst
import com.bukuwarung.payments.data.model.FinproOrderResponse
import com.bukuwarung.payments.data.model.PaymentHistory
import com.bukuwarung.payments.data.model.PaymentReceiptDto
import com.bukuwarung.payments.data.model.ReceiverBank
import com.bukuwarung.utils.*

class PaymentReceipt @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : FrameLayout(context, attrs, defStyleAttr) {
    private lateinit var binding: UtangPaymentReceiptLayoutBinding

    init {
        binding = UtangPaymentReceiptLayoutBinding.inflate(LayoutInflater.from(context), this, true)
    }

    private var bookEntity: BookEntity? = null
    private var customerEntity: CustomerEntity? = null
    private var receiverBank: ReceiverBank? = null
    private var paymentReceiptDto: PaymentReceiptDto? = null
    private var finproOrderResponse: FinproOrderResponse? = null
    private var userProfileEntity: UserProfileEntity? = null

    fun setup(initializer: Initializer.() -> Unit) = Initializer(initializer)

    inner class Initializer private constructor() {
        constructor(init: PaymentReceipt.Initializer.() -> Unit) : this() {
            init()
        }

        fun setBook(init: Initializer.() -> BookEntity?) = apply {
            bookEntity = init()
            initialize()
        }

        fun setCustomer(init: Initializer.() -> CustomerEntity?) = apply {
            customerEntity = init()
            initialize()
        }

        fun setReceiverBank(init: Initializer.() -> ReceiverBank?) = apply {
            receiverBank = init()
            initialize()
        }

        fun setDto(init: Initializer.() -> PaymentReceiptDto?, userProfile: UserProfileEntity?) = apply {
            paymentReceiptDto = init()
            userProfileEntity = userProfile
            initialize()
        }

        fun setFinproOrder(init: Initializer.() -> FinproOrderResponse?) = apply {
            finproOrderResponse = init()
            initialize()
        }
    }

    private fun initialize() {
        bookEntity?.let { book ->
            binding.tvWarungName.text = book.businessName.takeIf { it != "Usaha Saya" } ?: "-"
            binding.tvWarungPhone.text = Utility.beautifyPhoneNumber(book.businessPhone)

        }

        customerEntity?.let { customer ->
            binding.tvCstName.setTextOrDefault(customer.name)
            binding.tvCstPhone.setTextOrDefault(Utility.beautifyPhoneNumber(customer.phone))
        }

        paymentReceiptDto?.let { dto ->
            binding.tvTransactionDate.text =
                Utility.formatReceiptDateFromHistory(dto.completedStatusDate, true)
            binding.tvInvoiceNumber.text = dto.transactionId
            binding.tvTransactionNominal.text = Utility.formatAmount(dto.amount)
            binding.tvFreeCharge.visibility = dto.paymentFreeChargeStatus.asVisibility()
            if (dto.ppobCategory == PpobConst.CATEGORY_LISTRIK && dto.categoryCode != PpobConst.CATEGORY_PLN_POSTPAID) {
                binding.noteHint.visibility = View.VISIBLE
                binding.layoutPaymentCatatan.visibility = View.VISIBLE
                binding.tvNumber.text = dto.ppobItem?.details?.token
                binding.tvPdtName.text = context.getString(R.string.token_code)
            } else if (dto.ppobCategory == PpobConst.CATEGORY_VOUCHER_GAME && dto.ppobItem?.details?.voucherCode.isNotNullOrBlank()) {
                binding.layoutPaymentCatatan.visibility = View.VISIBLE
                binding.layoutPaymentCatatan.setBackgroundColor(
                    ContextCompat.getColor(
                        context,
                        R.color.alice_blue
                    )
                )
                binding.tvNumber.text = dto.ppobItem?.details?.voucherCode
                binding.tvPdtName.text = context.getString(R.string.code_voucher)
                binding.noteHint.showView()
                binding.noteHint.text = context.getString(R.string.voucher_code_info)
            } else {
                binding.layoutPaymentCatatan.visibility = View.GONE
                binding.noteHint.visibility = View.GONE
            }

            receiverBank?.let { bank ->
                // In case of ayment In, show info in following format
                // [Bank code]-[Business Name]-[merchant bank Name]
                if (dto.isPaymentIn && bookEntity?.businessName.isNotNullOrBlank()) {
                    binding.tvRecipientName.text = context.getString(
                        R.string.three_dashed_strings,
                        bank.bankCode,
                        bookEntity?.businessName,
                        bank.accountHolderName
                    )
                } else {
                    binding.tvRecipientName.text = context.getString(
                        R.string.two_dashed_strings,
                        bank.bankCode, bank.accountHolderName
                    )
                }
                binding.tvRecipientAaccountNumber.text = bank.accountNumber
            }

            binding.tvSenderName.text = if (dto.isPaymentIn) {
                context.getString(
                    R.string.two_dashed_strings,
                    dto.paymentChannel, customerEntity?.name
                )
            } else {
                // In case of Payment Out, show info in the following format
                // [Bank code]-[Business Name]-[Business Owner Name]
                val businessOwnerName = when {
                    userProfileEntity?.userName.isNotNullOrBlank() -> {
                        userProfileEntity?.userName
                    }
                    bookEntity?.businessOwnerName.isNotNullOrBlank() -> {
                        bookEntity?.let {
                            if (it.hasCompletedProfileWithOwnerName()) it.businessOwnerName
                            else context.getString(R.string.defaulBusinessName)
                        } ?: run { context.getString(R.string.defaulBusinessName) }
                    }
                    else -> context.getString(R.string.defaulBusinessName)
                }
                context.getString(
                    R.string.three_dashed_strings,
                    dto.paymentChannel,
                    bookEntity?.businessName,
                    businessOwnerName
                )
            }

            binding.tvTransactionNote.textHTML(
                dto.note.let {
                    if (it.isBlank() || it == "-") {
                        context.getString(R.string.label_payment_in_note_plain)
                    } else {
                        "${context.getString(R.string.label_payment_in_note_plain)} - $it"
                    }
                }
            )
            if (dto.isQrisPaymentIn) {
                binding.tvTransactionNote.textHTML(dto.note)
                binding.gpCustomerDetails.hideView()
            }
            binding.senderTxt.visibility = (!dto.isPPOB && !dto.isQrisPaymentIn).asVisibility()
            binding.tvSenderName.visibility = (!dto.isPPOB && !dto.isQrisPaymentIn).asVisibility()
            binding.recipientTxt.visibility = (!dto.isPPOB && !dto.isQrisPaymentIn).asVisibility()
            binding.tvRecipientName.visibility =
                (!dto.isPPOB && !dto.isQrisPaymentIn).asVisibility()
            binding.tvRecipientAaccountNumber.visibility =
                (!dto.isPPOB && !dto.isQrisPaymentIn).asVisibility()
            binding.recipientDivider.visibility =
                (!dto.isPPOB && !dto.isQrisPaymentIn).asVisibility()

        }

        finproOrderResponse?.let { order ->
            val item = order.items?.firstOrNull()
            binding.tvCstName.text = "-"
            if (item?.beneficiary?.category == PpobConst.CATEGORY_LISTRIK) {
                binding.tvCstPhone.text = item.beneficiary.phoneNumber
            } else {
                val mobileNumber = item?.beneficiary?.number ?: ""
                val isMobileNumer = Regex("^(^\\+62|62|062|08|8)(\\d{8,12})\$").matches(mobileNumber)
                binding.tvCstPhone.text = if (isMobileNumer) mobileNumber else "-"
            }
            if (item?.beneficiary?.category == PpobConst.CATEGORY_BPJS || item?.beneficiary?.code == PpobConst.CATEGORY_PLN_POSTPAID) {
                binding.tvCstPhone.text = "-"
            }
            if (item?.beneficiary?.category == PpobConst.CATEGORY_TRAIN_TICKET) {
                binding.tvCstName.text = context.getString(R.string.code_booking_info)
                binding.tvCstPhone.hideView()
                binding.tvCstNameLabel.hideView()
                binding.tvCstPhoneLabel.hideView()
                binding.tvTransactionNoteMessage.text =
                    context.getString(R.string.label_payment_detail)
                binding.layoutPaymentKode.showView()
                binding.tvToken.text = item.details.token
            }
            binding.tvTransactionNote.text = order.description?.trim() ?: EMPTY_STRING
            binding.tvTransactionDate.text =
                order.progress?.firstOrNull { it.state == PaymentHistory.STATUS_COMPLETED }
                    ?.getFormattedTimestamp()
                ?: "-"
            val serialNumber = item?.details?.serialNumber
            binding.serialNumberTxt.visibility = View.VISIBLE
            binding.serialNumberDivider.visibility = View.VISIBLE
            binding.serialNumberTxt.text =
                context.getString(R.string.serial_number_formatted, (serialNumber ?: "-"))
        }

        binding.layoutTransactionDetail.visibility = View.GONE
        binding.tvFooter.textHTML(RemoteConfigUtils.getAppText().poweredByFooter)
    }

    fun getFormattedText(): ArrayList<BasePrintType> {
        val printableList = ArrayList<BasePrintType>()

        printableList.addAll(
            listOf(
                appendLeftAndRight(
                    Utility.formatReceiptDateFromHistory(
                        paymentReceiptDto?.completedStatusDate,
                        false
                    ), binding.tvInvoiceNumber.text.toString()
                ),
                appendLeftWithNewLine(Utility.formatReceiptTimeromHistory(paymentReceiptDto?.completedStatusDate)),
                appendDashLine(),
                appendLeftWithNewLineBold(binding.tvWarungName.text.toString()),
                appendLeftWithNewLine(binding.tvWarungPhone.text.toString()),
                appendDashLine()
            )
        )

        if (paymentReceiptDto?.ppobCategory == PpobConst.CATEGORY_TRAIN_TICKET) {
            printableList.addAll(
                listOf(
                    appendLeftWithNewLineBold(context.getString(R.string.code_booking_info)),
                    appendLeftWithNewLine(context.getString(R.string.code_booking)),
                    appendLeftWithNewLineBold(paymentReceiptDto?.ppobItem?.details?.token.orEmpty()),
                    appendDashLine(),
                    appendLeftWithNewLine(context.getString(R.string.electricity_token_hint))
                )
            )
        } else {
            printableList.addAll(
                listOf(
                    appendLeftAndRight(context.getString(R.string.name_label), context.getString(R.string.mobile_phone_label)),
                    appendLeftAndRight(
                        binding.tvCstName.text.toString(),
                        binding.tvCstPhone.text.toString()
                    ),
                    appendExtraLine()
                )
            )
        }

        printableList.addAll(
            listOf(
                appendLeftWithNewLine(context.getString(R.string.payment_successful)),
                appendLeftWithNewLineBold(binding.tvTransactionNominal.text.toString()),
            )
        )

        // shouldShowAdminFeeDetails
        with(paymentReceiptDto?.agentFeeInfo?.amount ?: 0.0) {
            if (this != 0.0 && paymentReceiptDto?.isPPOB.isFalse) {
                printableList.addAll(
                    listOf(
                        appendLeftAndRight(if (paymentReceiptDto?.isPaymentIn.isTrue) context.getString(R.string.nominal_transaksi) else context.getString(R.string.label_total_payment), context.getString(R.string.service_fee)),
                        appendLeftAndRight(
                            binding.tvNominalAmount.text.toString(),
                            binding.tvServiceFee.text.toString()
                        ),
                        appendDashLine(),
                    )
                )
            }
        }

        // shouldShowPaymentFreeCharge
        with(paymentReceiptDto?.paymentFreeChargeStatus.isTrue) {
            if (this) {
                appendLeftWithNewLine(context.getString(R.string.free_charge_label))
            }
        }

        // showRegularPaymentInfo
        // We do not show recipient and sender for QRIS and PPOB transactions

        with(paymentReceiptDto?.isPPOB.isFalse && paymentReceiptDto?.isQrisPaymentIn.isFalse) {
            if (this) {
                printableList.addAll(
                    listOf(
                        appendLeftWithNewLine(context.getString(R.string.sender)),
                        appendLeftWithNewLine(binding.tvSenderName.text.toString()),
                        appendExtraLine(),
                        appendLeftWithNewLine(context.getString(R.string.penerima)),
                        appendLeftWithNewLine(binding.tvRecipientName.text.toString()),
                        appendLeftWithNewLine(binding.tvRecipientAaccountNumber.text.toString()),
                        appendDashLine()
                    )
                )
            } else {
                printableList.add(appendExtraLine())
            }
        }

        printableList.addAll(
            listOf(
                appendLeftWithNewLine(binding.tvTransactionNoteMessage.text.toString()),
                appendLeftWithNewLine(binding.tvTransactionNote.text.toString()),
            )
        )

        // showListrikInfo
        with(paymentReceiptDto?.ppobItem?.details?.token ?: "") {
            if (this.isNotBlank() && paymentReceiptDto?.isPPOB.isTrue && paymentReceiptDto?.ppobCategory == PpobConst.CATEGORY_LISTRIK) {
                printableList.addAll(
                    listOf(
                        appendDashLine(),
                        appendCenterWithNewLine(context.getString(R.string.token_code)),
                        appendCenterWithNewLineBoldAndLarge(this),
                        appendDashLine(),
                        appendLeftWithNewLine(context.getString(R.string.electricity_token_hint))
                    )
                )
            }
        }

        with(paymentReceiptDto?.ppobItem?.details?.voucherCode ?: "") {
            if (paymentReceiptDto?.ppobCategory == PpobConst.CATEGORY_VOUCHER_GAME && this.isNotNullOrBlank()) {
                printableList.addAll(
                    listOf(
                        appendDashLine(),
                        appendLeftWithNewLine(context.getString(R.string.code_voucher)),
                        appendLeftWithNewLineBold(this),
                        appendLeftWithNewLine(context.getString(R.string.voucher_code_info)),
                        appendDashLine()
                    )
                )
            }
        }

//      showSerialNumberPpob
        with(paymentReceiptDto?.isPPOB.isTrue) {
            if (this) {
                printableList.addAll(
                    listOf(
                        appendLeftWithNewLineBold(binding.serialNumberTxt.text.toString()),
                        appendDashLine()
                    )
                )
            }
        }

        printableList.addAll(
            listOf(
                appendCenterWithNewLine(RemoteConfigUtils.getAppText().poweredByFooterPlain),
                appendExtraLine(),
                appendExtraLine()
            )
        )

        return printableList
    }

    fun setNotePreview(text: String) {
        binding.tvTransactionNote.text = text
    }

    fun setServiceFee(amount: Double) {
        binding.tvServiceFee.text = Utility.formatAmount(amount)
        binding.layoutServiceFee.visibility = if (amount != 0.0) View.VISIBLE else View.GONE
    }

    fun setNominalAmount(text: String) {
        binding.tvNominalAmount.text = text
    }
}