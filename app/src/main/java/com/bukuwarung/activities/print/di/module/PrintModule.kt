package com.bukuwarung.activities.print.di.module

import com.bukuwarung.activities.print.deeplink.handler.NotesMissionSignalHandler
import com.bukuwarung.neuro.api.SignalHandler
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import dagger.multibindings.IntoSet

@Module
@InstallIn(SingletonComponent::class)
interface PrintModule {
    @Binds
    @IntoSet
    fun bindNotesMissionSignalHandler(handler: NotesMissionSignalHandler): SignalHandler
}