package com.bukuwarung.activities.print

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.widget.FrameLayout
import com.bukuwarung.R
import com.bukuwarung.activities.print.BluetoothPrinter.Companion.appendDashLine
import com.bukuwarung.activities.print.BluetoothPrinter.Companion.appendExtraLine
import com.bukuwarung.activities.print.BluetoothPrinter.Companion.appendLeftAndRight
import com.bukuwarung.activities.print.BluetoothPrinter.Companion.appendLeftWithNewLine
import com.bukuwarung.activities.print.BluetoothPrinter.Companion.appendLeftWithNewLineBoldAndLarge
import com.bukuwarung.bluetooth_printer.model.printTypes.BasePrintType
import com.bukuwarung.database.dto.CustomerTransactionSummaryDto
import com.bukuwarung.database.entity.BookEntity
import com.bukuwarung.database.entity.CustomerEntity
import com.bukuwarung.database.entity.TransactionEntity
import com.bukuwarung.databinding.UtangReceiptLayoutBinding
import com.bukuwarung.utils.Utility
import com.bukuwarung.utils.asVisibility
import com.bukuwarung.utils.setTextOrDefault

class UtangOldReceipt @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : FrameLayout(context, attrs, defStyleAttr) {
    private lateinit var binding: UtangReceiptLayoutBinding
    private var bookEntity: BookEntity? = null
    private var customerEntity: CustomerEntity? = null
    private var transactionEntity: TransactionEntity? = null
    private var customerTransactionSummaryDto: CustomerTransactionSummaryDto? = null
    private var isExpanded: Boolean = false
    private var isGuestUser: Boolean = false

    init {
        binding = UtangReceiptLayoutBinding.inflate(LayoutInflater.from(context), this, true)
    }

    fun setup(initializer: Initializer.() -> Unit) = Initializer(initializer)

    inner class Initializer private constructor() {
        constructor(init: Initializer.() -> Unit) : this() {
            init()
        }

        fun setBook(init: Initializer.() -> BookEntity?) = apply {
            bookEntity = init()
            initialize()
        }

        fun setTransaction(init: Initializer.() -> TransactionEntity?) = apply {
            transactionEntity = init()
            initialize()
        }

        fun setCustomer(init: Initializer.() -> CustomerEntity?) = apply {
            customerEntity = init()
            initialize()
        }

        fun setSummaryDto(init: Initializer.() -> CustomerTransactionSummaryDto?) = apply {
            customerTransactionSummaryDto = init()
            initialize()
        }

        fun setExpanded(init: Initializer.() -> Boolean) = apply {
            isExpanded = init()
            initialize()
        }

        fun setGuestUser(init: Initializer.() -> Boolean) = apply {
            isGuestUser = init()
            initialize()
        }
    }

    private fun initialize() {
        bookEntity?.let { book ->
            binding.tvWarungName.text = book.businessName.takeIf { it != "Usaha Saya" } ?: "-"
            binding.tvWarungPhone.text =
                Utility.beautifyPhoneNumber(book.businessPhone).takeIf { it.isNotBlank() }
                ?: Utility.beautifyPhoneNumber(book.ownerId).takeIf { it.isNotBlank() } ?: "-"
        }

        transactionEntity?.let { transaction ->
            binding.tvTransactionDate.text = "%s %s".format(
                Utility.formatReceiptDate(transaction.date),
                Utility.getReadableTimeString(transaction.updatedAt)
            )
            binding.tvTransactionType.text =
                context.getString(if (transaction.amount >= 0) R.string.receiving_label else R.string.giving_label)
            binding.tvTransactionNote.setTextOrDefault(transaction.description)
            binding.tvTransactionNominal.text = Utility.formatAmount(transaction.amount)

        }

        customerEntity?.let { customer ->
            binding.tvCstName.text = customer.name
            binding.tvCstPhone.text = Utility.beautifyPhoneNumber(customer.phone)
        }

        customerTransactionSummaryDto?.let { dto ->
            binding.tvTransactionTotal.text = Utility.formatAmount(dto.total.toDouble())
            binding.tvPaidTotal.text = Utility.formatAmount(dto.paid.toDouble())
            binding.tvRemainingTotal.text = Utility.formatAmount(dto.remaining.toDouble())
        }

        binding.transactionDetailLayout.visibility = isExpanded.asVisibility()
        binding.tvWarungName.visibility = (!isGuestUser).asVisibility()
        binding.tvWarungPhone.visibility = (!isGuestUser).asVisibility()
    }

    fun getFormattedText(): ArrayList<BasePrintType> {
        val printableList = ArrayList<BasePrintType>()

        printableList.add(appendLeftWithNewLine(binding.tvTransactionDate.text))
        printableList.add(appendDashLine())

        if (!isGuestUser) {
            printableList.addAll(
                listOf(
                    appendLeftWithNewLine(binding.tvWarungName.text),
                    appendLeftWithNewLine(binding.tvWarungPhone.text),
                    appendDashLine()
                )
            )
        } else {
            printableList.add(appendExtraLine())
        }

        printableList.addAll(
            listOf(
                appendLeftAndRight(context.getString(R.string.name_label), context.getString(R.string.mobile_phone_label)),
                appendLeftAndRight(binding.tvCstName.text, binding.tvCstPhone.text),
                appendExtraLine(),
                appendLeftWithNewLine(binding.tvTransactionType.text),
                appendLeftWithNewLineBoldAndLarge(binding.tvTransactionNominal.text),
                appendDashLine(),
                appendLeftWithNewLine(context.getString(R.string.label_note)),
                appendLeftWithNewLine(binding.tvTransactionNote.text),
                appendDashLine()
            )
        )

        if (isExpanded) {
            printableList.addAll(
                listOf(
                    appendLeftAndRight(
                        context.getString(R.string.total_utang),
                        binding.tvTransactionTotal.text
                    ),
                    appendLeftAndRight(
                        context.getString(R.string.total_sudah_dibayar),
                        binding.tvPaidTotal.text
                    ),
                    appendLeftAndRight(
                        context.getString(R.string.kurang_bayar),
                        binding.tvRemainingTotal.text
                    ),
                    appendExtraLine(),
                    appendDashLine()
                )
            )
        } else {
            printableList.add(appendExtraLine())
        }

        printableList.addAll(
            listOf(
                appendExtraLine(),
                appendExtraLine()
            )
        )
        return printableList
    }
}