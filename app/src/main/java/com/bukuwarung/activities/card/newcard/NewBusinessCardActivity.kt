package com.bukuwarung.activities.card.newcard

import android.app.Activity
import android.content.Intent
import android.content.pm.PackageManager
import android.text.Editable
import android.text.TextWatcher
import androidx.activity.viewModels
import androidx.core.widget.addTextChangedListener
import androidx.core.widget.doAfterTextChanged
import androidx.lifecycle.Observer
import androidx.viewpager.widget.ViewPager
import androidx.work.*
import com.bukuwarung.Application
import com.bukuwarung.activities.card.BusinessCardShareActivity
import com.bukuwarung.activities.geolocation.data.model.Address
import com.bukuwarung.activities.geolocation.view.BusinessAddressActivity
import com.bukuwarung.activities.profile.ProfileTabViewModel
import com.bukuwarung.activities.superclasses.BaseActivity
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.analytics.AppAnalytics.PropBuilder
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.constants.PermissionConst
import com.bukuwarung.database.entity.BookEntity
import com.bukuwarung.databinding.ActivityNewBusinessCardBinding
import com.bukuwarung.location.LocationUtil
import com.bukuwarung.preference.FeaturePrefManager
import com.bukuwarung.session.User
import com.bukuwarung.utils.*
import com.google.firebase.crashlytics.FirebaseCrashlytics
import dagger.hilt.android.AndroidEntryPoint
import java.util.*
import javax.inject.Inject
import kotlin.collections.HashMap

@AndroidEntryPoint
class NewBusinessCardActivity : BaseActivity() {
    private lateinit var binding: ActivityNewBusinessCardBinding
    private val pagerAdapter: BusinessCardAdapter by lazy { BusinessCardAdapter(this) }
    private var bookEntity: BookEntity? = null
        set(value) {
            field = value
            pagerAdapter.updateBusinessData(field)
        }

    private val viewModel: BusinessCardViewModel by viewModels()
    private val profileTabViewModel: ProfileTabViewModel by viewModels()
    private var cardDesigns: List<BusinessCardDesign> = emptyList()
    private var selectedDesignIndex: Int = 0
    private var isViewInitialized: Boolean = false
    private var address: Address? = null

    private var from:String? = null

    override fun setViewBinding() {
        binding = ActivityNewBusinessCardBinding.inflate(layoutInflater)
    }

    override fun setupView() {
        setContentView(binding.root)
        setUpToolbarWithHomeUp(binding.tb)

        binding.apply {
            binding.btnSave.setOnClickListener {

                var propBuilder = PropBuilder()
                propBuilder.put(AnalyticsConst.ENTRY_POINT2, AnalyticsConst.SAVE_BUSINESS_CARD)
                AppAnalytics.trackEvent(AnalyticsConst.EVENT_PREVIEW_BUSINESS_CARD_OPEN, propBuilder)

                FeaturePrefManager.getInstance().isBusinessCardSavedOnce = true
                InputUtils.hideKeyBoardWithCheck(this@NewBusinessCardActivity)

                propBuilder = PropBuilder()

                if (from.isNotNullOrEmpty()) {
                    propBuilder.put(AnalyticsConst.ENTRY_POINT, AnalyticsConst.HomePage.HOMEPAGE)
                }

                AppAnalytics.trackEvent(AnalyticsConst.EVENT_SAVE_BUSINESS_CARD, propBuilder)
                if (bookEntity != null && !Utility.isBlank(bookEntity!!.businessEmail)) {
//                    MoEHelper.getInstance(this@NewBusinessCardActivity).setEmail(bookEntity!!.businessEmail)
                    val prop = HashMap<String, Any>()
                    prop["email"] = bookEntity!!.businessEmail
                    AppAnalytics.setUserProperty("email", bookEntity!!.businessEmail)
                }

                viewModel.saveBusinessCard(address)
            }

            cardPager.apply {
                val windowWidthPixels = getScreenWidth(this@NewBusinessCardActivity)
                val margin = (windowWidthPixels * 0.05).toInt()
                val padding = (windowWidthPixels * 0.09).toInt()

                adapter = pagerAdapter
                clipToPadding = false
                setPadding(padding, 0, padding, 0)
                pageMargin = margin

                layoutParams.width = ViewPager.LayoutParams.MATCH_PARENT
                layoutParams.height = (windowWidthPixels * 5.5 / 9).toInt()
                requestLayout()
                dotsIndicator.setViewPager(this)

                addOnPageChangeListener(object : ViewPager.OnPageChangeListener {
                    override fun onPageScrollStateChanged(state: Int) {
                    }

                    override fun onPageScrolled(position: Int, positionOffset: Float, positionOffsetPixels: Int) {
                    }

                    override fun onPageSelected(position: Int) {
                        selectedDesignIndex = position
                    }

                })
            }

            etBusinessName.setOnFocusChangeListener { view, b ->
                viewModel.updateBusinessData(bookEntity?.apply { businessName = etBusinessName.text.toString() })
            }

            etOwnerName.setOnFocusChangeListener { view, b ->
                viewModel.updateBusinessData(bookEntity?.apply { businessOwnerName = etOwnerName.text.toString() })
            }

            etPhone.setOnFocusChangeListener { view, b ->
                viewModel.updateBusinessData(bookEntity?.apply { businessPhone = etPhone.text.toString() })
            }

            etSlogan.setOnFocusChangeListener { view, b ->
                viewModel.updateBusinessData(bookEntity?.apply { businessTagLine = etSlogan.text.toString() })
            }

            etAddress.setOnFocusChangeListener { view, b ->
                viewModel.updateBusinessData(bookEntity?.apply { businessAddress = etAddress.text.toString() })
            }

            bookEntity = viewModel.bookEntity.value
            etBusinessName.setText(bookEntity?.businessName)
            if(FeaturePrefManager.getInstance().isBusinessCardSavedOnce){
                etOwnerName.setText(bookEntity?.businessOwnerName)
            }
            etPhone.setText(bookEntity?.businessPhone)
            etSlogan.setText(bookEntity?.businessTagLine)
            etAddress.setText(Utility.getCompleteAddress(bookEntity))

            etAddress.setOnClickListener {
                AppAnalytics.PropBuilder().apply {
                    put(AnalyticsConst.ENTRY_POINT, AnalyticsConst.BUSINESS_CARD)
                    put(AnalyticsConst.ACTION, if (bookEntity?.isEditingAddressForFirstTime().isTrue) AnalyticsConst.NEW else AnalyticsConst.EDIT)
                    AppAnalytics.trackEvent(AnalyticsConst.EDIT_BUSINESS_ADDRESS, this)
                }

                val intent = BusinessAddressActivity.createIntent(this@NewBusinessCardActivity, entryPoint = AnalyticsConst.BUSINESS_CARD)
                startActivityForResult(intent, ADDRESS_FLOW_REQUEST_CODE)
            }
        }
    }

    override fun onResume() {
        super.onResume()
        if (intent.hasExtra("from")) {
            from = intent.getStringExtra("from")
        }
    }

    override fun subscribeState() {
        viewModel.bookEntity.observe(this, Observer { bookEntity = it })
        viewModel.businessCardDesignLiveData.observe(this, Observer {
            cardDesigns = it
            pagerAdapter.updateCardDesign(it)
            if (!isViewInitialized) {
                isViewInitialized = true
                val savedBizzCardUid = FeaturePrefManager.getInstance().newBizzCardDesign
                val savedBizzCardDesign = cardDesigns.find { design -> design.cardUID == savedBizzCardUid }
                        ?: return@Observer
                binding.cardPager.currentItem = cardDesigns.indexOf(savedBizzCardDesign)
            }
        })

        subscribeSingleLiveEvent(viewModel.state) { state ->
            when (state) {
                BusinessCardViewModel.State.OnSaved -> {
                    FeaturePrefManager.getInstance().newBizzCardDesign = cardDesigns[selectedDesignIndex].cardUID

                    val intent = Intent(this, BusinessCardShareActivity::class.java).apply {
                        putExtra(BusinessCardShareActivity.NEW_CARD_DESIGN, cardDesigns[selectedDesignIndex])
                        putExtra(BusinessCardShareActivity.FROM, from)
                    }

                    startActivity(intent)
                    finish()
                }
            }
        }


        if(!FeaturePrefManager.getInstance().isBusinessCardSavedOnce){
            profileTabViewModel.getUserProfile(User.getUserId()).observe(this, Observer {
                if (it != null) {
                    if(it.userName.isNotNullOrEmpty()){
                        binding.etOwnerName.setText(it.userName)
                    }
                } else {
                binding.etOwnerName.setText(bookEntity?.businessOwnerName)
                }
            })
        }
    }



    private fun storeLocation() {
        try {
            LocationUtil.getLocation(this, "business card")?.observe(this, Observer { workInfo: WorkInfo? ->
                if (workInfo != null && workInfo.state == WorkInfo.State.SUCCEEDED) {
                    val workInfoOutputData = workInfo.outputData
                    val streetName = workInfoOutputData.getString("streetName")
                    binding.etAddress.apply {
                        setText(streetName)
                        setSelection(streetName?.length ?: 0)
                    }
                }
            })
        } catch (ex: java.lang.Exception) {
            FirebaseCrashlytics.getInstance().recordException(ex)
        }
    }

    override fun onRequestPermissionsResult(requestCode: Int, permissions: Array<out String>, grantResults: IntArray) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        if (requestCode == PermissionConst.ACCESS_LOCATION) {
            val propBuilder = PropBuilder()
            if (grantResults.isNotEmpty() && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                propBuilder.put(AnalyticsConst.STATUS, AnalyticsConst.STATUS_ALLOW)
            } else {
                propBuilder.put(AnalyticsConst.STATUS, AnalyticsConst.STATUS_DENY)
            }
            AppAnalytics.trackEvent(AnalyticsConst.EVENT_LOCATION_PERMISSION_REQUEST, propBuilder)
            if (grantResults.isNotEmpty() && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                storeLocation()
            }
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        when(resultCode ){
            Activity.RESULT_OK ->{
                if (requestCode == ADDRESS_FLOW_REQUEST_CODE){
                    val merchantAddress = data?.getParcelableExtra<Address>(BusinessAddressActivity.ADDRESS) ?: return
                    address = merchantAddress
                    binding.etAddress.setText(Utility.getCompleteAddress(address))
                }
            }
            else ->{}
        }
    }

    companion object{
        private const val ADDRESS_FLOW_REQUEST_CODE = 1133
    }

}