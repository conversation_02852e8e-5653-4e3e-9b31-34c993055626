package com.bukuwarung.activities.card

import android.content.ActivityNotFoundException
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.os.Environment
import android.os.Parcelable
import android.view.View
import android.widget.TextView
import com.bukuwarung.R
import com.bukuwarung.activities.card.newcard.BusinessCardDesign
import com.bukuwarung.activities.card.newcard.NewBusinessCardActivity
import com.bukuwarung.activities.card.preview.LayoutToImageHandler
import com.bukuwarung.activities.superclasses.AppActivity
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.analytics.AppAnalytics.PropBuilder
import com.bukuwarung.analytics.SurvicateAnalytics
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.constants.PermissionConst
import com.bukuwarung.database.entity.BookEntity
import com.bukuwarung.preference.FeaturePrefManager
import com.bukuwarung.session.User
import com.bukuwarung.utils.*
import com.bukuwarung.databinding.ActivityBusinessCardShareBinding
import com.google.android.gms.tasks.TaskExecutors
import com.google.android.material.snackbar.Snackbar
import java.io.File

class BusinessCardShareActivity : AppActivity () {

    private lateinit var binding: ActivityBusinessCardShareBinding
    lateinit var bookEntity: BookEntity
    private var isNewBusinessCardEnabled = RemoteConfigUtils.NewBusinessCard.isEnabled()
    private var from: String? = null

    override fun onCreate(bundle: Bundle?) {
        super.onCreate(bundle)

        binding = ActivityBusinessCardShareBinding.inflate(layoutInflater)
        setContentView(binding.root)
        binding.newBusinessCardPreviewShare.visibility = isNewBusinessCardEnabled.asVisibility()
        binding.businessCardPreviewShare.root.visibility = (!isNewBusinessCardEnabled).asVisibility()
        bookEntity = businessRepository.getBusinessByIdSync(User.getBusinessId())

        if (isNewBusinessCardEnabled) {
            renderNewDesignBusinessCard()
        } else {
            renderOldDesignBusinessCard()
        }

        setShareOptions()

        checkForWhatsappInstagram()

        binding.btnClose.setOnClickListener {
            super.onBackPressed()
        }

        binding.editBusinessCard.setOnClickListener {

            AppAnalytics.trackEvent(AnalyticsConst.EVENT_EDIT_BUSINESS_CARD)

            val isNewBusinessCardEnabled = RemoteConfigUtils.NewBusinessCard.isEnabled()
            val clazz = if (isNewBusinessCardEnabled) NewBusinessCardActivity::class.java else BusinessCardActivity::class.java
            startActivity(Intent(this, clazz))
            finish()
        }

        if (intent.hasExtra("from")) {
            from = intent.getStringExtra("from")
        }

        binding.llDownload.setOnClickListener {
            if (!PermissonUtil.hasStoragePermission() && Build.VERSION.SDK_INT >= 23) {
                requestPermissions(PermissionConst.WRITE_EXTERNAL_STORAGE_PERMISSION_STR, PermissionConst.WRITE_EXTERNAL_STORAGE)
            } else {
                downloadBusinessCard()
                Snackbar.make(it, "File berhasil diunduh", Snackbar.LENGTH_LONG).show()
            }
        }
    }


    private fun downloadBusinessCard() {
        ImageUtils.saveLayoutConvertedImage(if (isNewBusinessCardEnabled) binding.newBusinessCardPreviewShare else binding.businessCardPreviewShare.root, true)

        val downloadDirectory = File(Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS), "business_card.png")

        val path = Uri.fromFile(downloadDirectory)
        val imageOpenIntent = Intent(Intent.ACTION_VIEW)
        imageOpenIntent.flags = Intent.FLAG_ACTIVITY_CLEAR_TOP
        imageOpenIntent.setDataAndType(path, "image/*")
        try {
            this.startActivity(imageOpenIntent)
        } catch (e: ActivityNotFoundException) {
        }
    }

    private fun renderOldDesignBusinessCard() {
        if (Utility.isBlank(bookEntity.businessOwnerName)) {
            binding.businessCardPreviewShare.nameLayout.visibility = View.GONE
        }
        if (Utility.isBlank(bookEntity.businessPhone)) {
            binding.businessCardPreviewShare.phoneLayout.visibility = View.GONE
        }
        if (Utility.isBlank(bookEntity.businessEmail)) {
            binding.businessCardPreviewShare.emailLayout.visibility = View.GONE
        }
        if (Utility.isBlank(bookEntity.businessAddress)) {
            binding.businessCardPreviewShare.addrLayout.visibility = View.GONE
        }

        binding.businessCardPreviewShare.businessCardCanvas.setBackgroundResource(FeaturePrefManager.getInstance().cardColor)

        setTextView("Hi, " + bookEntity.businessOwnerName, "", binding.tvShareName)
        setTextView(bookEntity.businessName, getString(R.string.business_card_shop_name), binding.businessCardPreviewShare.name)
        setTextView(Utility.beautifyPhoneNumber(bookEntity.businessPhone), getString(R.string.phone_label), binding.businessCardPreviewShare.phone)
        if (!bookEntity.businessTagLine.isNullOrEmpty()) {
            setTextView(bookEntity.businessTagLine, "", binding.businessCardPreviewShare.caption)
        }
        setTextView(bookEntity.businessOwnerName, getString(R.string.owner_name_label), binding.businessCardPreviewShare.owner)
        setTextView(bookEntity.businessEmail, getString(R.string.email_label), binding.businessCardPreviewShare.email)
        setTextView(bookEntity.businessAddress, getString(R.string.address_label), binding.businessCardPreviewShare.address)
    }

    private fun renderNewDesignBusinessCard() {
        setTextView("Hi, " + bookEntity.businessOwnerName, "", binding.tvShareName)
        val design = intent.getSerializableExtra(NEW_CARD_DESIGN) as BusinessCardDesign
        binding.newBusinessCardPreviewShare.setData(bookEntity, design)
    }

    private fun setTextView(value: String?, emptyMsg: String, targetTv: TextView) {
        if (!Utility.isBlank(value)) {
            targetTv.text = value
        } else {
            targetTv.text = emptyMsg
        }
    }

    private fun setShareOptions() {
        var view = binding.shareInstagram
        val layout = if (isNewBusinessCardEnabled) binding.newBusinessCardPreviewShare else binding.businessCardPreviewShare.root

        with(view) {
            ivShare.setImageResource(R.drawable.instagram_icon)
//            ivShare.setImageResource(R.drawable.insta)
            tvShare.text = "Instagram"

            ivShare.setOnClickListener {
                val propBuilder = PropBuilder()
                propBuilder.put(AnalyticsConst.SHARED_VIA, AnalyticsConst.INSTAGRAM)
                if (from.isNotNullOrEmpty()) {
                    propBuilder.put(AnalyticsConst.ENTRY_POINT, AnalyticsConst.HomePage.HOMEPAGE)
                }

                AppAnalytics.trackEvent(AnalyticsConst.EVENT_SHARE_FROM, propBuilder)
                ImageUtils.saveLayoutConvertedImage(layout, false).continueWith(TaskExecutors.MAIN_THREAD,
                        LayoutToImageHandler(this@BusinessCardShareActivity, "", null, false, true))
            }
        }

        view = binding.shareWhatsapp

        with(view) {
            ivShare.setBackgroundResource(R.drawable.whatsapp_icon)
//            ivShare.setImageResource(R.mipmap.ic_whatsapp_white_24dp)
            tvShare.text = getString(R.string.entries_whatsapp)

            ivShare.setOnClickListener {
                val propBuilder = PropBuilder()
                propBuilder.put(AnalyticsConst.SHARED_VIA, AnalyticsConst.WHATSAPP)

                if (from.isNotNullOrEmpty()) {
                    propBuilder.put(AnalyticsConst.ENTRY_POINT, AnalyticsConst.HomePage.HOMEPAGE)
                }
                AppAnalytics.trackEvent(AnalyticsConst.EVENT_SHARE_FROM, propBuilder)

                ImageUtils.saveLayoutConvertedImage(layout, false).continueWith(TaskExecutors.MAIN_THREAD,
                        LayoutToImageHandler(this@BusinessCardShareActivity, "com.whatsapp", null, true, false))
            }
        }

        view = binding.shareNormal

        with(view) {
            ivShare.setBackgroundResource(R.drawable.other_share_icon)
//            ivShare.setImageResource(R.drawable.share_white)
            tvShare.text = getString(R.string.tab_others_label)

            ivShare.setOnClickListener {
                val propBuilder = PropBuilder()
                propBuilder.put(AnalyticsConst.SHARED_VIA, AnalyticsConst.OTHER)

                if (from.isNotNullOrEmpty()) {
                    propBuilder.put(AnalyticsConst.ENTRY_POINT, AnalyticsConst.HomePage.HOMEPAGE)
                }

                AppAnalytics.trackEvent(AnalyticsConst.EVENT_SHARE_FROM, propBuilder)
                SurvicateAnalytics.invokeEventTracker(AnalyticsConst.EVENT_BUSINESS_CARD_SHARE_PREVIEW, this@BusinessCardShareActivity)
                ImageUtils.saveLayoutConvertedImage(layout, false).continueWith(TaskExecutors.MAIN_THREAD,
                        LayoutToImageHandler(this@BusinessCardShareActivity, "", null, false, false))
            }
        }
    }

    override fun onResume() {
        super.onResume()
        SurvicateAnalytics.invokeEventTracker(AnalyticsConst.EVENT_SAVE_BUSINESS_CARD, this)
        if (intent.hasExtra("from")) {
            from = intent.getStringExtra("from")
        }

    }

    private fun checkForWhatsappInstagram() {
        val packageManager = this.packageManager
        var isInstalled = ShareUtils.isPackageInstalled("com.whatsapp", packageManager)

        if (!isInstalled) {
            binding.shareWhatsapp.root.visibility = View.GONE
        }

        isInstalled = ShareUtils.isPackageInstalled("com.instagram.android", packageManager)
        if (!isInstalled) {
            binding.shareInstagram.root.visibility = View.GONE
        }
    }

    override fun onRequestPermissionsResult(requestCode: Int, permissions: Array<out String>, grantResults: IntArray) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        when (requestCode) {
            PermissionConst.WRITE_EXTERNAL_STORAGE -> {
                if (grantResults.isNotEmpty() && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                    downloadBusinessCard()
                }
            }
        }
    }

    companion object{
        const val NEW_CARD_DESIGN = "NEW_CARD_DESIGN"
        const val FROM = "from"
    }
}