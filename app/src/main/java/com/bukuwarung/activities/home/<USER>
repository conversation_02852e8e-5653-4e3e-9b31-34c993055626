package com.bukuwarung.activities.home

import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.bukuwarung.activities.referral.payment_referral.ReferralActivity
import com.bukuwarung.databinding.BottomSheetReferralErrorBinding
import com.bukuwarung.dialogs.base.BaseBottomSheetDialogFragment
import com.bukuwarung.payments.pref.PaymentPrefManager
import java.util.*

class ReferralErrorBottomSheet : BaseBottomSheetDialogFragment() {

    companion object {
        const val TAG = "referral_error_bottom_sheet"
    }

    private var _binding: BottomSheetReferralErrorBinding? = null
    private val binding get() = _binding!!

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = BottomSheetReferralErrorBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        PaymentPrefManager.getInstance().setKybBottomSheetShownDate(Date())
        binding.btnNext.setOnClickListener {
            startActivity(Intent(context, ReferralActivity::class.java))
            dismiss()
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }

}
