package com.bukuwarung.activities.pos

import android.annotation.SuppressLint
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.activityViewModels
import androidx.lifecycle.Observer
import androidx.recyclerview.widget.LinearLayoutManager
import com.bukuwarung.R
import com.bukuwarung.activities.pos.adapter.PosCartAdapter
import com.bukuwarung.activities.pos.experiments.PosPaymentWalletFragment
import com.bukuwarung.activities.pos.model.PosProduct
import com.bukuwarung.activities.pos.viewmodel.PosClickAction
import com.bukuwarung.activities.pos.viewmodel.PosClickEvent
import com.bukuwarung.activities.pos.viewmodel.PosViewModel
import com.bukuwarung.activities.superclasses.BaseFragment
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.databinding.FragmentPosCartBinding
import com.bukuwarung.dialogs.GenericConfirmationDialog
import com.bukuwarung.utils.RemoteConfigUtils
import com.bukuwarung.utils.Utilities
import com.bukuwarung.utils.subscribeSingleLiveEvent
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class PosCartFragment : BaseFragment() {

    private val viewModel: PosViewModel by activityViewModels()

    /**
     * Remote config
     */
    private var isPosNonCashTaggingEnabled = RemoteConfigUtils.PosExperiments.isNonCashTaggingEnabled()

    private lateinit var binding: FragmentPosCartBinding
    private val adapter: PosCartAdapter by lazy {
        PosCartAdapter {
            trackProductUpdateDetailsOpenEvent()
            showEditBottomSheet(it)
        }
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View {
        binding = FragmentPosCartBinding.inflate(inflater, container, false)
        return binding.root
    }


    override fun setupView(view: View) {
        val layoutManager = object : LinearLayoutManager(requireContext()){
            override fun canScrollVertically(): Boolean {
                return false
            }
        }

        binding.stockUnitRecyclerView.layoutManager = layoutManager
        binding.stockUnitRecyclerView.adapter = adapter

        binding.btnSave.setOnClickListener {
            trackCartCheckoutEvent()

            if (binding.btnSave.isEnabled) {
                val fragmentTransaction = activity?.supportFragmentManager?.beginTransaction()
                fragmentTransaction?.let {
                    when {
                        isPosNonCashTaggingEnabled -> {
                            it.replace(R.id.pos_fragment_container, PosPaymentWalletFragment())
                        }
                        else -> {
                            it.replace(R.id.pos_fragment_container, PosPaymentFragment())
                        }
                    }
                    it.addToBackStack(null)
                    it.commit()
                }
            }
        }

        binding.tvEmptyCart.setOnClickListener {
            showEmptyCartConfirmationDialog()
        }

        binding.closeBtn.setOnClickListener {
            activity?.onBackPressed()
        }
    }

    @SuppressLint("SetTextI18n")
    override fun subscribeState() {
        subscribeSingleLiveEvent(viewModel.state) {
            when (it) {
                // subscribe to PosViewModel state changes here
                else -> {}
            }
        }

        viewModel.selectedProduct.observe(this, Observer {
            if (it.isEmpty()) {
                activity?.onBackPressed()
            }
            adapter.updateData(it)
            binding.tvSubtotalAmount.text = Utilities.getLocaleFormattedPrice(viewModel.getSelectedProductTotalPrice())
            binding.tvProductPriceSummary.text = Utilities.getLocaleFormattedPrice(viewModel.getSelectedProductTotalPrice())
        })
    }

    private fun showEmptyCartConfirmationDialog() {
        context?.let {
            GenericConfirmationDialog.create(it) {
                titleRes = R.string.delete_order
                bodyRes = R.string.all_items_customer_wants_to_buy_will_be_deleted
                btnLeftRes = R.string.delete
                btnRightRes = R.string.cancel
                rightBtnCallback = null
                leftBtnCallback = {
                    clearSelectedProductsEvent()
                    viewModel.onEventReceived(PosViewModel.Event.OnClearSelectedProducts)
                    activity?.onBackPressed()
                }
            }.show()
        }
    }

    private fun showEditBottomSheet(posProduct: PosProduct){
        val bottomSheet = EditPosStockBottomSheetFragment(posClickEvent = PosClickEvent(posProduct, PosClickAction.EDIT), isCartMode = true) {
            it ?: return@EditPosStockBottomSheetFragment
            viewModel.onEventReceived(PosViewModel.Event.OnProductClicked(it))
        }
        bottomSheet.show(parentFragmentManager, "PosCartFragment")
    }

    // Events

    private fun clearSelectedProductsEvent() {
        // Refactor this and get values in subscribeState CartCleared event
        val totalProductQty = viewModel.getSelectedProductTotalQty()
        val uniqueProductCount = viewModel.getSelectedUniqueProductCount()
        val totalAmountToPay = viewModel.getSelectedProductTotalPrice()

        val propBuilder = AppAnalytics.PropBuilder()
        propBuilder
            .put(AnalyticsConst.TOTAL_AMOUNT, totalAmountToPay)
            .put(AnalyticsConst.UNIQUE_PRODUCT_COUNT, uniqueProductCount)
            .put(AnalyticsConst.TOTAL_PRODUCT_QTY, totalProductQty)

        AppAnalytics.trackEvent(AnalyticsConst.POS_CLEAR_CART, propBuilder)
    }

    private fun trackCartCheckoutEvent() {
        // Refactor this and get values in subscribeState CartCheckOut event
        val totalProductQty = viewModel.getSelectedProductTotalQty()
        val uniqueProductCount = viewModel.getSelectedUniqueProductCount()
        val totalAmountToPay = viewModel.getSelectedProductTotalPrice()
        val totalProductBuyingPrice = viewModel.getSelectedProductTotalBuyingPrice()

        val propBuilder = AppAnalytics.PropBuilder()
        propBuilder
            .put(AnalyticsConst.TOTAL_AMOUNT, totalAmountToPay)
            .put(AnalyticsConst.UNIQUE_PRODUCT_COUNT, uniqueProductCount)
            .put(AnalyticsConst.TOTAL_PRODUCT_QTY, totalProductQty)
            .put(AnalyticsConst.ENTRY_POINT2, AnalyticsConst.POS_CART)
            .put(AnalyticsConst.SUM_PRODUCT_BUYING_PRICE, totalProductBuyingPrice)

        AppAnalytics.trackEvent(AnalyticsConst.POS_CART_CHECKOUT, propBuilder)
    }

    private fun trackProductUpdateDetailsOpenEvent() {
        val propBuilder = AppAnalytics.PropBuilder()
        propBuilder
            .put(AnalyticsConst.PRODUCT_NAME, AnalyticsConst.CART)
        AppAnalytics.trackEvent(AnalyticsConst.POS_UPDATE_PRODUCT_DETAILS_OPEN, propBuilder)
    }

    companion object {
        fun getInstance() = PosCartFragment()
    }
}