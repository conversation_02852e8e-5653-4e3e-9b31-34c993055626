package com.bukuwarung.activities.pos

import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.core.widget.doAfterTextChanged
import androidx.fragment.app.activityViewModels
import com.bukuwarung.R
import com.bukuwarung.activities.expense.detail.CashTransactionDetailActivity
import com.bukuwarung.activities.pos.viewmodel.PosViewModel
import com.bukuwarung.activities.superclasses.BaseFragment
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.database.repository.TransactionRepository
import com.bukuwarung.databinding.FragmentPosPaymentBinding
import com.bukuwarung.utils.*
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class PosPaymentFragment : BaseFragment() {
    private lateinit var binding: FragmentPosPaymentBinding

    private val viewModel: PosViewModel by activityViewModels()

    private var totalAmountToPay: Double = 0.0
    private var totalBuyingPrice: Double = 0.0
    private var inputtedAmount: Double = 0.0
    private var change: Double = 0.0
    private var entryPoint: String = ""
    private var trxType: String = ""


    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = FragmentPosPaymentBinding.inflate(inflater, container, false)
        initKeyboard()

        return binding.root
    }

    override fun setupView(view: View) {
        totalAmountToPay = viewModel.getSelectedProductTotalPrice()
        totalBuyingPrice = viewModel.getSelectedProductTotalBuyingPrice()

        binding.btnReceiveMoney.setOnClickListener {
            trxType = EXACT_AMOUNT
            closeCalculatorKeyboard()
            entryPoint = AnalyticsConst.PAY_EXACT_AMOUNT_BUTTON
            binding.tvTrxSuccess.text = RemoteConfigUtils.getTrxSuccessMessage()
            InputUtils.hideKeyBoardWithCheck(activity)
            viewModel.onEventReceived(
                PosViewModel.Event.OnTransactionSave(
                    totalAmountToPay,
                    0.0,
                    totalBuyingPrice
                )
            )
        }

        binding.closeBtn.setOnClickListener {
            activity?.onBackPressed()
        }

        binding.sellingPriceEdit.requestFocus()
        setKeyboardSimpan(false)
        binding.sellingPriceEdit.doAfterTextChanged {
            inputtedAmount = Utility.extractAmountFromText(it.toString())
            change = totalAmountToPay - inputtedAmount

            val isAmountSufficient = inputtedAmount >= totalAmountToPay
            binding.llChangeToGive.visibility = isAmountSufficient.asVisibility()
            setKeyboardSimpan(isAmountSufficient)

            val changeToGive = "Rp${Utility.formatCurrency(change)}"
            binding.tvChangeToGive.text = changeToGive
        }

        val totalPriceStr = "Rp${Utility.formatCurrency(totalAmountToPay)}"
        binding.tvTotalAmount.text = totalPriceStr
    }


    private fun setKeyboardSimpan(isSet: Boolean) {
        if (isSet) {
            binding.keyboardView.submitBtn.isClickable = true
            binding.keyboardView.submitBtn.setBackgroundColor(
                ContextCompat.getColor(
                    requireContext(),
                    R.color.buku_CTA
                )
            )
        } else {
            binding.keyboardView.submitBtn.isClickable = false
            binding.keyboardView.submitBtn.setBackgroundColor(
                ContextCompat.getColor(
                    requireContext(),
                    R.color.black_2
                )
            )
        }
    }

    override fun subscribeState() {
        subscribeSingleLiveEvent(viewModel.state) {
            when (it) {
                is PosViewModel.State.OnTransactionSaved -> {
                    binding.lavSuccess.showForOnce(binding.successView, 75) {
                        it.trxId?.let { trxId ->
                            trackPosPaymentCompleteEvent(
                                trxId = trxId,
                                uniqueProductCount = it.uniqueProductCount,
                                totalProductQty = it.totalProductQty
                            )
                        }

                        val detailIntent = Intent(
                            requireContext(),
                            CashTransactionDetailActivity::class.java
                        ).apply {
                            putExtra(CashTransactionDetailActivity.TRX_ID_PARAM, it.trxId)
                            putExtra(CashTransactionDetailActivity.IS_EXPENSE_PARAM, false)
                            putExtra("isRedirectedFromCashTransaction", true)
                            putExtra(
                                CashTransactionDetailActivity.IS_REDIRECTED_FROM_POS_MODE,
                                true
                            )
                            putExtra("changeAmount", change)
                        }

                        val posTrxCount =
                            TransactionRepository.getInstance(activity).posTransactionCountWithDeletedRecords
                        /**
                         * track first pos transaction
                         */

                        startActivity(detailIntent)
                        requireActivity().finish()
                    }
                }
                else -> {}
            }
        }
    }

    private fun initKeyboard() {
        binding.keyboardView.visibility = View.VISIBLE
        binding.keyboardView.setResultTv(binding.sellingPriceEdit)
        binding.keyboardView.setFromPos()
        binding.keyboardView.cursor = binding.cursor
        binding.keyboardView.setCurrency(binding.currencySymbol)
        binding.keyboardView.setExprTv(binding.textAmountCalc)
        binding.keyboardView.setResultLayout(binding.resultLayout)
        binding.keyboardView.showCursor()

        binding.keyboardView.setOnSubmitListener {
            trxType = CUSTOM_AMOUNT
            closeCalculatorKeyboard()
            entryPoint = AnalyticsConst.SAVE_TRANSACTION_BUTTON
            binding.tvTrxSuccess.text = RemoteConfigUtils.getTrxSuccessMessage()
            InputUtils.hideKeyBoardWithCheck(activity)
            viewModel.onEventReceived(
                PosViewModel.Event.OnTransactionSave(
                    totalAmountToPay,
                    inputtedAmount,
                    totalBuyingPrice
                )
            )
        }
    }

    private fun trackPosPaymentCompleteEvent(
        trxId: String,
        uniqueProductCount: Int,
        totalProductQty: Double
    ) {
        var changeAmount = inputtedAmount - totalAmountToPay
        var exactAmount = calculateExactAmountType(changeAmount)

        // if user presses btnReceiveMoney, changeAmount and inputtedAmount should be 0
        if (trxType == EXACT_AMOUNT) {
            changeAmount = 0.0
            exactAmount = AnalyticsConst.EXACT_AMOUNT
            inputtedAmount = 0.0
        }

        val propBuilder = AppAnalytics.PropBuilder()
        propBuilder
            .put(AnalyticsConst.MODE_OF_PAYMENT, AnalyticsConst.CASH)
            .put(AnalyticsConst.EXACT_AMOUNT, exactAmount)
            .put(AnalyticsConst.TRANSACTION_ID, trxId)
            .put(AnalyticsConst.TOTAL_AMOUNT, totalAmountToPay)
            .put(AnalyticsConst.CUSTOM_AMOUNT, inputtedAmount)
            .put(AnalyticsConst.CHANGE_AMOUNT, changeAmount)
            .put(AnalyticsConst.UNIQUE_PRODUCT_COUNT, uniqueProductCount)
            .put(AnalyticsConst.TOTAL_PRODUCT_QTY, totalProductQty)
            .put(AnalyticsConst.ENTRY_POINT2, entryPoint)
            .put(AnalyticsConst.SUM_PRODUCT_BUYING_PRICE, viewModel.getSelectedProductTotalBuyingPrice())
            .put(AnalyticsConst.NOTA_STANDARD_ENABLED, RemoteConfigUtils.shouldShowNewPosInvoice())

        if (PosActivity.from.isNotNullOrEmpty()) {
            propBuilder.put(AnalyticsConst.ENTRY_POINT, AnalyticsConst.HomePage.HOMEPAGE)
        }

        AppAnalytics.trackEvent(AnalyticsConst.POS_PAYMENT_COMPLETE, propBuilder)
    }

    private fun calculateExactAmountType(changeAmount: Double): String {
        return when {
            changeAmount < 0.0 -> {
                AnalyticsConst.LESS
            }
            changeAmount == 0.0 -> {
                AnalyticsConst.EXACT_AMOUNT
            }
            else -> {
                AnalyticsConst.MORE
            }
        }
    }

    private fun closeCalculatorKeyboard() {
        if (requireActivity().isAnimEnabled()) binding.keyboardView.clearAnimation()
        binding.keyboardView.visibility = View.GONE
        binding.keyboardView.cursor = binding.cursor
        binding.keyboardView.hideCursor()
    }

    companion object {
        const val EXACT_AMOUNT = "exact_amount"
        const val CUSTOM_AMOUNT = "custom_amount"

        fun getInstance() = PosPaymentFragment()
    }
}