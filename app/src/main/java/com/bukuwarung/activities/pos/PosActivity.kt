package com.bukuwarung.activities.pos

import android.app.PendingIntent
import android.content.Intent
import android.os.Build
import androidx.activity.viewModels
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import com.bukuwarung.Application
import com.bukuwarung.R
import com.bukuwarung.activities.pos.helpers.IOnBackPressed
import com.bukuwarung.activities.pos.viewmodel.PosViewModel
import com.bukuwarung.activities.stickers.StickerMainActivity
import com.bukuwarung.activities.stickers.StickerPackDetailsActivity
import com.bukuwarung.activities.superclasses.BaseActivity
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.database.repository.TransactionRepository
import com.bukuwarung.databinding.ActivityPosBinding
import dagger.hilt.android.AndroidEntryPoint
import com.bukuwarung.dialogs.GenericConfirmationDialog
import com.bukuwarung.managers.local_notification.LocalNotificationData
import com.bukuwarung.managers.local_notification.LocalNotificationIcon
import com.bukuwarung.managers.local_notification.LocalNotificationManager
import com.bukuwarung.managers.local_notification.LocalNotificationManager.Companion.showDefaultNotification
import com.bukuwarung.managers.local_notification.LocalNotificationStyle
import com.bukuwarung.preference.FeaturePrefManager
import java.util.*

@AndroidEntryPoint
class PosActivity : BaseActivity() {

    private lateinit var binding: ActivityPosBinding

    private val viewModel: PosViewModel by viewModels()

    lateinit var posStoreFrontFragment: PosStoreFrontFragment

    private lateinit var currentFragment: Fragment

    companion object {
        var from: String? = null
    }

    override fun setViewBinding() {
        binding = ActivityPosBinding.inflate(layoutInflater)
        setContentView(binding.root)
    }

    override fun setupView() {
        posStoreFrontFragment = PosStoreFrontFragment.getInstance()

        supportFragmentManager.beginTransaction()
            .replace(R.id.pos_fragment_container, posStoreFrontFragment).commit()

        currentFragment = posStoreFrontFragment

        supportFragmentManager.addOnBackStackChangedListener {
            val f = supportFragmentManager.fragments
            val frag = f[0]
            currentFragment = frag
        }
    }

    override fun subscribeState() {
        // subscribe to PosViewModel state changes here
    }

    override fun onBackPressed() {
        if (currentFragment is PosStoreFrontFragment) {
            (currentFragment as? IOnBackPressed)?.onBackPressed()?.let {
                if (it) {
                    showExitDialog()
                } else {
                    super.onBackPressed()
                }
            }
        } else {
            super.onBackPressed()
        }
    }

    fun showExitDialog() {
        GenericConfirmationDialog.create(this) {
            titleRes = R.string.exit_cashier_mode_question
            bodyRes = R.string.items_in_cashier_mode_will_be_deleted_after_exit
            btnLeftRes = R.string.yes_exit
            btnRightRes = R.string.cancel
            rightBtnCallback = {}
            leftBtnCallback = {
                trackPosExitEvent()
                checkAndCreateNotification()
                finish()
            }
        }.show()
    }



    override fun onResume() {
        super.onResume()

        if (intent.hasExtra("from")) {
            from = intent.getStringExtra("from")
        }
    }

    // Local Notification on back press
    private fun checkAndCreateNotification() {
        val posTransactionCount = TransactionRepository.getInstance(this).posTransactionCountWithDeletedRecords
        val localNotificationData = LocalNotificationData(
            resources.getString(R.string.notification_pos_title),
            resources.getString(R.string.notification_pos_body),
            LocalNotificationIcon.DEFAULT
        )
        val random = Random()
        val randomNumber = random.nextInt(1000)
        val intent = Intent(this, StickerMainActivity::class.java)
        intent.putExtra(StickerPackDetailsActivity.EXTRA_POS_TARGET, true)
        val pendingIntent = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            PendingIntent.getActivities(this, randomNumber, arrayOf(intent), PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE)
        } else {
            PendingIntent.getActivities(this, randomNumber, arrayOf(intent), PendingIntent.FLAG_UPDATE_CURRENT)
        }
        if (posTransactionCount >= 15 && !FeaturePrefManager.getInstance().isPosNotificationSent) {
            showDefaultNotification(
                Application.getAppContext(),
                localNotificationData,
                LocalNotificationStyle.BIG_TEXT,
                pendingIntent, LocalNotificationManager.NOTIFICATION_CHANNEL_TITLE
            )
            FeaturePrefManager.getInstance().setPosNotificationShown()
        }
    }

    /**
     * Events
     */
    private fun trackPosExitEvent() {
        val uniqueProductCount = viewModel.getSelectedUniqueProductCount()

        val propBuilder = AppAnalytics.PropBuilder()
        propBuilder
            .put(AnalyticsConst.UNIQUE_PRODUCT_COUNT, uniqueProductCount)

        AppAnalytics.trackEvent(AnalyticsConst.POS_EXIT, propBuilder)
    }
}
