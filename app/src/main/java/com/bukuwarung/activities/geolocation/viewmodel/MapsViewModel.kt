package com.bukuwarung.activities.geolocation.viewmodel

import android.app.Application
import android.location.Geocoder
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.bukuwarung.activities.BaseAndroidViewModel
import com.bukuwarung.activities.geolocation.data.model.Address
import com.bukuwarung.constants.AppConst.EMPTY_STRING
import com.google.android.gms.maps.model.LatLng
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.lang.Exception
import java.util.*
import javax.inject.Inject

@HiltViewModel
class MapsViewModel @Inject constructor(application: Application) :
    BaseAndroidViewModel(application) {

    private val eventStatus = MutableLiveData<Event>()
    val observeEvent: LiveData<Event> = eventStatus

    sealed class Event {
        data class AddressFound(val address: Address) : Event()
        data class AddressError(val exception: Exception) : Event()
    }

    private suspend fun setEventStatus(event: Event) = withContext(Dispatchers.Main) {
        eventStatus.value = event
    }

    fun geocodeAddress(latLng: LatLng) = viewModelScope.launch {
        withContext(Dispatchers.IO) {
            try {
                val currentLocation =
                    Geocoder(getApplication<Application>().applicationContext, Locale.getDefault())
                        .getFromLocation(latLng.latitude, latLng.longitude, 1)?.get(0)
                val address = Address(
                    id = EMPTY_STRING,
                    name = EMPTY_STRING,
                    fullAddress = currentLocation?.getAddressLine(0),
                    latitude = currentLocation?.latitude,
                    longitude = currentLocation?.longitude,
                    province = currentLocation?.adminArea.orEmpty(),
                    city = currentLocation?.subAdminArea.orEmpty(),
                    district = currentLocation?.locality.orEmpty(),
                    subDistrict = currentLocation?.subLocality.orEmpty(),
                    postalCode = currentLocation?.postalCode.orEmpty()
                )
                setEventStatus(Event.AddressFound(address))
            } catch (exception: Exception) {
                setEventStatus(Event.AddressError(exception))
            }
        }
    }

}