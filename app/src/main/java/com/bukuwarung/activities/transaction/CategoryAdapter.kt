package com.bukuwarung.activities.transaction

import android.annotation.SuppressLint
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.bukuwarung.R
import com.bukuwarung.activities.expense.data.Category
import com.bukuwarung.database.entity.MeasurementEntity
import com.bukuwarung.databinding.CategoryItemBinding

class CategoryAdapter(private val categoryList: ArrayList<Category>, val click: (id: String) -> Unit) : RecyclerView.Adapter<RecyclerView.ViewHolder>() {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        val binding = CategoryItemBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return CategoryViewHolder(binding, click)
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        return (holder as CategoryViewHolder).bind(categoryList[position])
    }

    override fun getItemCount(): Int {
        return categoryList.size
    }

    class CategoryViewHolder(
        private val binding: CategoryItemBinding,
        val click: (id: String) -> Unit
    ) : RecyclerView.ViewHolder(binding.root) {
        @SuppressLint("SetTextI18n")
        fun bind(category : Category) {

            binding.root.setOnClickListener {
                click(category.categoryName)
            }

            binding.categoryName.text = category.categoryName

        }
    }

//    fun updateData(newcategoryList: List<MeasurementEntity>) {
//        categoryList.clear()
//        categoryList.addAll(newcategoryList)
//        notifyDataSetChanged()
//
//    }

}