package com.bukuwarung.activities.transaction.customer

import android.content.Context
import android.content.DialogInterface
import android.os.Bundle
import android.view.LayoutInflater
import android.widget.CheckBox
import androidx.viewbinding.ViewBinding
import com.bukuwarung.R
import com.bukuwarung.dialogs.base.BaseBottomSheetDialog
import com.bukuwarung.databinding.CustomerTypeDialogBinding

class CustomerTypeBottomSheet(context: Context, private val currentSelectedType: List<Int> = listOf(), private val onDialogClose: (List<Int>) -> Unit) : BaseBottomSheetDialog(context) {
    private val selectedType = mutableListOf<Int>()
    private var binding: CustomerTypeDialogBinding? = null

    init {
        setUseFullWidth(true)
        setCancellable(true)
    }

    override fun getResId(): Int = R.layout.customer_type_dialog
    override fun getBinding(): ViewBinding? {
        if (binding == null) {
            binding = CustomerTypeDialogBinding.inflate(LayoutInflater.from(context))
        }
        return binding
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setupView()
    }

    private fun setupView() {
        selectedType.addAll(currentSelectedType)
        binding?.let { b ->
            b.closeDialog.setOnClickListener {
                onDialogClose(selectedType)
                dismiss()
            }
            setupCb(b.cbReseller, RESELLER)
            setupCb(b.cbDropshipper, DROPSHIPPER)
            setupCb(b.cbPersonal, PERSONAL)
        }
    }

    private fun setupCb(checkBox: CheckBox, type: Int) {
        checkBox.apply {
            isChecked = currentSelectedType.contains(type)
            setOnCheckedChangeListener { _, isChecked ->
                if (isChecked) {
                    selectedType.add(type)
                } else {
                    selectedType.remove(type)
                }
            }
        }
    }

    override fun dismiss() {
        super.dismiss()
        onDialogClose(selectedType)
    }

    companion object {
        const val RESELLER = 1
        const val DROPSHIPPER = 2
        const val PERSONAL = 3
    }

}