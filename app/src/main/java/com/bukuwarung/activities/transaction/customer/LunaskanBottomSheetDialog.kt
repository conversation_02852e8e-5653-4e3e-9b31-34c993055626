package com.bukuwarung.activities.transaction.customer

import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.constraintlayout.widget.ConstraintLayout
import com.bukuwarung.R
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.analytics.AppAnalytics.PropBuilder
import com.bukuwarung.analytics.SurvicateAnalytics.Companion.invokeEventTracker
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.databinding.LunaskanBottomSheetBinding
import com.bukuwarung.dialogs.base.BaseBottomSheetDialogFragment
import com.bukuwarung.utils.RemoteConfigUtils
import com.bukuwarung.utils.Utility
import com.bukuwarung.utils.dp

class LunaskanBottomSheetDialog() : BaseBottomSheetDialogFragment() {
    private var _binding: LunaskanBottomSheetBinding? = null
    private val binding get() = _binding!!

    companion object {
        fun newInstance(amount: Double,customerId:String, customerName: String, listener: LunaskanSheetListener? = null): LunaskanBottomSheetDialog {
            val dialog = LunaskanBottomSheetDialog()
            dialog.listener = listener
            val bundle = Bundle()
            bundle.putDouble("amount", amount)
            bundle.putString("customerId", customerId)
            bundle.putString("customerName", customerName)
            dialog.arguments = bundle
            return dialog
        }
    }

    private var sendSmsProof = false
    private var listener: LunaskanSheetListener? = null
    private val amount by lazy {
        arguments?.getDouble("amount") ?: 0.0
    }

    private val customerId by lazy {
        arguments?.getString("customerId") ?: ""
    }

    private val customerName by lazy {
        arguments?.getString("customerName") ?: ""
    }

    interface LunaskanSheetListener {
        fun saveTransactionAfterLunaskanClick(sendSmsProof: Boolean)
    }


    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        _binding = LunaskanBottomSheetBinding.inflate(inflater, container, false)
        val view = binding.root

        invokeEventTracker(AnalyticsConst.EVENT_SETTLE_OPEN, requireActivity())

        binding.cbSendSms.visibility =
            if (RemoteConfigUtils.showShowLunaskanSMS()) View.VISIBLE else View.GONE

        binding.cbSendSms.setOnCheckedChangeListener { _, isChecked ->
            val layoutParams = binding.cbSendSms.layoutParams as ConstraintLayout.LayoutParams
            sendSmsProof = if (isChecked) {
                layoutParams.marginStart = 1.dp
                binding.cbSendSms.layoutParams = layoutParams
                binding.cbSendSms.setButtonDrawable(R.drawable.ic_checkbox_checked)
                true
            } else {
                layoutParams.marginStart = 4.dp
                binding.cbSendSms.layoutParams = layoutParams
                binding.cbSendSms.setButtonDrawable(R.drawable.ic_checkbox_not_checked)
                false
            }
        }

        binding.confirmationBtn.setOnClickListener {
            val propBuilder = PropBuilder()
            val showOldUTang = RemoteConfigUtils.shouldShowOldUtangForm();
            propBuilder.put(AnalyticsConst.REMAINING_UTANG, amount)
            propBuilder.put(AnalyticsConst.CUSTOMER_ID, customerId)
            propBuilder.put(AnalyticsConst.ENTRY_POINT2, AnalyticsConst.CUSTOMER_DETAILS)
            if (showOldUTang) {
                propBuilder.put(AnalyticsConst.UTANG_UI_VARIATION, AnalyticsConst.PRE_JUNE);
            } else {
                propBuilder.put(AnalyticsConst.UTANG_UI_VARIATION, AnalyticsConst.UPDATED_JUNE);
            }
            propBuilder.put(AnalyticsConst.SMS_CHECKBOX_ENABLED, RemoteConfigUtils.shouldSendSmsUtang())
            AppAnalytics.trackEvent(AnalyticsConst.EVENT_SETTLE_CONFIRM, propBuilder)
            invokeEventTracker(AnalyticsConst.EVENT_SETTLE_CONFIRM, requireActivity())

            val intent = Intent(context, LunaskanSuccessMessageActivity::class.java)
            intent.putExtra("customerName",customerName)
            intent.putExtra("amount",amount)
            intent.putExtra("customerId",customerId)
            val type = if (amount > 0) 0 else 1
            intent.putExtra("type", type)
            startActivity(intent)
            dialog?.dismiss()
            listener?.saveTransactionAfterLunaskanClick(sendSmsProof)
        }
        binding.closeDialog.setOnClickListener {
            dialog?.dismiss()
        }
        binding.balance.text = Utility.formatAmount(amount)
        if(amount>0){
            binding.txtMainTitle.setText(R.string.giving_label)
            binding.balance.setTextColor(getResources().getColor(R.color.out_red))
        }else{
            binding.txtMainTitle.setText(R.string.receiving_label)
            binding.balance.setTextColor(getResources().getColor(R.color.in_green))
        }

        return view
    }
}