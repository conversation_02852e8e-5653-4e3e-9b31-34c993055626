package com.bukuwarung.activities.transaction.customer

import android.content.Context
import android.os.Bundle
import android.text.Editable
import android.text.SpannableStringBuilder
import android.text.TextWatcher
import android.view.LayoutInflater
import android.view.View
import com.bukuwarung.R
import com.bukuwarung.dialogs.base.BaseDialog
import com.bukuwarung.dialogs.base.BaseDialogType
import com.bukuwarung.utils.RemoteConfigUtils
import com.bukuwarung.utils.Utility
import com.bukuwarung.utils.boldText
import com.bukuwarung.databinding.ManualReminderDialogBinding
import java.util.*

class ManualReminderDialog(context: Context, private val action: (balanceAmount: String, createPayment: Boolean) -> Unit, private val balance: Double) : BaseDialog(context, BaseDialogType.POPUP) {

    init {
        setCancellable(true)
        setUseFullWidth(false)
    }

    private lateinit var binding: ManualReminderDialogBinding

    override fun getResId(): Int = 0

    fun setAdminFee(adminFee: Double) {
        val boldText: String
        val spannableSb: SpannableStringBuilder
        if (adminFee <= 0.0) {
            boldText = context.getString(R.string.free).uppercase(Locale.getDefault())
            spannableSb =
                SpannableStringBuilder(context.getString(R.string.create_payment_checkbox_text))

        } else {
            boldText = Utility.formatAmount(adminFee)
            spannableSb = SpannableStringBuilder(context.getString(R.string.create_payment_checkbox_pay_text, boldText))
        }
        spannableSb.boldText(boldText)
        binding.createPaymentTxt.text = spannableSb
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        binding = ManualReminderDialogBinding.inflate(LayoutInflater.from(context))
        setupViewBinding(binding.root)

        binding.utangAmount.text = Utility.formatAmount(balance)

        binding.inputNominal.setText(Utility.formatAmount(balance))
        binding.amountErrorMessageTxt.text = context.getString(
            R.string.minimum_amount_error_message,
            Utility.formatAmount(RemoteConfigUtils.getMinimumPaymentAmount())
        )
        binding.inputNominal.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {
            }

            override fun onTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {
                if (binding.inputNominal.getNumberValue() >= RemoteConfigUtils.getMinimumPaymentAmount()) {
                    binding.minimumAmountErrorMessage.visibility = View.GONE
                    binding.createPaymentCheckbox.isEnabled = true
                } else {
                    binding.minimumAmountErrorMessage.visibility = View.VISIBLE
                    binding.createPaymentCheckbox.isEnabled = false
                }
            }

            override fun afterTextChanged(p0: Editable?) {
            }

        })

        binding.cancel.setOnClickListener {
            dismiss()
        }

        binding.requestMoney.setOnClickListener {
            action(binding.inputNominal.text.toString(), binding.createPaymentCheckbox.isChecked)
        }

    }
}
