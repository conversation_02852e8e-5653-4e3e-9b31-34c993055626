# Status Bar Color Fix Plan - Updated

## Issue
The Customer Support screen (Zoho chat) has a blank white status bar instead of using the primary blue color that matches the toolbar.

## Root Cause Analysis
After investigation, the issue is NOT in WebviewActivity. The Customer Support screen uses Zoho SalesIQ SDK chat interface (`ZohoSalesIQ.Chat.show()`) which is a separate activity/interface provided by the Zoho SDK. The Zoho chat interface doesn't inherit the app's status bar styling because:

1. The Zoho SalesIQ SDK (version 8.2.0-beta01) is not properly initialized with theme configuration
2. The SDK initialization code appears to be missing from Application.java
3. Without proper initialization, the Zoho chat uses default styling instead of the app's theme

## Solution
Add proper Zoho SalesIQ SDK initialization with theme configuration to ensure the chat interface uses the app's primary color for the status bar.

## Implementation Steps

### 1. Add Zoho SalesIQ Initialization to Application.java
- Import required Zoho SDK classes for initialization and theme configuration
- Add lifecycle registration for Zoho SDK (`MobilistenActivityLifecycleCallbacks.register()`)
- Initialize Zoho SalesIQ with proper configuration including theme settings
- Configure status bar color to use primary color (#0091ff)

### 2. Configure Zoho Theme Settings
- Set primary color for Zoho chat interface
- Configure status bar color specifically for the chat interface
- Ensure theme consistency with the app's design

### 3. Test the fix
- Build the app with stgDebug variant
- Navigate to Help section and open Customer Support chat (Zoho chat)
- Verify status bar color matches the toolbar in Zoho chat interface

## Files to Modify
- `app/src/main/java/com/bukuwarung/Application.java` - Add Zoho initialization with theme config
- Potentially add Zoho configuration resources if needed

## Expected Result
The status bar in the Zoho Customer Support chat interface should display the primary blue color (#0091ff) instead of white, matching the app's toolbar color.

## Technical Notes
- Zoho SalesIQ SDK 8.x uses new initialization method `ZohoSalesIQ.initialize()` instead of legacy `ZohoSalesIQ.init()`
- Requires `MobilistenActivityLifecycleCallbacks.register()` for proper lifecycle management
- Theme configuration should be done during SDK initialization
- The chat interface is launched via `ZohoSalesIQ.Chat.show()` in HelpDialog.kt
- `colorPrimary` is defined as `#0091ff` in `app/src/main/res/values/colors.xml`
